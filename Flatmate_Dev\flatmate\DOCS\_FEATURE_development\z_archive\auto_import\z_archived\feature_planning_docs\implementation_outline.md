# Implementation Outline: Auto-Import MVP

This document outlines the technical implementation plan for the Minimum Viable Product (MVP) of the auto-import folder feature. The focus is on creating a robust, background-service-based solution using `watchdog` for file system monitoring.

--- 

## 1. Core Components

### a. `AutoImportManager` Service
- **Location**: `fm/core/services/auto_import_manager.py`
- **Purpose**: A new singleton service responsible for managing the file monitoring process.
- **Key Responsibilities**:
    - Read auto-import configuration (enabled, import path, archive path, etc.) from the application settings.
    - Start and stop the `watchdog` file system observer.
    - Contain the event handler logic that is triggered when a new file is detected.
    - Be initialised and started by the main application process.

### b. `AutoImportEventHandler`
- **Location**: Defined within `auto_import_manager.py`.
- **Purpose**: A `watchdog.events.FileSystemEventHandler` subclass to handle file system events.
- **Logic**:
    - Implement the `on_created` method.
    - When triggered, it will check if the new file is a supported type (initially, just `.csv`).
    - It will add the file path to a queue for processing to avoid doing heavy work directly in the event handler.

## 2. Processing and Integration

### a. Processing Queue
- A simple `queue.Queue` will be used within the `AutoImportManager` to decouple file detection from file processing.
- The `AutoImportManager` will have a worker thread that processes files from this queue.

### b. Integration with `dw_director`
- For each file from the queue, the `AutoImportManager`'s worker thread will call the existing `dw_director.run_scan()` method (or a similar public API method).
- `dw_director` already contains the logic to find the correct handler and process the file. This reuses existing, tested code.

### c. File Handling (Post-Processing)
- Based on the result from `dw_director`:
    - **On Success**: Move the processed file to the configured `archive` sub-directory.
    - **On Failure**: Move the file to the configured `failed_imports` sub-directory.
- This logic will reside within the `AutoImportManager`'s worker thread.

## 3. Configuration

- **Location**: The main application configuration file (e.g., a `config.ini` or `settings.py`).
- **New Settings**:
    - `auto_import_enabled = [True|False]`
    - `auto_import_path = /path/to/user/folder`
    - `auto_import_archive_path = /path/to/user/folder/archive`
    - `auto_import_failed_path = /path/to/user/folder/failed`
- The `AutoImportManager` will read these settings on startup.

## 4. Application Lifecycle

- **Startup**: The main application entry point (e.g., `main.py` or `app.py`) will be responsible for:
    1. Instantiating the `AutoImportManager`.
    2. If `auto_import_enabled` is true, call `auto_import_manager.start()`.
- **Shutdown**: The main application must gracefully shut down the `AutoImportManager` by calling `auto_import_manager.stop()` to ensure the observer thread is terminated correctly.

## 5. Development Phases

1.  **Create `AutoImportManager`**: Build the basic class structure and configuration reading.
2.  **Integrate `watchdog`**: Add the `AutoImportEventHandler` and the observer logic to watch the configured directory.
3.  **Implement Processing Logic**: Add the queue, worker thread, and integrate the call to `dw_director`.
4.  **Add File Handling**: Implement the move-to-archive/failed logic.
5.  **Integrate with Main App**: Hook the manager into the application's startup and shutdown sequences.
6.  **Testing**: Create unit and integration tests.

--- 
*Updated: 2025-07-20*

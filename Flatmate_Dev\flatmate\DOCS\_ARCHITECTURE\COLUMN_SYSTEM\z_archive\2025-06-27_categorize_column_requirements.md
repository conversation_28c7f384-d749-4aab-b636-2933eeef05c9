# Categorize Module Column Requirements

*Date: 2025-06-27*

This document details how the categorize module uses the column system, focusing on column mapping, display, and specific requirements. This information is critical for the column system unification project to ensure all existing functionality is preserved.

## Overview

The categorize module provides functionality for viewing and categorizing transactions. It uses the canonical `StandardColumns` enum from `fm_standard_columns.py` as the source of truth for column names and mappings, while leveraging the `ColumnManager` and `ColumnNameService` for display and column handling.

## Key Files and Components

1. **cat_presenter.py**
   - Main presenter for the categorize module
   - Handles loading transactions from database and files
   - Uses `transactions_to_dataframe()` to convert Transaction objects to DataFrame

2. **transaction_view_panel.py**
   - UI component for displaying transactions in a table
   - Uses `ColumnNameService` for column mapping and display
   - Saves and loads column visibility preferences

3. **transaction_utils.py**
   - Provides utility functions for transaction handling
   - Wraps the core data services `convert_transactions_to_dataframe()` function

4. **column_manager.py**
   - Central service for column operations
   - Provides methods for column conversion, filtering, and display mapping
   - Contains module-specific column defaults for categorize module

## Required Columns

The categorize module requires the following columns:

1. **Core Transaction Columns**:
   - `DATE` (StandardColumns.DATE)
   - `DETAILS` (StandardColumns.DETAILS)
   - `AMOUNT` (StandardColumns.AMOUNT)
   - `ACCOUNT` (StandardColumns.ACCOUNT)

2. **Categorization-Specific Columns**:
   - `category` - For storing transaction categories
   - `tags` - For storing transaction tags
   - `notes` - For storing transaction notes

## Column Mapping and Display

The categorize module uses a sophisticated approach to column mapping:

1. **Database to Display Conversion**:
   - Uses `ColumnNameService.get_standard_column_widths()` to get standard column widths
   - Uses `ColumnNameService.get_reverse_mapping()` to convert display names back to database names

2. **Default Visible Columns**:
   - Defined in config: `'categorize.display.default_visible_columns': ['date', 'details', 'amount', 'account', 'tags', 'category']`
   - Can be overridden by user preferences stored in `'categorize.display.last_used_columns'`

3. **Editable Columns**:
   - Only the `tags` column is editable in the UI

## DataFrame Handling

1. **Transaction to DataFrame Conversion**:
   ```python
   # In transaction_utils.py
   def transactions_to_dataframe(transactions: List) -> pd.DataFrame:
       return convert_transactions_to_dataframe(
           transactions,
           ensure_columns=['category', 'tags', 'notes']  # Categorize module requirements
       )
   ```

2. **Column Ensuring**:
   - The module ensures that `category` and `tags` columns exist, creating them with empty values if missing

## User Preferences

The categorize module stores and retrieves user preferences for column visibility:

1. **Saving Preferences**:
   - Visible columns are saved to `'categorize.display.last_used_columns'` config
   - Display names are converted back to database names for storage

2. **Loading Preferences**:
   - Default columns are loaded from config
   - The table view automatically applies these preferences

## Implications for Column System Unification

For the unified column system, we must ensure:

1. **Column Mapping Preservation**:
   - The new system must maintain the ability to map between database and display names
   - The `ColumnNameService` functionality must be preserved or enhanced

2. **Module-Specific Defaults**:
   - The categorize module's default visible columns must be preserved
   - The editable column configuration must be maintained

3. **Category-Related Columns**:
   - The special handling of `category`, `tags`, and `notes` columns must be preserved
   - These columns must be properly defined in the unified system with appropriate metadata

4. **User Preference Handling**:
   - The mechanism for saving and loading user column preferences must be maintained
   - The config keys used for storing preferences should be preserved for backward compatibility

## Migration Considerations

When migrating to the unified column system:

1. **UI Component Compatibility**:
   - The `TransactionViewPanel` relies on the column system for display and configuration
   - Any changes must ensure the panel can still properly display and configure columns

2. **DataFrame Conversion**:
   - The `convert_transactions_to_dataframe` function is critical and must be preserved or enhanced
   - The ability to ensure specific columns exist must be maintained

3. **Column Metadata**:
   - The unified system should include metadata about which columns are editable
   - Column width information should be preserved and enhanced

4. **Backward Compatibility**:
   - The migration should maintain backward compatibility with existing code
   - Consider providing adapter functions or classes during the transition period

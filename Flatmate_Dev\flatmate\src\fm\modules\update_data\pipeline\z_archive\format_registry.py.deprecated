import importlib
import pkgutil
from typing import Dict, Any

class FormatRegistry:
    _formats: Dict[str, Any] = {}

    @classmethod
    def load_formats(cls, base_path='fm.modules.update_data.utils.formatting.formats'):
        """
        Dynamically load all format modules from subfolders
        """
        try:
            # Import the base formats package
            formats_package = importlib.import_module(base_path)
            
            # Iterate through all subfolders (banks)
            for _, name, _ in pkgutil.iter_modules(formats_package.__path__):
                try:
                    # Dynamically import the module
                    module = importlib.import_module(f'{base_path}.{name}')
                    
                    # Look for format definitions in the module
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        
                        # More robust checking of format definition
                        if (hasattr(attr, 'name') and 
                            hasattr(attr, 'type') and 
                            isinstance(attr.name, str) and 
                            isinstance(attr.type, str)):
                            # Use the full name as the key
                            key = f'{name}_{attr.type}'.lower()
                            cls._formats[key] = attr
                
                except ImportError as e:
                    print(f"Could not import format module {name}: {e}")
        
        except ImportError as e:
            print(f"Could not load formats: {e}")

    @classmethod
    def get_format(cls, format_name: str):
        """
        Retrieve a specific format by name
        """
        return cls._formats.get(format_name.lower())

    @classmethod
    def list_formats(cls):
        """
        List all loaded formats
        """
        return list(cls._formats.keys())

# Automatically load formats when module is imported
FormatRegistry.load_formats()

# Simple main for testing
if __name__ == "__main__":
    print("Available Formats:")
    for format_name in FormatRegistry.list_formats():
        print(f"- {format_name}")
        format_def = FormatRegistry.get_format(format_name)
        print(f"  Name: {format_def.name}")
        print(f"  Type: {format_def.type}")
        print(f"  Min Columns: {getattr(format_def, 'min_columns', 'N/A')}")
        print()
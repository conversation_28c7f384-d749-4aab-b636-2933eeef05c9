# Flatmate Application Overview
last updated : 2025-07-27 @ 03:07:34 
## Core Architecture

### Application Structure
- **Framework**: Modern Python desktop application built with **PySide6 (Qt)**.
- **Modularity**: Follows a modular architecture with a clear separation of concerns. Each functional area is a self-contained module.

### Entry Point: `main.py`
- Serves as the application's main entry point.
- Handles the initialisation of core components (e.g., logging, configuration, directories).
- Creates the main application window and starts the module coordinator.

### Module System
- **Coordinator**: `ModuleCoordinator` manages transitions and lifecycle for all application modules.
- **Modules**: Core features include `home`, `update_data` (for importing), and `categorize` (for viewing data).
- **Pattern**: Each module follows a **Presenter pattern** with its own dedicated UI components.

## Key Components

### Core Services
- **`logger.py`**: A centralised logging system provided through a singleton `log` object.
- **`event_bus.py`**: Manages event-driven communication between components.
- **`auto_import_manager.py`**: Handles the automatic import of data files.
- **`cache_service.py`**: Provides a caching mechanism for performance optimisation.

### Data Management
- **`DBIOService`**: A dedicated service for all database I/O operations, ensuring data persistence.
- **Statement Handlers**: Custom handlers for processing various financial statement formats.
- **Repositories**: Implements the repository pattern for data access (e.g., `SQLiteTransactionRepository`).

### GUI Architecture
- **`main_window.py`**: The main application window responsible for layout management.
- **Shared Components**: A library of shared components in `_shared_components` ensures a consistent UI.
- **Custom Widgets**: Reusable UI elements like `option_menus.py` are used across the application.
- **Styling**: A consistent application appearance is maintained through a dedicated styling system.

## Design Patterns

### Presenter Pattern
- Each module has a dedicated presenter (e.g., `HomePresenter`, `UpdateDataPresenter`).
- Presenters contain the UI logic and coordinate with backend services.

### Repository Pattern
- Data access is abstracted through repository interfaces.
- This design allows for swapping database backends (currently SQLite) with minimal code changes.

### Singleton Pattern
- Ensures a single instance for critical services like logging (`log`) and database access (`DBIOService`).
- Provides consistent state and access across the application.

### Event-Driven Architecture
- Components communicate via an `event_bus`, reducing tight coupling between modules.

## Code Style and Principles

### Code Quality
- **Readability**: Favours explicit and readable code over complex one-liners.
- **Organisation**: Files are well-organised with clear sections and comments.

### Error Handling
- **Philosophy**: Minimal `try/except` blocks are used to ensure errors are immediately visible ("fail fast").

### Logging
- **Implementation**: A custom logger provides color-coded console output.
- **Configuration**: Logging is configured centrally.

### Configuration System
- **Management**: A centralised system manages all application settings.
- **Type Safety**: Keys are defined in `ConfigKeys` for type safety and easy discoverability.

---

This codebase is a well-structured financial management application with a strong focus on modularity, maintainability, and a consistent user experience.

2025-07-27 @ 03:08:11
We are currently working on rationaling update data specifically around the view and state management but also dir structure, logical flow and file organisation ...
Refactoring has proved very difficult - we attempting to apply and event based state management system...
The current objective is to migrate the existing code base to the new system and and then covert to the new logical flow, based around:
~\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_UD_VIEW_docs_workspace\_USER_JORNEY_FLOW_v2.md

In update_data\_UD_VIEW_docs_workspace
you wil find the current working documents. 

I am currently run and debugging the app .. cleaning up issues along the way
The foxus is currently view, left_panel, and widgets 
which have their bases and primitives in gui shared componets
Watch out for nheritance patterns, these are intended to give common methods, and interfaces but do not attempt to make a generic base class that micromanages every widget 
- at least certainly not using ABC base classes
These will not work with the QT pyside classes. 
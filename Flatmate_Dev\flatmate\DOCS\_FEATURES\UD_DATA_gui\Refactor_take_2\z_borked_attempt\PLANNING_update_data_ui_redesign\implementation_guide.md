# Update Data UI - Implementation Guide

**Version**: 1.0  
**Date**: 2025-07-25  
**Status**: In Progress

## 1. Overview

This document provides a step-by-step guide for implementing the state-driven redesign of the Update Data UI. The architecture is based on a `ViewModel` that contains the view's state logic, configured by a `StateEngine` that reads a declarative state table from a CSV file. The core tasks are the creation of the `SelectGroupWidget`, development of the `SourceSelectionViewModel` and its `StateEngine`, and the integration of these components into the `update_data` module.

## 2. Implementation Phases

### Phase 1: Component Development (Foundation)

#### Task 1.1: Create `SelectGroupWidget`

*   **Objective**: Develop a reusable widget that combines a label, an option menu, and a button.
*   **Location**: Create the initial file at `fm/modules/update_data/_view/left_panel/widgets/select_group_widget.py`.
*   **Specification**: Refer to `component_spec_SelectGroupWidget.md`.
*   **Key Steps**:
    1.  Define a `SelectGroupWidget` class inheriting from `QWidget`.
    2.  Create a `SelectGroupConfig` dataclass or similar structure to handle configuration (label text, button text, menu options).
    3.  Compose the widget using existing shared components: `OptionMenuWithLabel` and `SecondaryButton`.
    4.  Implement methods to set/get values and handle button clicks via signals.
    5.  Create a basic standalone test to ensure the widget works as expected before integration.

#### Task 1.2: Develop `SourceSelectionViewModel` and `StateEngine`

*   **Objective**: Build the `ViewModel` that encapsulates the UI's state and logic, configured by a `StateEngine` that reads the state CSV.
*   **Location**:
    *   ViewModel: `fm/modules/update_data/_viewmodel/source_selection_view_model.py`
    *   State Engine: `fm/modules/update_data/_viewmodel/state_engine.py`
*   **Specification**: The refactored prototype (`run_prototype.py`) serves as the reference implementation.
*   **Key Steps**:
    1.  Create a `StateEngine` class that loads and parses `machine_readable_schema.csv` into a dictionary, providing widget configurations.
    2.  Create a `SourceSelectionViewModel` class inheriting from `QObject`.
    3.  The `ViewModel` will instantiate the `StateEngine`.
    4.  The `ViewModel` will hold the current UI state (e.g., `INITIAL`, `SOURCE_SELECTED`).
    5.  Implement public methods (`on_dropdown_changed`, `on_select_button_clicked`) that the View can call in response to user actions.
    6.  These methods will contain the logic for state transitions.
    7.  The `ViewModel` will emit Qt signals (`update_select_button`, `update_info_pane`, etc.) to notify the View of required changes. The View will be responsible for updating the actual widgets.

### Phase 2: Integration

#### Task 2.1: Refactor the Left Panel

*   **Objective**: Replace the hard-coded widgets in the left panel with the new `ViewModel`-driven approach.
*   **Location**: `fm/modules/update_data/_view/left_panel/left_panel.py`.
*   **Key Steps**:
    1.  In the `update_data_module`, instantiate the `SourceSelectionViewModel`.
    2.  Pass the `ViewModel` instance to the `LeftPanel`.
    3.  In the `LeftPanel`, connect widget signals (e.g., `select_button.clicked`) to the `ViewModel`'s public methods (`on_select_button_clicked`).
    4.  Connect the `ViewModel`'s signals (`update_select_button`) to handler methods in the `LeftPanel` that are responsible for updating the UI widgets.

#### Task 2.2: Integrate Center Panel and Folder Monitoring

*   **Objective**: Connect the state engine to the folder monitoring logic and the center panel display.
*   **Location**: `fm/modules/update_data/update_data_module.py`.
*   **Key Steps**:
    1.  Ensure the `QFileSystemWatcher` logic emits signals that can be processed as events by the `UIStateEngine` (e.g., `files_detected`).
    2.  The state engine should transition the UI to a "FilesQueued" state.
    3.  In the "FilesQueued" state, the `PROCESS FILES` button should become enabled, and the detected files should be displayed in the existing center panel file widget.

### Phase 3: Testing and Refinement

*   **Objective**: Ensure the new implementation is robust and meets all requirements.
*   **Specification**: Refer to `testing_strategy.md`.
*   **Key Steps**:
    1.  Perform unit tests on `SelectGroupWidget` and `UIStateEngine`.
    2.  Perform integration tests on the complete workflow.
    3.  Conduct User Acceptance Testing (UAT) to confirm the UX is intuitive and correct.

## 3. File Manifest

*   **New Files**:
    *   `fm/modules/update_data/_view/left_panel/widgets/select_group_widget.py`
    *   `fm/modules/update_data/_viewmodel/state_engine.py`
    *   `fm/modules/update_data/_viewmodel/source_selection_view_model.py`
*   **Modified Files**:
    *   `fm/modules/update_data/_view/left_panel/left_panel.py`
    *   `fm/modules/update_data/update_data_module.py`
*   **Supporting Documents**:
    *   `docs/.../mvp_state_schema.csv`
    *   `docs/.../component_spec_SelectGroupWidget.md`
    *   `docs/.../state_engine_spec.md`
    *   `docs/.../testing_strategy.md`

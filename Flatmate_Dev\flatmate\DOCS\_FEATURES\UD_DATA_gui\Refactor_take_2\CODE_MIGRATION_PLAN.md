# Update Data GUI - Code Migration Plan

## Overview
This document provides the concrete code changes required to implement the SimpleStateCoordinator system based on analysis of the actual codebase.

## Current State Analysis

### Files to Modify
1. **`ud_presenter.py`** - Remove StateEngine, add SimpleStateCoordinator (but just call it StateCoordinator)
2. **`ud_view.py`** - Add new view methods for state coordinator
3. **Create new file**: `simple_state_coordinator.py`

### Files to Remove/Archive
1. **`_view/viewmodel/state_engine.py`** - Move to z_archive
2. **`_view/viewmodel/update_data_viewmodel.py`** - Move to z_archive  
3. **`_view/viewmodel/machine_readable_schema.csv`** - Move to z_archive

## Implementation Plan

### Phase 1: Create Guide Pane Component (1 hour)

Since the design requires a `guide_pane` component that doesn't exist, we need to create it first.

#### Create: `_view/components/guide_pane.py`
```python
from PySide6.QtWidgets import <PERSON><PERSON>idge<PERSON>, <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout
from PySide6.QtCore import Qt

class GuidePaneWidget(QWidget):
    """
    Contextual guidance widget for Update Data GUI.
    Displays helpful messages to guide user through the workflow.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        
        self.message_label = QLabel("Select source files to begin")
        self.message_label.setWordWrap(True)
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        layout.addWidget(self.message_label)
        
    def display(self, message: str):
        """Display a message in the guide pane."""
        self.message_label.setText(message)
```

### Phase 2: Create SimpleStateCoordinator (2 hours)

#### Create: `simple_state_coordinator.py`
```python
from pathlib import Path
from typing import Optional

class SimpleStateCoordinator:
    """
    Centralized state management for Update Data GUI.
    Encapsulates all UI state transitions in a single, testable location.
    """
    
    def __init__(self, view, guide_pane):
        self.view = view
        self.guide_pane = guide_pane
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,  # 'folder' or 'files'
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }
    
    def set_source_folder(self, path: str, file_count: int):
        """Handle folder selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'folder',
            'source_path': path,
            'file_count': file_count
        })
        self._update_ui_state()
        self.guide_pane.display(f"Found {file_count} files ready for processing")
    
    def set_source_files(self, files: list[str]):
        """Handle file selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'files',
            'source_path': files,
            'file_count': len(files)
        })
        self._update_ui_state()
        self.guide_pane.display(f"Selected {len(files)} files for processing")
    
    def set_destination_same_as_source(self):
        """Handle same-as-source destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': 'same_as_source'
        })
        self._update_ui_state()
        self.guide_pane.display("Files will be archived in source folder")
    
    def set_destination_custom(self, path: str):
        """Handle custom destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': path
        })
        self._update_ui_state()
        self.guide_pane.display(f"Files will be archived in {Path(path).name}")
    
    def is_ready_to_process(self) -> bool:
        """Check if all requirements are met."""
        return (self.state['source_configured'] and 
                self.state['destination_configured'] and 
                not self.state['processing'])
    
    def start_processing(self):
        """Handle processing start."""
        self.state['processing'] = True
        self.view.set_process_text("Processing...")
        self.view.set_all_controls_enabled(False)
        self.guide_pane.display("Processing files...")
    
    def complete_processing(self, success_count: int):
        """Handle processing completion."""
        self.state['processing'] = False
        self.view.set_process_text("View Results")
        self.view.set_all_controls_enabled(True)
        self.guide_pane.display(f"Successfully processed {success_count} files")
    
    def reset_to_initial(self):
        """Reset to initial state."""
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }
        self._update_ui_state()
        self.guide_pane.display("Select source files to begin")
    
    def _update_ui_state(self):
        """Central state transition logic."""
        ready_to_process = self.is_ready_to_process()
        
        # Update view states
        self.view.set_process_enabled(ready_to_process)
        self.view.set_archive_enabled(self.state['source_configured'])
        
        # Update guide pane context
        if ready_to_process:
            self.guide_pane.display(f"Ready to process {self.state['file_count']} files")
```

### Phase 3: Update View Interface (1 hour)

#### Modify: `_view/ud_view.py`
Add these methods to the UpdateDataView class:

```python
def set_process_enabled(self, enabled: bool):
    """Enable/disable the process button."""
    if hasattr(self.left_buttons, 'process_btn'):
        self.left_buttons.process_btn.setEnabled(enabled)

def set_archive_enabled(self, enabled: bool):
    """Enable/disable the archive section."""
    self.set_save_select_enabled(enabled)

def set_process_text(self, text: str):
    """Set the process button text."""
    if hasattr(self.left_buttons, 'process_btn'):
        self.left_buttons.process_btn.setText(text)

def set_all_controls_enabled(self, enabled: bool):
    """Enable/disable all controls during processing."""
    if hasattr(self.left_buttons, 'process_btn'):
        self.left_buttons.process_btn.setEnabled(enabled)
    if hasattr(self.left_buttons, 'source_select_btn'):
        self.left_buttons.source_select_btn.setEnabled(enabled)
    if hasattr(self.left_buttons, 'save_select_btn'):
        self.left_buttons.save_select_btn.setEnabled(enabled)
```

Add guide pane to view layout:
```python
# In setup_ui method, add:
from .components.guide_pane import GuidePaneWidget
self.guide_pane = GuidePaneWidget()
# Add to appropriate layout position
```

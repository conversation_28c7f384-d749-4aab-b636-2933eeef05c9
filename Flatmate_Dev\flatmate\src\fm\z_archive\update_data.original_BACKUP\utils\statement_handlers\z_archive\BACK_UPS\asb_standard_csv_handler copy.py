"""ASB Bank standard CSV format mapping."""

from dataclasses import dataclass
from pathlib import Path
import re

import pandas as pd

from ._base_statement_handler import StatementHandler, StatementError
from fm.core.data_services.standards.fm_standard_columns import StandardColumns


@dataclass
class AsbStandardCSVHandler(StatementHandler):
    """ASB Bank standard CSV format mapping.
    
    This handler processes ASB's standard CSV export format, which includes:
    - Metadata rows at the top with account information
    - A header row with column names
    - Transaction data with detailed payment information
    """

    def __init__(self):
        super().__init__()
        # Statement format definition
        self.statement_format = self.StatementFormat(
            bank_name="ASB",
            variant="standard",  # Standard CSV format
            file_type="csv",
        )

        # Account number pattern and location
        # ASB includes account in metadata: "Bank 12; Branch 3053; Account 0478706-50"
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"Bank 12; Branch \d+; Account [\d-]+",  # Matches ASB account format
            location=(1, 0),  # Second line (1), first column (0)
            in_metadata=True,  # Account is in metadata
        )

        # Metadata structure
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=True,
            metadata_start=(0, 0),  # Starts at first line
            metadata_end=(6, 0),  # Ends at line 6 (before headers)
        )

        # Column mapping
        self.column_attrs = self.ColumnAttributes(
            has_col_names=True,  # File has column names
            n_source_cols=7,
            col_names_in_header=False,  # Column names are in the header section
            has_account_column=False,  # Account number is in metadata
            col_names_row=7,  # Column names are on row 7
            source_col_names=[
                "Date",
                "Unique Id",
                "Transaction Type",
                "Cheque Number",  # Always empty in ASB exports
                "Payee",
                "Memo",
                "Amount"
            ],
            target_col_names=[
                StandardColumns.DATE,
                StandardColumns.UNIQUE_ID,  # Bank's unique transaction identifier
                StandardColumns.PAYMENT_TYPE,  # Transaction type (EFTPOS, DEBIT, etc)
                None,  # Skip Cheque Number column (always empty in ASB)
                StandardColumns.OP_NAME,  # Keep payee separately
                StandardColumns.DETAILS,  # Keep memo separately and also use for combined details
                StandardColumns.AMOUNT,
            ],
            date_format='%Y/%m/%d',  # ASB uses YYYY/MM/DD format
        )
    
    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if this handler can process the given file.
        
        For ASB Standard CSV, we check:
        1. File extension is .csv (case-insensitive)
        2. File has the expected metadata structure
        3. File has the expected number of columns (7)
        4. First column contains valid dates
        """
        # Create a temporary instance to access the logger and other instance attributes
        temp_instance = cls()
        
        with temp_instance.handle_errors(f"Checking if {filepath} can be handled by {cls.__name__}"):
            filename = Path(filepath).name
            
            # Check file extension (case-insensitive)
            if not filepath.lower().endswith('.csv'):
                raise StatementError("File is not a CSV", is_validation=True)
            
            try:
                # Read the first 10 rows to check metadata and headers
                df = pd.read_csv(filepath, nrows=10, header=None)
            except Exception as e:
                raise StatementError(f"Error reading file: {str(e)}", is_validation=True)
            
            # Check if we have enough rows for metadata and headers
            if len(df) < 8:  # 7 metadata rows + header row
                raise StatementError(
                    f"File is too short (expected at least 8 rows, got {len(df)})",
                    is_validation=True
                )
            
            # Check account number in metadata (row 1, col 0)
            account_info = str(df.iloc[1, 0]) if len(df) > 1 else ""
            if not re.search(r"Bank \d+; Branch \d+; Account [\d-]+", account_info):
                raise StatementError(
                    f"Could not find account information in metadata: {account_info}",
                    is_validation=True
                )
            
            # Check number of columns (should be 7 for ASB standard CSV)
            if len(df.columns) != 7:
                raise StatementError(
                    f"Expected 7 columns, got {len(df.columns)}",
                    is_validation=True
                )
            
            # Check if first data row (row 7) has a valid date
            if len(df) > 7:
                try:
                    pd.to_datetime(df.iloc[7, 0], format='%Y/%m/%d')
                except (ValueError, TypeError) as e:
                    date_value = str(df.iloc[7, 0])
                    raise StatementError(
                        f"First data row doesn't match date format (YYYY/MM/DD): {date_value}",
                        is_validation=True
                    )
            
            temp_instance._logger.debug(f"Successfully validated {filename} as ASB standard CSV")
            return True

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle ASB-specific formatting:
        - Combine Payee and Memo into Details for better transaction descriptions
        
        Args:
            df: Input DataFrame with ASB transaction data
            
        Returns:
            Formatted DataFrame with combined details
            
        Raises:
            StatementError: If required columns are missing from the input DataFrame
        """
        with self.handle_errors("Applying ASB-specific formatting"):
            # Verify required columns exist
            required_columns = [
                StandardColumns.OP_NAME.value,
                StandardColumns.DETAILS.value
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise StatementError(
                    f"Missing required columns for ASB formatting: {', '.join(missing_columns)}",
                    is_validation=True
                )
            
            # Create combined details from payee and memo
            payee = df[StandardColumns.OP_NAME.value].fillna('')
            memo = df[StandardColumns.DETAILS.value].fillna('')
            df[StandardColumns.DETAILS.value] = (payee + ' ' + memo).str.strip()
            
            # Fill any remaining NaN values with empty string
            return df.fillna('')

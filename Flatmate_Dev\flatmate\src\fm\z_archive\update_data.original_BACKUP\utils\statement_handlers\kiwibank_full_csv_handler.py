from dataclasses import dataclass
from ._base_statement_handler import *
from fm.core.data_services.standards.columns import Columns


@dataclass
class KiwibankFullCSVHandler(StatementHandler):
    """Kiwibank full CSV format mapping."""
    
    statement_type = StatementType(
        bank_name="Kiwibank",
        variant="full",
        file_type="csv"
    )
    
    columns = ColumnAttributes(
        has_col_names=True,         
        colnames_in_header=True,      # Validate header names for full CSV
        col_names_row=0,
        data_start_row=1,
        has_account_column=True,     # Account number is in source data
        n_source_cols=16,
        date_format='%d-%m-%Y',  # Kiwibank Full CSV uses DD-MM-YYYY format (e.g., '18-08-2024')
        source_col_names=[
            "Account number",  
            "Date",
            "Memo/Description",
            "Source Code (payment type)",
            "TP ref",
            "TP part",
            "TP code",
            "OP ref",
            "OP part", 
            "OP code",
            "OP name",
            "OP Bank Account Number",
            "Amount (credit)",
            "Amount (debit)",
            "Amount",
            "Balance"
        ],
        target_col_names=(
            Columns.ACCOUNT,
            Columns.DATE,
            Columns.DETAILS,
            Columns.PAYMENT_TYPE,
            Columns.TP_REF,
            Columns.TP_PART,
            Columns.TP_CODE,
            Columns.OP_REF,
            Columns.OP_PART,
            Columns.OP_CODE,
            Columns.OP_NAME,  
            Columns.OP_ACCOUNT,
            Columns.CREDIT_AMOUNT,
            Columns.DEBIT_AMOUNT,
            Columns.AMOUNT,
            Columns.BALANCE
        ),
    )
    
    account = AccountNumberAttributes(
        pattern=r"38-\d{4}-\d{7}-\d{2}",  # Match exact format: 38-XXXX-XXXXXXX-XX
        in_data=True,  # Check first row data
        in_file_name=True,
        location=(1, 0)  # 2nd  row, 1st column: = "Account" column - all read pd.read_csv(header=None)
    )
    
    metadata = SourceMetadataAttributes(
        has_metadata_rows=False
    )
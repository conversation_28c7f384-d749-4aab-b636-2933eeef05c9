# Update Data GUI - User Journey Flow

## The "Choose Your Own Adventure" Story

You're about to update your financial data. Here's your path:

### 🏠 **Starting Point: The Welcome Screen**
*You see a clean interface with three main sections*

**What you see:**
- **Source Files...** section at the top
- **Archive** section in the middle  
- **PROCESS FILES** button at the bottom (currently grayed out)

### 🎯 **Your First Choice: Where's Your Data?**

**Option A: "I have a folder full of CSV files"**
- Click "Select Folder" under Source Files
- Browse to your folder and select it
- *Result: Archive section becomes active*
- >>  acknowledgement in main window info-widget
         which acts like a guide
         commenting on things happening in left panel. 
         when a folder is chosen, all recognised file types files are displayed in the file display section
**Option B: "I have specific CSV files to process"**
- Click "Select Files" under Source Files  
- Choose individual CSV files from different locations
- *Result: Archive section becomes active*
>> - info widget "guide" 
        "Monitor this folder for new files [ ]?."
### 📁 **Your Second Choice: Where Should Processed Files Go?**

**Option A: "Keep them in the same folder as source"**
- Select "Same as source" under Archive
- *Result: No additional folder selection needed*
>>info guide: Source folder selected, Tidy files into an "Archive" sub folder? [?]
**Option B: "I want them in a different location"**
- Select "Select folder..." under Archive
- Browse to choose your destination folder
- *Result: You pick the save location
>>info guide: Tidy files into "Archive" folder? [ ]

### ⚡ **The Magic Moment**

**When both choices are made:**
- **PROCESS FILES** button ~~turns from gray to active blue~~ 
>> becomes active.
- You're ready to process!

### 🔄 **During Processing**
- Watch the progress bar fill up
- See real-time status updates
- **Cancel** button available if you need to stop
>> how flash ! 
### ✅ **Success!**
- **PROCESS FILES** button changes to **View Results**
- Your data is now updated in the system
- Choose to view the results or exit

## The Simple Rules

1. **You need a source** (folder or files) before anything else works
2. **You need a destination** (same as source or custom folder)  
3. **Both choices must be made** before you can process
4. **Everything else happens automatically**

## Common User Paths

### **The Quick Path** (Most Common)
1. Select folder → Same as source → Process → Done

### **The Organized Path**  
1. Select folder → Custom destination → Process → Done

### **The Picky Path**
1. Select specific files → Custom destination → Process → Done

## Behind the Scenes (For the Curious)

- **INACTIVE** = waiting for your choices
- **ACTIVE** = ready to go  
>>info widget (check actual name)
Perhaps "guide_pane" would be better?
- **Progress bars** = working on your data
- **Green checkmarks** = success!

## One-Sentence Summary
**Pick your source, pick your destination, then press the button that turns blue.**
>> good logic description but  it doesnt turn blue, it's built on a base class that has an active and inactive state - qwidget
styles are defined in a qss style sheet, then elements are combined in a base class

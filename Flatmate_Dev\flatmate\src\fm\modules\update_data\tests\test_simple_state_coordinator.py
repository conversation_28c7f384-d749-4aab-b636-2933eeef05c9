"""
Unit tests for SimpleStateCoordinator.
"""

import unittest
from unittest.mock import Mock, MagicMock
import sys
import os

# Add the parent directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simple_state_coordinator import SimpleStateCoordinator


class TestSimpleStateCoordinator(unittest.TestCase):
    """Test cases for SimpleStateCoordinator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_view = Mock()
        self.mock_guide_pane = Mock()
        self.coordinator = SimpleStateCoordinator(self.mock_view, self.mock_guide_pane)
    
    def test_initial_state(self):
        """Test initial state is correct."""
        self.assertFalse(self.coordinator.is_ready_to_process())
        self.assertFalse(self.coordinator.state['source_configured'])
        self.assertFalse(self.coordinator.state['destination_configured'])
        self.assertFalse(self.coordinator.state['processing'])
        self.assertEqual(self.coordinator.state['file_count'], 0)
    
    def test_source_folder_selection(self):
        """Test folder source selection."""
        test_path = "/test/folder"
        test_count = 5
        
        self.coordinator.set_source_folder(test_path, test_count)
        
        self.assertTrue(self.coordinator.state['source_configured'])
        self.assertEqual(self.coordinator.state['source_type'], 'folder')
        self.assertEqual(self.coordinator.state['source_path'], test_path)
        self.assertEqual(self.coordinator.state['file_count'], test_count)
        
        # Check that guide pane was updated
        self.mock_guide_pane.display.assert_called_with(f"Found {test_count} files ready for processing")
        
        # Check that view methods were called
        self.mock_view.set_process_enabled.assert_called()
        self.mock_view.set_archive_enabled.assert_called_with(True)
    
    def test_source_files_selection(self):
        """Test files source selection."""
        test_files = ["/test/file1.csv", "/test/file2.csv", "/test/file3.csv"]
        
        self.coordinator.set_source_files(test_files)
        
        self.assertTrue(self.coordinator.state['source_configured'])
        self.assertEqual(self.coordinator.state['source_type'], 'files')
        self.assertEqual(self.coordinator.state['source_path'], test_files)
        self.assertEqual(self.coordinator.state['file_count'], len(test_files))
        
        # Check that guide pane was updated
        self.mock_guide_pane.display.assert_called_with(f"Selected {len(test_files)} files for processing")
    
    def test_destination_same_as_source(self):
        """Test same-as-source destination selection."""
        self.coordinator.set_destination_same_as_source()
        
        self.assertTrue(self.coordinator.state['destination_configured'])
        self.assertEqual(self.coordinator.state['destination_path'], 'same_as_source')
        
        # Check that guide pane was updated
        self.mock_guide_pane.display.assert_called_with("Files will be archived in source folder")
    
    def test_destination_custom(self):
        """Test custom destination selection."""
        test_path = "/test/destination"
        
        self.coordinator.set_destination_custom(test_path)
        
        self.assertTrue(self.coordinator.state['destination_configured'])
        self.assertEqual(self.coordinator.state['destination_path'], test_path)
        
        # Check that guide pane was updated with path name
        self.mock_guide_pane.display.assert_called()
    
    def test_ready_state(self):
        """Test ready to process state."""
        # Initially not ready
        self.assertFalse(self.coordinator.is_ready_to_process())
        
        # Set source
        self.coordinator.set_source_folder("/test/path", 5)
        self.assertFalse(self.coordinator.is_ready_to_process())  # Still need destination
        
        # Set destination
        self.coordinator.set_destination_same_as_source()
        self.assertTrue(self.coordinator.is_ready_to_process())  # Now ready
        
        # Check that process button was enabled
        self.mock_view.set_process_enabled.assert_called_with(True)
    
    def test_processing_state(self):
        """Test processing state transitions."""
        # Set up ready state
        self.coordinator.set_source_folder("/test/path", 5)
        self.coordinator.set_destination_same_as_source()
        
        # Start processing
        self.coordinator.start_processing()
        
        self.assertTrue(self.coordinator.state['processing'])
        self.assertFalse(self.coordinator.is_ready_to_process())  # Not ready while processing
        
        # Check view updates
        self.mock_view.set_process_text.assert_called_with("Processing...")
        self.mock_view.set_all_controls_enabled.assert_called_with(False)
        self.mock_guide_pane.display.assert_called_with("Processing files...")
    
    def test_processing_completion(self):
        """Test processing completion."""
        # Set up processing state
        self.coordinator.set_source_folder("/test/path", 5)
        self.coordinator.set_destination_same_as_source()
        self.coordinator.start_processing()
        
        # Complete processing
        success_count = 3
        self.coordinator.complete_processing(success_count)
        
        self.assertFalse(self.coordinator.state['processing'])
        
        # Check view updates
        self.mock_view.set_process_text.assert_called_with("View Results")
        self.mock_view.set_all_controls_enabled.assert_called_with(True)
        self.mock_guide_pane.display.assert_called_with(f"Successfully processed {success_count} files")
    
    def test_reset_to_initial(self):
        """Test reset to initial state."""
        # Set up some state
        self.coordinator.set_source_folder("/test/path", 5)
        self.coordinator.set_destination_same_as_source()
        
        # Reset
        self.coordinator.reset_to_initial()
        
        # Check all state is reset
        self.assertFalse(self.coordinator.state['source_configured'])
        self.assertFalse(self.coordinator.state['destination_configured'])
        self.assertFalse(self.coordinator.state['processing'])
        self.assertIsNone(self.coordinator.state['source_type'])
        self.assertIsNone(self.coordinator.state['source_path'])
        self.assertIsNone(self.coordinator.state['destination_path'])
        self.assertEqual(self.coordinator.state['file_count'], 0)
        
        # Check guide pane was updated
        self.mock_guide_pane.display.assert_called_with("Select source files to begin")


if __name__ == '__main__':
    unittest.main()

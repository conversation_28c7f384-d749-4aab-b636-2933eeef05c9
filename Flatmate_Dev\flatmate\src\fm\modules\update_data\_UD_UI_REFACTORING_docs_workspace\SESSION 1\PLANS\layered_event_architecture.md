# Layered Event Architecture Design

**Date**: 2025-07-26  
**Architect**: <PERSON> 🏗️  
**Pattern**: Two-Layer Event System (Global + Local)

---

## Architectural Overview

You're absolutely correct about needing **two layers**! This creates a clean separation between:

1. **Global Events** → Core system events (cross-module communication)
2. **Local Events** → Module-specific UI/state events (internal coordination)

```
┌─────────────────────────────────────────────────────────────┐
│                    GLOBAL EVENT LAYER                       │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Core Event Bus  │    │  Events Log     │                │
│  │ (Cross-Module)  │    │ (Navigation)    │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              ▲
                              │ Bridge Events
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    LOCAL EVENT LAYER                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ View Events     │    │ State Events    │                │
│  │ (UI Actions)    │    │ (State Changes) │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

---

## Recommended File Structure

```
update_data/
├── services/
│   ├── events.py                    # Existing - Global events (keep)
│   ├── local_event_bus.py          # NEW - Local event coordinator
│   └── view_events.py              # NEW - View-specific events
├── _view_components/
│   └── state_coordinator.py        # Enhanced with local events
└── ud_presenter.py                 # Uses both layers
```
>> should view_events be in services - or in view_components?
>> we should rename events.py
to local_event_bus.py [x] - VS<PERSON> did some auto refactoring
# >> *#TODO: 
## Layer 1: Global Events (Core Services)

**Location**: Keep existing `services/events.py`  
**Purpose**: Cross-module communication, pipeline events, navigation  
**Scope**: Application-wide

### Enhanced Global Events:
```python
# services/events.py - Enhanced with navigation events
class UpdateDataEvents(Enum):
    # Existing pipeline events (keep these)
    FILE_PROCESSING_STARTED = auto()
    FILE_PROCESSING_COMPLETED = auto()
    FILE_PROCESSING_STATS = auto()
    UNRECOGNIZED_FILES_DETECTED = auto()
    
    # NEW: Navigation/Module events (for events_log)
    MODULE_ACTIVATED = auto()
    MODULE_DEACTIVATED = auto()
    PROCESSING_SESSION_STARTED = auto()
    PROCESSING_SESSION_COMPLETED = auto()
    
    # NEW: Cross-module notifications
    DATA_UPDATED = auto()           # Notify other modules of data changes
    CONFIG_CHANGED = auto()         # Configuration updates
    USER_WORKFLOW_COMPLETED = auto() # Major workflow completion

class UpdateDataEventService:
    # Existing methods (keep)
    
    # NEW: Navigation events for events_log
    @classmethod
    def publish_module_activated(cls, module_info):
        """Publish when module becomes active - for events_log"""
        global_event_bus.publish(
            UpdateDataEvents.MODULE_ACTIVATED.name,
            {
                'module': 'update_data',
                'timestamp': datetime.now(),
                'context': module_info
            }
        )
    
    @classmethod
    def publish_workflow_completed(cls, workflow_result):
        """Publish major workflow completion - cross-module notification"""
        global_event_bus.publish(
            UpdateDataEvents.USER_WORKFLOW_COMPLETED.name,
            {
                'module': 'update_data',
                'workflow': 'file_processing',
                'result': workflow_result,
                'timestamp': datetime.now()
            }
        )
```
# *#TODO: 'result' should incude n files prcessed, n entries added to db, etc.
---

## Layer 2: Local Events (Module-Specific)

**Location**: `services/local_event_bus.py` + `services/view_events.py`  
**Purpose**: Internal module coordination, UI state management  
**Scope**: Update Data module only

### Local Event Bus:
```python
# services/local_event_bus.py - NEW
from typing import Any, Callable, Dict, List
import threading
from enum import Enum, auto

class LocalEventBus:
    """
    Local event bus for internal module communication.
    Separate from global event bus to avoid cross-module pollution.
    """
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self._listeners: Dict[str, List[Callable]] = {}
        self._lock = threading.Lock()
        self._event_log = []  # Local debugging
        
    def subscribe(self, event_type: str, listener: Callable):
        """Subscribe to local events"""
        with self._lock:
            if event_type not in self._listeners:
                self._listeners[event_type] = []
            self._listeners[event_type].append(listener)
            
    def emit(self, event_type: str, data: Any = None):
        """Emit local event"""
        # Log for debugging
        self._event_log.append({
            'type': event_type,
            'data': data,
            'timestamp': datetime.now()
        })
        
        # Notify listeners
        listeners = self._listeners.get(event_type, []).copy()
        for listener in listeners:
            try:
                listener(data)
            except Exception as e:
                print(f"Local event error in {self.module_name}.{event_type}: {e}")
                
    def bridge_to_global(self, local_event: str, global_event: str, transform_fn=None):
        """Bridge local events to global events"""
        def bridge_handler(data):
            transformed_data = transform_fn(data) if transform_fn else data
            from fm.core.services.event_bus import global_event_bus
            global_event_bus.publish(global_event, transformed_data)
            
        self.subscribe(local_event, bridge_handler)

# Module instance
update_data_local_bus = LocalEventBus('update_data')
```

### View Events Definition:
```python
# services/view_events.py - NEW
from enum import Enum, auto

class ViewEvents(Enum):
    """Local view events for Update Data module"""
    
    # User Actions (View → Presenter)
    SOURCE_SELECT_REQUESTED = auto()
    DESTINATION_SELECT_REQUESTED = auto()
    PROCESS_REQUESTED = auto()
    CANCEL_REQUESTED = auto()
    
    # Business Events (Presenter → State)
    SOURCE_DISCOVERED = auto()
    DESTINATION_CONFIGURED = auto()
    PROCESSING_STARTED = auto()
    PROCESSING_COMPLETED = auto()
    BUSINESS_ERROR = auto()
    
    # State Events (State → View)
    UI_STATE_CHANGED = auto()
    STATUS_MESSAGE_CHANGED = auto()
    
    # Dialog Events (Bidirectional)
    FOLDER_DIALOG_REQUESTED = auto()
    FOLDER_DIALOG_COMPLETED = auto()
    ERROR_DIALOG_REQUESTED = auto()

class ViewEventData:
    """Data structures for view events"""
    
    @staticmethod
    def source_select_request(selection_type: str, path: str = None):
        return {'type': selection_type, 'path': path}
        
    @staticmethod
    def source_discovered(source_type: str, files: list, path: str, count: int):
        return {
            'type': source_type,
            'files': files,
            'path': path,
            'count': count
        }
        
    @staticmethod
    def ui_state_update(can_process: bool, message: str, **kwargs):
        return {
            'can_process': can_process,
            'status_message': message,
            **kwargs
        }
```

---

## Event Flow Architecture

### Local Event Flow (Internal Module):
```
User Action → View Event → Presenter Logic → State Event → UI Update
     ↓              ↓              ↓              ↓           ↓
[Button Click] → [SOURCE_SELECT] → [Business] → [UI_STATE] → [Enable Button]
```

### Global Event Flow (Cross-Module):
```
Local Workflow → Bridge Event → Global Event → Events Log → Other Modules
      ↓               ↓              ↓             ↓            ↓
[File Processed] → [Transform] → [DATA_UPDATED] → [Log] → [Notify Tables]
```

---

## Integration with Existing Code

### Enhanced State Coordinator:
```python
# _view_components/state_coordinator.py - Enhanced
from ..services.local_event_bus import update_data_local_bus
from ..services.view_events import ViewEvents, ViewEventData

class UpdateDataStateCoordinator:
    def __init__(self):
        self.local_bus = update_data_local_bus
        self.state = {...}
        self._subscribe_to_local_events()
        self._setup_global_bridges()
        
    def _subscribe_to_local_events(self):
        """Subscribe to local business events"""
        self.local_bus.subscribe(ViewEvents.SOURCE_DISCOVERED.name, self.on_source_discovered)
        self.local_bus.subscribe(ViewEvents.PROCESSING_STARTED.name, self.on_processing_started)
        
    def _setup_global_bridges(self):
        """Bridge important local events to global"""
        self.local_bus.bridge_to_global(
            ViewEvents.PROCESSING_STARTED.name,
            'FILE_PROCESSING_STARTED',
            transform_fn=lambda data: {'job_sheet': data}
        )
        
    def on_source_discovered(self, source_info):
        """Handle local source discovery"""
        self.state['source_configured'] = True
        self.state['files'] = source_info['files']
        
        # Emit local UI update
        ui_state = ViewEventData.ui_state_update(
            can_process=self._can_process(),
            message=f"Found {source_info['count']} files"
        )
        self.local_bus.emit(ViewEvents.UI_STATE_CHANGED.name, ui_state)
```

### Refactored Presenter:
```python
# ud_presenter.py - Using both layers
class UpdateDataPresenter:
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        super().__init__(main_window, gui_config, gui_keys)
        
        # Local events for internal coordination
        self.local_bus = update_data_local_bus
        
        # Global events for cross-module communication  
        self.global_bus = global_event_bus
        
        self._subscribe_to_local_events()
        
    def _subscribe_to_local_events(self):
        """Subscribe to local view events"""
        self.local_bus.subscribe(ViewEvents.SOURCE_SELECT_REQUESTED.name, self.handle_source_select)
        self.local_bus.subscribe(ViewEvents.PROCESS_REQUESTED.name, self.handle_process)
        
    def handle_source_select(self, request_data):
        """Handle source selection - pure business logic"""
        try:
            source_info = self._discover_files(request_data)
            
            # Emit local business event
            self.local_bus.emit(
                ViewEvents.SOURCE_DISCOVERED.name,
                ViewEventData.source_discovered(**source_info)
            )
            
        except Exception as e:
            self.local_bus.emit(ViewEvents.BUSINESS_ERROR.name, {'message': str(e)})
```

---

## Benefits of This Architecture

### ✅ **Separation of Concerns**:
- **Global Events**: Cross-module communication, navigation, logging
- **Local Events**: Internal UI coordination, state management

### ✅ **No Circular Imports**:
- Components only depend on their respective event bus
- Clear dependency hierarchy

### ✅ **Events Log Capability**:
- Global events automatically logged for navigation context
- Other modules can subscribe to relevant global events

### ✅ **Scalability**:
- Easy to add new local events without affecting global system
- Bridge pattern allows selective promotion of local events to global

### ✅ **Developer Experience**:
- Clear event naming conventions
- Type-safe event data structures
- Local debugging through event logs

This layered approach gives you the clean decoupling you need while maintaining the ability to coordinate across modules through the global event system.

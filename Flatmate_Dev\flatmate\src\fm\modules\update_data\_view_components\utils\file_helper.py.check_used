"""
File helper utilities for the Update Data module.
"""

import shutil
from pathlib import Path
from typing import Dict, <PERSON>, Optional, Tuple, Union


class FileHelper:
    """Helper class for file operations in the Update Data module."""
    
    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, str]:
        """
        Get information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {
                "name": file_path.name,
                "size": "0",
                "type": "Unknown",
                "exists": "No"
            }
        
        # Get file stats
        stats = file_path.stat()
        
        # Determine file type
        file_type = "Unknown"
        if file_path.suffix.lower() in ['.csv', '.xlsx', '.xls']:
            file_type = "Spreadsheet"
        elif file_path.suffix.lower() in ['.txt', '.md', '.rtf']:
            file_type = "Text"
        elif file_path.suffix.lower() in ['.pdf']:
            file_type = "PDF"
        
        # Format size
        size_bytes = stats.st_size
        if size_bytes < 1024:
            size_str = f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            size_str = f"{size_bytes / 1024:.1f} KB"
        else:
            size_str = f"{size_bytes / (1024 * 1024):.1f} MB"
        
        return {
            "name": file_path.name,
            "size": size_str,
            "type": file_type,
            "exists": "Yes",
            "modified": stats.st_mtime
        }
    
    @staticmethod
    def get_files_in_directory(directory: Union[str, Path], extensions: Optional[List[str]] = None) -> List[str]:
        """
        Get list of files in a directory with optional extension filtering.
        
        Args:
            directory: Directory to scan
            extensions: List of extensions to include (e.g., ['.csv', '.xlsx'])
            
        Returns:
            List of file paths
        """
        directory = Path(directory)
        if not directory.exists() or not directory.is_dir():
            return []
        
        files = []
        for item in directory.iterdir():
            if item.is_file():
                if extensions is None or item.suffix.lower() in extensions:
                    files.append(str(item))
        
        return sorted(files)
    
    @staticmethod
    def copy_file(source: Union[str, Path], destination: Union[str, Path]) -> Tuple[bool, str]:
        """
        Copy a file from source to destination.
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Returns:
            Tuple of (success, message)
        """
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            # Check if source exists
            if not source_path.exists():
                return False, f"Source file does not exist: {source}"
            
            # Create destination directory if it doesn't exist
            dest_dir = dest_path.parent
            if not dest_dir.exists():
                dest_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy the file
            shutil.copy2(source_path, dest_path)
            return True, f"File copied successfully to {destination}"
            
        except Exception as e:
            return False, f"Error copying file: {str(e)}"

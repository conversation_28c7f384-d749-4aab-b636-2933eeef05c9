# Implementation Guide: Table Search & Filtering

## Implementation Steps

### 1. Analyze Current Filtering Logic
**Files to Examine:**
- Search input handler in the Categorise view UI
- Current filtering implementation in table model/controller

**Key Questions:**
- How is the search input currently processed?
- What triggers the live filtering?
- Where are filter terms parsed and applied?

### 2. Implement Operator Detection
**Implementation Details:**
- Add detection for operator characters: `O`, `R`, `/`, `(`, `)`, etc.
- Create a regex pattern to identify these characters in the search input
- Implement a function that returns `true` when advanced operators are detected

**Example Implementation:**
```python
def has_advanced_operators(search_text):
    """Check if search text contains advanced operators."""
    # Pattern matches OR, parentheses, and other advanced operators
    pattern = r'[\(\)/]|\bOR\b'
    return bool(re.search(pattern, search_text, re.IGNORECASE))
```

### 3. Disable Live Filtering for Advanced Queries
**Implementation Details:**
- Modify the input handler to check for advanced operators on each keystroke
- When detected, disable the live filtering mechanism
- Add a visual indicator (button or highlight) to show that filtering requires explicit apply

**Example Implementation:**
```python
def on_search_text_changed(self, text):
    """Handle search text changes."""
    if has_advanced_operators(text):
        self.disable_live_filtering()
        self.show_apply_button(True)
    else:
        self.enable_live_filtering()
        self.show_apply_button(False)
        self.apply_filter(text)  # Apply filter immediately for simple queries
```

### 4. Hide Internal/System Columns
**Implementation Details:**
- Identify all internal/system columns that should be hidden:
  - `DB UID`
  - `Source UID`
  - `Is Deleted`
- Modify the table view configuration to exclude these columns

**Example Implementation:**
```python
def configure_visible_columns(self):
    """Configure which columns are visible to users."""
    hidden_columns = ['DB UID', 'Source UID', 'Is Deleted']
    for column in hidden_columns:
        self.table_view.hideColumn(self.get_column_index(column))
```

### 5. Optimize Filtering Performance
**Implementation Details:**
- Profile the current filtering implementation to identify bottlenecks
- Optimize the filtering logic, especially for complex queries
- Consider implementing a debounce mechanism for live filtering

**Example Implementation:**
```python
def apply_filter(self, text, force=False):
    """Apply filter with performance optimization."""
    # Cancel any pending filter operation
    if hasattr(self, '_filter_timer') and self._filter_timer.isActive():
        self._filter_timer.stop()
    
    # Use timer to debounce frequent filter operations
    if not force and not has_advanced_operators(text):
        self._filter_timer = QTimer()
        self._filter_timer.setSingleShot(True)
        self._filter_timer.timeout.connect(lambda: self._do_apply_filter(text))
        self._filter_timer.start(200)  # 200ms debounce
    else:
        self._do_apply_filter(text)
```

### 6. Add Error Handling and User Feedback
**Implementation Details:**
- Add try/except blocks around filter application
- Provide clear error messages for invalid filter syntax
- Show visual feedback when filtering fails

**Example Implementation:**
```python
def _do_apply_filter(self, text):
    """Actually apply the filter with error handling."""
    try:
        # Apply the filter
        filter_result = self.filter_model.set_filter_text(text)
        self.status_bar.showMessage(f"Found {filter_result['count']} matches")
    except Exception as e:
        from fm.core.services.logger import log
        log.error(f"Filter error: {str(e)}")
        self.status_bar.showMessage(f"Filter error: {str(e)}", 5000)
```

## Testing Strategy

### Unit Tests
- Test operator detection function with various inputs
- Test column visibility configuration
- Test filter application with simple and complex queries

### Integration Tests
- Verify UI updates correctly when switching between simple and advanced queries
- Test performance with large datasets (1000+ rows)
- Verify error handling with invalid filter syntax

### User Acceptance Tests
- Test all acceptance criteria from requirements
- Validate with real user data and edge cases
- Verify performance metrics (live filtering < 200ms)

## Known Pitfalls and Solutions

### Operator Detection Issues
- **Problem:** Missing some operator characters or patterns
- **Solution:** Comprehensive regex pattern and thorough testing with edge cases

### UI Update Issues
- **Problem:** UI not updating correctly when switching between live and explicit apply
- **Solution:** Ensure proper state management and UI refresh on mode changes

### Performance Issues
- **Problem:** Lag or freezes with complex queries or large datasets
- **Solution:** Implement debouncing, optimize filter algorithm, consider background processing

### Bracket Handling
- **Problem:** Severe lag when entering text inside brackets (observed in testing)
- **Solution:** Immediately disable live filtering when brackets are detected, require explicit apply

## Implementation Checkpoints

1. ✓ Operator detection function works correctly
2. ✓ Live filtering disables properly for advanced queries
3. ✓ Apply button/visual cue appears when needed
4. ✓ Internal columns are hidden from view
5. ✓ No UI lag or freeze with any filter
6. ✓ Error handling provides clear feedback

# Shared Components Simple Organization - Implementation Guide

**Date**: July 22, 2025  
**Status**: Implementation Ready  
**Goal**: Add simple label components without breaking existing functionality  

## Current Directory Structure (Accurate)

```
flatmate/src/fm/gui/_shared_components/widgets/
├── __init__.py                    # Current convenience imports
├── account_selector.py            # ✅ Working - AccountSelector
├── base_widgets.py                # ⚠️  Deprecated compatibility shim
├── buttons.py                     # ✅ Working - ActionButton, SecondaryButton, ExitButton
├── checkboxes.py                  # ✅ Working - LabeledCheckBox
├── date_filter_pane.py            # ✅ Working - DateFilterPane
└── option_menus.py                # ✅ Working - OptionMenuWithLabel, OptionMenuWithLabelAndButton
```

## Implementation Steps

### Step 1: Create labels.py

**File**: `flatmate/src/fm/gui/_shared_components/widgets/labels.py`

```python
"""
Simple label components that just wrap QLabel + setObjectName.
No bullshit, no complexity, just consistent styling.
"""
from PySide6.QtWidgets import QLabel


class HeadingLabel(QLabel):
    """Main heading label - just QLabel + objectName='heading'."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("heading")


class SubheadingLabel(QLabel):
    """Panel subheading label - just QLabel + objectName='lbl_panel_subheading'."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("lbl_panel_subheading")


class InfoLabel(QLabel):
    """Info label - just QLabel + objectName='subheading'."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("subheading")
```

### Step 2: Update __init__.py

**File**: `flatmate/src/fm/gui/_shared_components/widgets/__init__.py`

```python
"""
Shared widget components for the Flatmate application.

This package provides reusable concrete UI widgets that can be used
across different modules for consistent styling and behavior.

Organized by widget type:
- buttons: ActionButton, SecondaryButton, ExitButton
- checkboxes: LabeledCheckBox
- labels: HeadingLabel, SubheadingLabel, InfoLabel
- option_menus: OptionMenuWithLabel, OptionMenuWithLabelAndButton
- selectors: AccountSelector
- filters: DateFilterPane
"""

from .buttons import ActionButton, SecondaryButton, ExitButton
from .checkboxes import LabeledCheckBox
from .labels import HeadingLabel, SubheadingLabel, InfoLabel
from .option_menus import OptionMenuWithLabel, OptionMenuWithLabelAndButton
from .account_selector import AccountSelector
from .date_filter_pane import DateFilterPane

__all__ = [
    # Buttons
    'ActionButton',
    'SecondaryButton', 
    'ExitButton',
    
    # Checkboxes
    'LabeledCheckBox',
    
    # Labels
    'HeadingLabel',
    'SubheadingLabel',
    'InfoLabel',
    
    # Option Menus
    'OptionMenuWithLabel',
    'OptionMenuWithLabelAndButton',
    
    # Selectors
    'AccountSelector',
    
    # Filters
    'DateFilterPane'
]
```

### Step 3: Test Implementation

**Command**: Run the app to ensure no import errors
```bash
cd flatmate && source .venv_fm313/Scripts/activate && fm
```

**Expected Result**: App starts successfully, no import errors

### Step 4: Update Module Usage (Incremental)

#### Update Data Module
**Files to modify**:
- `flatmate/src/fm/modules/update_data/_view/left_panel/widgets/widgets.py`
- `flatmate/src/fm/modules/update_data/_view/center_panel/data_pane.py`
- `flatmate/src/fm/modules/update_data/_view/center_panel/widgets/status_bar.py`

**Pattern**:
```python
# Before
from PySide6.QtWidgets import QLabel
self.title = QLabel("Update Data")
self.title.setObjectName("heading")

# After  
from fm.gui._shared_components.widgets import HeadingLabel
self.title = HeadingLabel("Update Data")
```

#### Categorize Module
**Files to modify**:
- `flatmate/src/fm/modules/categorize/_view/components/left_panel/left_panel.py`

#### Home Module
**Files to modify**:
- `flatmate/src/fm/modules/home/<USER>

### Step 5: Validation Checklist

After each module update:

✅ **App starts without errors**  
✅ **Buttons have proper colors** (green action buttons, not white)  
✅ **Labels have consistent styling** (proper fonts, colors)  
✅ **All modules load correctly**  
✅ **Navigation between modules works**  

## File Structure After Implementation

```
flatmate/src/fm/gui/_shared_components/widgets/
├── __init__.py                    # ✅ Updated with label imports
├── account_selector.py            # ✅ Unchanged
├── base_widgets.py                # ⚠️  Keep for compatibility (can remove later)
├── buttons.py                     # ✅ Unchanged
├── checkboxes.py                  # ✅ Unchanged  
├── date_filter_pane.py            # ✅ Unchanged
├── labels.py                      # 🆕 NEW - Simple label components
└── option_menus.py                # ✅ Unchanged
```

## Usage Examples After Implementation

```python
# Clean, consistent imports
from fm.gui._shared_components.widgets import (
    ActionButton, SecondaryButton, ExitButton,
    LabeledCheckBox,
    HeadingLabel, SubheadingLabel, InfoLabel,
    OptionMenuWithLabel, OptionMenuWithLabelAndButton
)

# Simple, obvious usage
self.title = HeadingLabel("Update Data")
self.source_label = SubheadingLabel("1. Source Files")
self.file_info_label = InfoLabel("File:")
self.process_btn = ActionButton("Process")
self.cancel_btn = ExitButton("Cancel")
self.db_checkbox = LabeledCheckBox("Update Database", checked=True)
```

## CSS Styling (No Changes Needed)

The existing CSS will work perfectly:

```css
/* These selectors will target our new label components */
QLabel#heading {
    font-size: 1.5em;
    font-weight: bold;
    color: white;
}

QLabel#lbl_panel_subheading {
    color: #CCCCCC;
    font-size: 1.3em;
    font-weight: bold;
    margin-top: 15px;
}

QLabel#subheading {
    color: #CCCCCC;
    font-weight: bold;
}
```

## Success Criteria

1. ✅ **No breaking changes** - Existing functionality preserved
2. ✅ **Simple implementation** - Just QLabel + setObjectName
3. ✅ **Consistent styling** - All labels use proper CSS classes
4. ✅ **Clean calling code** - One line instead of two
5. ✅ **Easy to use** - Import and use, no configuration needed

## What We're NOT Doing

❌ **BaseWidget abstractions**  
❌ **Configuration systems**  
❌ **Wrapper widgets**  
❌ **Factory patterns**  
❌ **Abstract base classes**  

---

**Ready to implement**: Start with Step 1 - Create labels.py

# Component Specification: UIStateEngine

**Version**: 1.0  
**Date**: 2025-07-25  
**Status**: Definition

## 1. Overview

The `StateEngine` is a configuration loader for the `SourceSelectionViewModel`. It reads a declarative state table from a CSV file and provides the initial configuration properties for UI components. It does not manage state transitions; that logic is contained within the `ViewModel` itself.

## 2. Technical Specification

*   **Class Name**: `StateEngine`
*   **Inherits From**: `QObject`
*   **Location**: `fm.modules.update_data._viewmodel.state_engine`

### 2.1. State Table (`machine_readable_schema.csv`)

The engine is driven by a CSV file that defines the initial properties of UI components. The `ViewModel` uses this data to configure the UI at startup and during state transitions.

| component_id      | state_initial | text_initial    | text_folder_selected |
|-------------------|---------------|-----------------|----------------------|
| source_select_btn | active        | Select...       | Change...            |
| process_btn       | disabled      | Process Files   | Process Files        |

### 2.2. API and Methods

*   `__init__(self, csv_path: str, parent=None)`: Constructor.
    *   `csv_path`: Absolute path to the state definition CSV.
*   `get_initial_config(self, component_id: str) -> dict`: Retrieves the initial configuration for a given `component_id` as a dictionary.

### 2.3. Signals

None. The `StateEngine` does not emit signals; it is a passive configuration loader. The `ViewModel` is responsible for emitting signals to the View.

### 2.4. Example Usage

```python
# In the SourceSelectionViewModel
from .state_engine import StateEngine

class SourceSelectionViewModel(QObject):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 1. Create an instance of the engine
        self.state_engine = StateEngine('path/to/machine_readable_schema.csv')
        
        # 2. Use the engine to get configuration data
        self._configure_ui()

    def _configure_ui(self):
        # Get config for a specific widget
        button_config = self.state_engine.get_initial_config('source_select_btn')
        initial_text = button_config.get('text_initial')
        
        # Emit a signal to the View to update the widget
        self.update_select_button.emit(initial_text, True)
```

## 3. Implementation Notes

*   The CSV is loaded into a simple dictionary for fast lookups.
*   The engine should handle file-not-found errors gracefully.

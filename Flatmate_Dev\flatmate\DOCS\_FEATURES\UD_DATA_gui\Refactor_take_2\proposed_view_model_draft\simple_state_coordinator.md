# Simple State Coordinator - KISS Implementation

## Design Philosophy
**Keep It Simple, Stupid** - Centralized state management without architectural complexity

## Core Concept
Single coordinator class that encapsulates all UI state logic in one location

## Implementation

```python
class SimpleStateCoordinator:
    """
    Centralized state management for Update Data GUI.
    
    Encapsulates all UI state transitions in a single, testable location.
    Follows KISS principle - no external configuration, just clean code.
    """
    
    def __init__(self, view, guide_pane):
        self.view = view
        self.guide_pane = guide_pane
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,  # 'folder' or 'files'
            'source_path': None,
            'destination_path': None,
            'file_count': 0
        }
    
    def set_source_folder(self, path: str, file_count: int):
        """Handle folder selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'folder',
            'source_path': path,
            'file_count': file_count
        })
        self._update_ui_state()
        self.guide_pane.display(f"Found {file_count} files in {Path(path).name}")
    
    def set_source_files(self, files: list[str]):
        """Handle file selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'files',
            'source_path': files,
            'file_count': len(files)
        })
        self._update_ui_state()
        self.guide_pane.display(f"Selected {len(files)} files for processing")
    
    def set_destination_same_as_source(self):
        """Handle same-as-source destination."""
        self.state['destination_configured'] = True
        self.state['destination_path'] = 'same_as_source'
        self._update_ui_state()
        self.guide_pane.display("Files will be archived in source folder")
    
    def set_destination_custom(self, path: str):
        """Handle custom destination."""
        self.state['destination_configured'] = True
        self.state['destination_path'] = path
        self._update_ui_state()
        self.guide_pane.display(f"Files will be archived in {Path(path).name}")
    
    def _update_ui_state(self):
        """Central state transition logic."""
        ready_to_process = (
            self.state['source_configured'] and 
            self.state['destination_configured'] and 
            not self.state['processing']
        )
        
        # Update view states
        self.view.set_process_enabled(ready_to_process)
        self.view.set_archive_enabled(self.state['source_configured'])
        
        # Update guide pane context
        if ready_to_process:
            self.guide_pane.display(f"Ready to process {self.state['file_count']} files")
    
    def start_processing(self):
        """Handle processing start."""
        self.state['processing'] = True
        self.view.set_process_text("Processing...")
        self.view.set_all_controls_enabled(False)
        self.guide_pane.display("Processing files...")
    
    def complete_processing(self, success_count: int):
        """Handle processing completion."""
        self.state['processing'] = False
        self.view.set_process_text("View Results")
        self.view.set_all_controls_enabled(True)
        self.guide_pane.display(f"Successfully processed {success_count} files")
    
    def reset_to_initial(self):
        """Reset to initial state."""
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,
            'source_path': None,
            'destination_path': None,
            'file_count': 0
        }
        self._update_ui_state()
        self.guide_pane.display("Select source files to begin")

# Usage in Presenter
class UpdateDataPresenter:
    def __init__(self, view, guide_pane):
        self.state_coordinator = SimpleStateCoordinator(view, guide_pane)
        
    def handle_folder_selected(self, path: str, files: list):
        self.state_coordinator.set_source_folder(path, len(files))
        
    def handle_files_selected(self, files: list):
        self.state_coordinator.set_source_files(files)
        
    def handle_destination_selected(self, path: str):
        self.state_coordinator.set_destination_custom(path)
        
    def handle_same_as_source(self):
        self.state_coordinator.set_destination_same_as_source()
        
    def handle_process(self):
        self.state_coordinator.start_processing()
```

## Benefits

### Simplicity
- **Single source of truth** for all state logic
- **No external configuration** - just Python code
- **Easy to test** - unit tests can cover all state transitions
- **Easy to debug** - clear method names and state tracking

### Maintainability
- **Centralized changes** - modify one file for state logic updates
- **Clear dependencies** - view and guide_pane only
- **No magic** - explicit method calls for every state change

### Extensibility
- **Easy to add new states** - just add methods and state keys
- **Easy to add logging** - centralized state tracking
- **Easy to add validation** - single location for business rules

## Testing Strategy

```python
# Unit test example
def test_state_transitions():
    coordinator = SimpleStateCoordinator(mock_view, mock_guide)
    
    # Test folder selection
    coordinator.set_source_folder("/test/folder", 5)
    assert coordinator.state['source_configured'] is True
    assert coordinator.state['file_count'] == 5
    
    # Test destination selection
    coordinator.set_destination_same_as_source()
    assert coordinator.state['destination_configured'] is True
    assert coordinator.state['destination_path'] == 'same_as_source'
    
    # Test ready state
    assert coordinator.view.set_process_enabled.called_with(True)
```

## Migration Path

1. **Phase 1**: Create SimpleStateCoordinator class
2. **Phase 2**: Migrate existing hard-coded logic to coordinator
3. **Phase 3**: Remove unused StateEngine and ViewModel code
4. **Phase 4**: Add comprehensive unit tests

## One-Line Summary
**One coordinator class replaces scattered state logic with centralized, testable, maintainable code.**

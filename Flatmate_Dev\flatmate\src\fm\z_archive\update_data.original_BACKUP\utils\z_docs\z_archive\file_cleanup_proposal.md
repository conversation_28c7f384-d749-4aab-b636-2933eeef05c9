# Proposal: Consolidate File Cleanup Logic

**Date:** 2025-07-10

## 1. Goal

To move all file system operations from `dw_director.py` into `dw_pipeline.py`, creating a clean separation of concerns. This proposal focuses on moving the file deletion logic.

## 2. The Problem

Currently, `dw_director.py` performs the final cleanup step of deleting the original source files after they have been successfully backed up (lines 127-136 in `dw_director.py`).

This violates the architectural principle we are working towards, where the `dw_director` should only be an **orchestrator**, managing the workflow, while the `dw_pipeline` should handle all **operations**, including data processing and file system interactions.

## 3. Proposed Solution

I propose to modify the `back_up_originals` function in `dw_pipeline.py` to optionally handle the cleanup of source files.

1.  **Update `back_up_originals` in `dw_pipeline.py`**:
    *   Add a new boolean parameter to the function signature: `cleanup_source_files: bool = False`.
    *   If `cleanup_source_files` is `True`, after the files are successfully backed up, the function will proceed to delete the original source files.
    *   The function's return value (the stats dictionary) will be updated to include the count of deleted files.

2.  **Update `dw_director.py`**:
    *   The call to `back_up_originals` will be updated to pass the `cleanup_source_folder` flag to the new `cleanup_source_files` parameter.
    *   The entire `for` loop responsible for deleting files (lines 127-136) will be removed, as this logic now resides in the pipeline.

## 4. Reasoning

-   **Separation of Concerns**: This change enforces the key architectural goal. The director will no longer contain `os.remove` calls or any direct file system manipulation logic. It will simply tell the pipeline *what* to do (backup and cleanup), and the pipeline will handle *how* it gets done.
-   **Code Consolidation**: It centralizes related logic. The backup and subsequent cleanup of a file are part of a single logical operation. Housing them in the same function makes the code cleaner and easier to understand.
-   **Simplifies Director**: This makes the `dw_director` code shorter, simpler, and more focused on its core responsibility of orchestration.

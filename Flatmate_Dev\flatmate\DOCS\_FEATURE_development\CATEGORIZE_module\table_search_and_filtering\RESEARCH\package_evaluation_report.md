# Boolean Search Parser Package Evaluation Report

**Date:** 2025-07-18  
**Purpose:** Evaluate available Python packages for boolean search parsing to replace custom parser implementation

---

## Executive Summary

After evaluating available Python packages for boolean search parsing, I recommend **luqum** as the primary choice with **boolean-parser** as a secondary option. Both packages offer mature, well-tested solutions that align with our package-based implementation strategy.

---

## Evaluated Packages

### 1. luqum (RECOMMENDED)

**Package:** `luqum` v1.0.0  
**PyPI:** https://pypi.org/project/luqum/  
**GitHub:** https://github.com/jurismarches/luqum  
**License:** Dual licensed (Apache 2.0 / LGPL 3.0)

#### Strengths
- **Mature & Battle-tested**: 194 stars, actively maintained, v1.0.0 released Feb 2025
- **Lucene Query DSL Compatible**: Implements standard Lucene syntax (industry standard)
- **Full Feature Set**: Supports AND, OR, NOT, parentheses, quoted phrases, field queries
- **AST Generation**: Produces abstract syntax tree for manipulation and analysis
- **ElasticSearch Integration**: Can convert to ElasticSearch JSON DSL (future extensibility)
- **Python 3.10+ Compatible**: Modern Python support
- **Comprehensive Documentation**: Well-documented with examples

#### Potential Concerns
- **Complexity**: May be overkill for simple use cases
- **Learning Curve**: Lucene syntax might be unfamiliar to some users
- **Dependencies**: Uses PLY (Python Lex-Yacc) parser generator

#### Example Usage
```python
from luqum import parser
tree = parser.parse("(coffee OR tea) AND -decaf")
# Returns AST that can be traversed and evaluated
```

---

### 2. boolean-parser (ALTERNATIVE)

**Package:** `boolean-parser` v0.1.4  
**PyPI:** https://pypi.org/project/boolean-parser/  
**GitHub:** https://github.com/havok2063/boolean_parser  
**License:** BSD 3-Clause

#### Strengths
- **Lightweight**: Focused specifically on boolean expressions
- **Pyparsing-based**: Uses well-established pyparsing library
- **SQLAlchemy Integration**: Can convert to SQLAlchemy filter conditions
- **Simple API**: Straightforward to use and integrate
- **Good Documentation**: Clear examples and usage patterns

#### Potential Concerns
- **Beta Status**: Still in development (v0.1.4, Beta classification)
- **Limited Maintenance**: Smaller community, less frequent updates
- **Feature Set**: May lack some advanced features we might need later
- **Python 3.6+ Only**: Older Python version requirement

#### Example Usage
```python
from boolean_parser import Parser
parser = Parser()
result = parser.parse("x > 1 and y < 2")
# Returns parsed expression object
```

---

### 3. pyparsing Examples (CONSIDERED)

**Source:** https://github.com/pyparsing/pyparsing/blob/master/examples/booleansearchparser.py

#### Strengths
- **Educational Value**: Good reference implementation
- **Customizable**: Full control over grammar and behavior
- **No Dependencies**: Uses only pyparsing (which we might already have)

#### Concerns
- **Not a Package**: Would need to copy/adapt example code
- **Maintenance Burden**: Would become our responsibility to maintain
- **Limited Features**: Basic implementation, would need extension

---

## Recommendation: luqum

### Primary Choice: luqum

**Rationale:**
1. **Industry Standard**: Implements Lucene Query DSL, familiar to many users
2. **Mature & Stable**: v1.0.0 release indicates production readiness
3. **Feature Complete**: Supports all operators we need (AND, OR, NOT, parentheses, quotes)
4. **Extensible**: AST output allows for custom evaluation and manipulation
5. **Future-Proof**: Can integrate with ElasticSearch if we ever need full-text search
6. **Active Development**: Recent releases and ongoing maintenance

### Implementation Strategy

1. **Phase 1**: Basic integration with preprocessing layer
2. **Phase 2**: Leverage full Lucene syntax capabilities
3. **Phase 3**: Explore advanced features (field queries, ranges, etc.)

### Preprocessing Requirements

Even with luqum, we'll need preprocessing to handle our operator synonyms:

```python
def preprocess_query(user_input: str) -> str:
    """Convert user-friendly operators to Lucene syntax."""
    # Handle OR operators: | and / → OR
    query = re.sub(r'(\w+)\|(\w+)', r'\1 OR \2', user_input)
    query = re.sub(r'(\w+)/(\w+)', r'\1 OR \2', query)
    
    # Handle NOT operators: - → NOT
    query = re.sub(r'-(\w+)', r'NOT \1', query)
    
    # Handle implicit AND (spaces)
    # Lucene already handles this naturally
    
    return query
```

---

## Implementation Plan

### Step 1: Package Installation & Basic Integration
- Install luqum: `pip install luqum`
- Create SearchQueryParser wrapper class
- Implement basic preprocessing for operator synonyms

### Step 2: AST Evaluation
- Implement custom visitor to evaluate parsed AST against our data
- Handle field mapping (map search fields to our column names)
- Implement case-insensitive matching

### Step 3: Performance Optimization
- Profile parsing performance vs. current implementation
- Implement expression caching for repeated queries
- Optimize preprocessing step

### Step 4: Testing & Validation
- Comprehensive test suite covering all syntax patterns
- Backward compatibility testing with Phase 1 syntax
- Performance benchmarking

---

## Risk Mitigation

### Fallback Strategy
If luqum proves unsuitable:
1. **Secondary Option**: Switch to boolean-parser
2. **Custom Implementation**: Use pyparsing examples as reference
3. **Hybrid Approach**: Keep simple cases in custom parser, use package for complex ones

### Performance Concerns
- Profile early and often
- Consider caching parsed expressions
- Implement lazy evaluation where possible

---

## Next Steps

1. **Prototype Integration**: Create minimal working example with luqum
2. **Performance Testing**: Compare against current implementation
3. **User Testing**: Validate syntax with actual users
4. **Documentation Update**: Revise user guides for new syntax capabilities

---

**Conclusion:** luqum provides the best balance of features, maturity, and industry alignment for our boolean search implementation. The package-based approach will reduce maintenance burden while providing more robust parsing capabilities than our custom implementation.

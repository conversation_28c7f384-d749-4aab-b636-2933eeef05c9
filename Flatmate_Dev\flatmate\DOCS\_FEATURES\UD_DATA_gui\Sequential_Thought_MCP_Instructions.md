# Sequential Thought MCP Server: Setup & Management Guide

## Overview
This guide explains how to set up, manage, and automate the Sequential Thought MCP server on Windows 10 using PM2.

## Initial Setup

### 1. Install Required Packages

```bash
# Install the Sequential Thought MCP server globally
npm install -g @modelcontextprotocol/server-sequential-thinking

# Install PM2 globally if not already installed
npm install -g pm2
```

### 2. Server Files
The following files have been created to manage the server:

- **`start-sequential-thought.js`**: Node.js script that launches the server
- **`sequential-thought.config.js`**: PM2 configuration file

Both files are located in: `c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\`

## Managing the Server

### Start the Server
```bash
# Navigate to the directory containing the config file
cd c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate

# Start the server using PM2
pm2 start sequential-thought.config.js
```

### Check Server Status
```bash
pm2 status
```

### View Server Logs
```bash
pm2 logs sequential-thought-mcp
```

### Stop the Server
```bash
pm2 stop sequential-thought-mcp
```

### Restart the Server
```bash
pm2 restart sequential-thought-mcp
```

### Delete the Server from PM2
```bash
pm2 delete sequential-thought-mcp
```

## Auto-start Configuration

### Configure PM2 to Start on Boot
```bash
# Save the current PM2 process list
pm2 save

# Generate startup script and follow instructions
pm2 startup
```

### Auto-start with Windsurf
A script has been created to automatically check if the Sequential Thought MCP server is running and start it if needed:

- **`check-and-start-sequential-thought.js`**: Located in `c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/`

To run this script when Windsurf starts:

1. Create a batch file to run the script:

```bash
cat > ~/windsurf-startup.bat << 'EOL'
@echo off
node "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\check-and-start-sequential-thought.js"
EOL
```

2. Configure this batch file to run when Windows starts or when you start Windsurf.

### Configure Windsurf to Connect to the Server
Edit your `mcp_config.json` file to include:

```json
{
  "mcpServers": {
    // Other servers...
  },
  "servers": [
    {
      "name": "sequential-thought",
      "endpoint": "http://localhost:4000",
      "type": "node",
      "description": "Sequential Thought MCP server running locally"
    }
  ]
}
```

## Troubleshooting

### Server Won't Start
1. Check if the server is already running:
   ```bash
   pm2 status
   ```

2. Check for errors in the logs:
   ```bash
   pm2 logs sequential-thought-mcp
   ```

3. Try restarting the server:
   ```bash
   pm2 restart sequential-thought-mcp
   ```

4. If all else fails, delete and restart:
   ```bash
   pm2 delete sequential-thought-mcp
   pm2 start sequential-thought.config.js
   ```

### Verify Server is Running
The Sequential Thought MCP server runs on port 4000 by default. To verify it's running:

```bash
curl http://localhost:4000/resources
```

You should see a JSON response listing available resources.

## Running from Any Directory
To run PM2 commands from any directory, always use the full path to the config file:

```bash
pm2 start c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/sequential-thought.config.js
```

Or create an alias in your `.bashrc` or `.bash_profile`:

```bash
echo 'alias start-st="pm2 start c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/sequential-thought.config.js"' >> ~/.bashrc
echo 'alias stop-st="pm2 stop sequential-thought-mcp"' >> ~/.bashrc
echo 'alias restart-st="pm2 restart sequential-thought-mcp"' >> ~/.bashrc
source ~/.bashrc
```

Then you can simply use:
```bash
start-st  # Start the server
stop-st   # Stop the server
restart-st # Restart the server
```

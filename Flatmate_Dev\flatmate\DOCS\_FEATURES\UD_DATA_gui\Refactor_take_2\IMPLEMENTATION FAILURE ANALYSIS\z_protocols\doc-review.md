-a simple reusable protocol for document review.


All comments made by will be marked with `>>`

I'm the boss, (the user, ceo, PM and lead dev)
I'm also still learning.
address my concerns and update the document
or reply in line to my comments
make suggestions
as appropriate ...
*DO NOT make speculative claims, or assumptions if you have low confidense in a repsonse, dont guess* - suggest a testable hypothesis. 
Unless other<PERSON><PERSON> directed. 

All requests for reports, responses, analysis 
can be presumed to be required an appropriately named 
document. 
If they are new version of a document, version them 
<name_od_document>_v2.md 
*etc*
when filing reports, prefix with compact iso <date>_<name>.md
eg 25725_doc-review.md (strip leading 0's from date)

Keep folders tidy and organised. 


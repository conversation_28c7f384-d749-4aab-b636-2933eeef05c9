# Export Functionality Tasks

## Implementation Steps (Phase 1)

### Task 1: Implement `get_visible_dataframe()` Method
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
**Location**: Add after line 367 (after existing `get_dataframe()` method)
**Dependencies**: None
**Estimated Time**: 20 minutes

**Method Signature**:
```python
def get_visible_dataframe(self) -> pd.DataFrame:
    """Get the currently visible data as a pandas DataFrame.

    This method respects all active filters and sorting applied through
    the proxy model, returning exactly what the user sees in the table.

    Returns:
        pd.DataFrame: The filtered and sorted data as displayed
    """
```

**Implementation Requirements**:
1. Access the proxy model: `self.model()` (returns `EnhancedFilterProxyModel`)
2. Get source model: `self.model().sourceModel()` (returns `EnhancedTableModel`)
3. Iterate through proxy model rows: `range(self.model().rowCount())`
4. Map proxy rows to source rows: `self.model().mapToSource(proxy_index)`
5. Extract data respecting column visibility: `self.isColumnHidden(col)`
6. Build DataFrame with visible data in display order

**Code Template**:
```python
def get_visible_dataframe(self) -> pd.DataFrame:
    """Get the currently visible data as a pandas DataFrame."""
    import pandas as pd

    proxy_model = self.model()  # EnhancedFilterProxyModel
    source_model = proxy_model.sourceModel()  # EnhancedTableModel

    if proxy_model.rowCount() == 0:
        return pd.DataFrame()

    # Get visible columns and their names
    visible_columns = []
    column_names = []
    for col in range(source_model.columnCount()):
        if not self.isColumnHidden(col):
            visible_columns.append(col)
            column_names.append(source_model.headerData(col, Qt.Horizontal))

    # Extract visible data in display order
    data_rows = []
    for proxy_row in range(proxy_model.rowCount()):
        row_data = []
        for col in visible_columns:
            proxy_index = proxy_model.index(proxy_row, col)
            source_index = proxy_model.mapToSource(proxy_index)
            value = source_model.data(source_index, Qt.DisplayRole)
            row_data.append(value)
        data_rows.append(row_data)

    return pd.DataFrame(data_rows, columns=column_names)
```

**Testing**:
- Test with no filters (should return all visible data)
- Test with text filters applied
- Test with sorting applied
- Test with hidden columns
- Test with empty result set

---

### Task 2: Update `_export_data()` Method
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
**Location**: Lines 309-325 (existing method)
**Dependencies**: Task 1 (get_visible_dataframe method)
**Estimated Time**: 10 minutes

**Current Code** (Line 314):
```python
df = self.get_dataframe()
```

**New Code**:
```python
df = self.get_visible_dataframe()
```

**Additional Error Handling**:
Add after line 314:
```python
if df.empty:
    from PySide6.QtWidgets import QMessageBox
    QMessageBox.information(self, "Export", "No data to export. Please check your filters.")
    return
```

**Full Updated Method**:
```python
def _export_data(self, format_type):
    """Export data to file."""
    from PySide6.QtWidgets import QFileDialog, QMessageBox

    # Get visible DataFrame (respects filters and sorting)
    df = self.get_visible_dataframe()

    # Check if there's data to export
    if df.empty:
        QMessageBox.information(self, "Export", "No data to export. Please check your filters.")
        return

    try:
        if format_type == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to CSV", "", "CSV Files (*.csv)")
            if file_path:
                df.to_csv(file_path, index=False)
                QMessageBox.information(self, "Export", f"Data exported successfully to {file_path}")
        elif format_type == "excel":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to Excel", "", "Excel Files (*.xlsx)")
            if file_path:
                df.to_excel(file_path, index=False)
                QMessageBox.information(self, "Export", f"Data exported successfully to {file_path}")
    except Exception as e:
        QMessageBox.critical(self, "Export Error", f"Failed to export data: {str(e)}")
        # Log error for debugging
        import logging
        logging.error(f"Export failed: {e}")
```

**Testing**:
- Test CSV export with filtered data
- Test Excel export with sorted data
- Test export with no visible data (empty result)
- Test file permission errors
- Test invalid file paths

---

### Task 3: Verify Signal Connections
**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/fm_table_view.py`
**Location**: Lines 345-346, 524-530
**Dependencies**: None
**Estimated Time**: 5 minutes

**Verification Steps**:
1. Confirm signal connections exist in `_connect_signals()` method (lines 345-346):
   ```python
   self.toolbar.csv_export_requested.connect(self._export_csv)
   self.toolbar.excel_export_requested.connect(self._export_excel)
   ```

2. Confirm handler methods exist (lines 524-530):
   ```python
   def _export_csv(self):
       """Handle CSV export request."""
       self.table_view._export_data("csv")

   def _export_excel(self):
       """Handle Excel export request."""
       self.table_view._export_data("excel")
   ```

**Expected Result**: No changes needed - connections already exist and are correct.

---

### Task 4: Integration Testing
**File**: Test in categorize module
**Location**: `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`
**Dependencies**: Tasks 1-3 completed
**Estimated Time**: 15 minutes

**Test Scenarios**:
1. **Basic Export**: Load categorize module, click export, verify CSV contains all visible data
2. **Filtered Export**: Apply date filter, export, verify only filtered data exported
3. **Sorted Export**: Sort by amount, export, verify data exported in sorted order
4. **Hidden Columns**: Hide balance column, export, verify balance not in export
5. **Empty Results**: Filter to show no results, export, verify user-friendly message
6. **Large Dataset**: Test with 1000+ transactions for performance

**Test Steps**:
1. Launch application
2. Navigate to categorize module
3. Load transaction data
4. Apply various filters and sorts
5. Click export button → CSV/Excel
6. Verify exported file matches visible table exactly

---

## Future Steps (Phase 2+)

### Task 5: Enhanced Export Options
- [ ] Add JSON export format
- [ ] Add export configuration dialog
- [ ] Add column selection for export
- [ ] Add date range export options

### Task 6: Advanced Features
- [ ] Export templates and presets
- [ ] Batch export functionality
- [ ] Export scheduling
- [ ] Export history tracking

---

## Validation Criteria

### Acceptance Tests:
1. **WYSIWYG Compliance**: Exported data must match exactly what is visible in the table
2. **Filter Respect**: All active filters must be applied to export
3. **Sort Respect**: Export data must be in the same order as displayed
4. **Column Visibility**: Hidden columns must not appear in export
5. **Error Handling**: Clear user messages for all error conditions
6. **Performance**: Export of 1000+ rows should complete within 5 seconds

### Success Metrics:
- [ ] Export matches visible table 100% of the time
- [ ] No silent failures or crashes
- [ ] User-friendly error messages for all failure modes
- [ ] Export completes within acceptable time limits

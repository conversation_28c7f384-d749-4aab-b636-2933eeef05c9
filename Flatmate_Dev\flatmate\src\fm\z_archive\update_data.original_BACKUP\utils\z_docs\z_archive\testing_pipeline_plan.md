# Testing Plan for Data Processing Pipeline

## 1. Objective

To create a standalone test script that validates the end-to-end functionality of the refactored `dw_director` and `dw_pipeline` modules. The test will ensure that the pipeline correctly processes, validates, and saves data, while also handling file operations (backups, cleanup) as expected.

The test will be modeled after `tests/test_real_csvs.py` and will be executable as a standalone script.

## 2. Test Setup and Environment

The test script will perform the following setup steps:

1.  **Create a Temporary Directory Structure**: A unique temporary directory will be created for each test run to isolate test artifacts. This directory will contain:
    *   `input/`: For placing sample CSV files.
    *   `output/`: Where the master file will be saved.
    *   `backup/`: Where original files will be backed up.

2.  **Prepare Test Data**: Sample CSV files will be created in the `input/` directory. These will include:
    *   A valid CSV file that can be processed by a known handler.
    *   An unrecognized file that no handler can process.
    *   A file with data that will fail the final validation step (e.g., missing critical columns).

3.  **Construct a `job_sheet`**: A `job_sheet` dictionary will be created to configure the `dw_director` call, specifying the input filepaths and save locations.

## 3. Test Scenarios and Assertions

The test will cover the following scenarios:

### Scenario 1: Successful End-to-End Run

*   **Action**: Call `dw_director` with a valid CSV and an unrecognized file.
*   **Assertions**:
    *   The director returns a `"status": "success"` response.
    *   The correct number of files are reported as processed and unrecognized.
    *   A master file is created in the `output/` directory.
    *   The original processed file is moved to the `backup/` directory and then deleted from `input/`.
    *   The unrecognized file is moved to the `input/unrecognised/` directory.

### Scenario 2: Data Validation Failure

*   **Action**: Call `dw_director` with a file containing invalid data (e.g., nulls in the 'Amount' column).
*   **Assertions**:
    *   The director returns a `"status": "error"` response.
    *   The error message clearly indicates a validation failure.
    *   No master file is created.

## 4. Implementation Plan

1.  Create a new test file: `tests/test_dw_pipeline.py`.
2.  Implement the setup and teardown logic to manage the temporary directory.
3.  Write test functions for each scenario described above.
4.  Use standard `assert` statements for validation.

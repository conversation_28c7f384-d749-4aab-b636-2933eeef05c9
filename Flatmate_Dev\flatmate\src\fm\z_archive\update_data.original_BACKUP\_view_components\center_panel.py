"""Center panel manager for the Update Data module."""

import pandas as pd
from fm.core.services.event_bus import Events, global_event_bus
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QVBoxLayout

# Temporarily comment out to break circular import
# from fm.gui._shared_components import BasePanelComponent
from PySide6.QtWidgets import QWidget
# Temporarily comment out to break circular import chain
# from .center_panel_components._switcher import PanelSwitcher
# from .center_panel_components.data_pane import DataPane
# from .center_panel_components.file_pane import FilePane
# from .center_panel_components.welcome_pane import WelcomePane
# from .center_panel_components.widgets.ud_status_bar import UpdateDataStatusBar


class CenterPanelManager(QWidget):
    """Main center panel manager for the Update Data module.

    This class uses the Composite pattern to manage different panes
    that can be shown in the center area of the Update Data module.
    """

    # Signals for publishing events to subscribers
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file

    # Pane identifiers
    WELCOME_PANE = "welcome"
    FILE_PANE = "file"
    DATA_PANE = "data"

    def __init__(self, parent=None):
        """Initialize the center panel manager."""
        super().__init__(parent)
        self.info_bar = None
        self.event_bus = global_event_bus
        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # TODO: Temporarily simplified to break circular imports
        # self.pane_switcher = PanelSwitcher(self)
        # self.main_layout.addWidget(self.pane_switcher)
        # self.status_bar = UpdateDataStatusBar()
        # self.main_layout.addWidget(self.status_bar)
        # self._create_panes()

        # TODO: Temporarily commented out - pane_switcher not created
        # self.pane_switcher.show_component(self.WELCOME_PANE)

    def _create_panes(self):
        """Create and register all pane components."""
        # TODO: Temporarily commented out to break circular imports
        # self.welcome_pane = WelcomePane()
        # self.pane_switcher.add_component(self.WELCOME_PANE, self.welcome_pane)
        # self.file_pane = FilePane()
        # self.pane_switcher.add_component(self.FILE_PANE, self.file_pane)
        # self.data_pane = DataPane()
        # self.pane_switcher.add_component(self.DATA_PANE, self.data_pane)
        pass

    def _connect_signals(self):
        """Connect signals between components."""
        # TODO: Temporarily commented out - panes not created
        # self.file_pane.publish_file_removed.connect(self.publish_file_removed.emit)
        # self.file_pane.publish_file_selected.connect(self.publish_file_selected.emit)
        pass

    def set_source_path(self, path: str):
        """Set the source folder path."""
        # Access the file pane directly
        self.file_pane.set_source_path(path)

    def set_save_path(self, path: str):
        """Set the save location path."""
        # Access the file pane directly
        self.file_pane.set_save_path(path)

    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.

        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        # Access the file pane directly
        self.file_pane.set_files(files, source_dir)
        # Show the file pane
        self.show_file_pane()

    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        return self.file_pane.get_files()

    def display_master_csv(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in a table.

        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        # Access the data pane directly
        self.data_pane.display_dataframe(df, file_info)
        # Show the data pane
        self.show_data_pane()

    def display_welcome(self):
        """Display welcome message."""
        # Show the welcome pane
        self.show_welcome_pane()

        # Clear file pane
        self.file_pane.clear()

    def show_error(self, message: str):
        """Show error message."""
        # Pass to current pane if it supports it
        current_component = self.pane_switcher.get_current_component()
        if current_component is not None:
            current_component.show_error(message)

        # Also publish to event bus for InfoBar to pick up
        self.event_bus.publish(Events.INFO_MESSAGE, f"Error: {message}")

    def show_success(self, message: str):
        """Show success message."""
        # Pass to current pane if it supports it
        current_component = self.pane_switcher.get_current_component()
        if current_component is not None:
            current_component.show_success(message)

        # Also publish to event bus for InfoBar to pick up
        self.event_bus.publish(Events.INFO_MESSAGE, message)

    def show_welcome_pane(self):
        """Show the welcome pane."""
        self.pane_switcher.show_component(self.WELCOME_PANE)

    def show_file_pane(self):
        """Show the file display pane."""
        self.pane_switcher.show_component(self.FILE_PANE)

    def show_data_pane(self):
        """Show the data display pane."""
        self.pane_switcher.show_component(self.DATA_PANE)

    def get_current_pane(self):
        """Get the currently displayed pane."""
        return self.pane_switcher.get_current_component_id()

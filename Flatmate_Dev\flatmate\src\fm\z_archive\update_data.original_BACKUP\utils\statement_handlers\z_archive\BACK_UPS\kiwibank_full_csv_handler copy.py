from dataclasses import dataclass
from pathlib import Path
import re

import pandas as pd

from ._base_statement_handler import StatementHandler, StatementError
from fm.core.data_services.standards.fm_standard_columns import StandardColumns


@dataclass
class KiwibankFullCSVHandler(StatementHandler):
    """Handler for Kiwibank's full CSV statement format.
    
    This format includes detailed transaction information with many columns.
    Account numbers are expected to be in the first column of data.
    """
    
    def __init__(self):
        super().__init__()
        self.statement_format = self.StatementFormat(
            bank_name="Kiwibank",
            variant="full",
            file_type="csv",
        )
        self.column_attrs = self.ColumnAttributes(
            has_col_names=True,  # Validate header names for full CSV
            has_account_column=True,  # Account number is in source data
            col_names_in_header=True,  # Column names are in the header section
            source_col_names=[
                "Account number",  
                "Date",
                "Memo/Description",
                "Source Code (payment type)",
                "TP ref",
                "TP part",
                "TP code",
                "OP ref",
                "OP part", 
                "OP code",
                "OP name",
                "OP Bank Account Number",
                "Amount (credit)",
                "Amount (debit)",
                "Amount",
                "Balance"
            ],
            target_col_names=[
                StandardColumns.ACCOUNT,
                StandardColumns.DATE,
                StandardColumns.DETAILS,
                StandardColumns.PAYMENT_TYPE,
                StandardColumns.TP_REF,
                StandardColumns.TP_PART,
                StandardColumns.TP_CODE,
                StandardColumns.OP_REF,
                StandardColumns.OP_PART,
                StandardColumns.OP_CODE,
                StandardColumns.OP_NAME,  
                StandardColumns.OP_ACCOUNT,
                StandardColumns.CREDIT_AMOUNT,
                StandardColumns.DEBIT_AMOUNT,
                StandardColumns.AMOUNT,
                StandardColumns.BALANCE
            ],
            n_source_cols=16,
            date_format='%d/%m/%y',  # Kiwibank Full CSV uses DD/MM/YY format (e.g., '18/08/24')
        )
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"38-?\d{4}-?\d{7}-?\d{2,3}.*",  # Match 2-3 digits at end
            in_data=True,  # Check first row data
            in_header=False,  # Don't check column names
            location=(0, 0),  # First row, first column
        )
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=False
        )
    
    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if this handler can process the given file.
        
        For Kiwibank Full CSV, we check:
        1. File extension is .csv (case-insensitive)
        2. File has the expected number of columns (16)
        3. First column contains valid account numbers
        4. Second column contains valid dates
        """
        # Create a temporary instance to access the logger and other instance attributes
        temp_instance = cls()
        
        with temp_instance.handle_errors(f"Checking if {filepath} can be handled by {cls.__name__}"):
            filename = Path(filepath).name
            
            # Check file extension (case-insensitive)
            if not filepath.lower().endswith('.csv'):
                raise StatementError("File is not a CSV", is_validation=True)
            
            try:
                # Read just the headers and first few rows to validate format
                df = pd.read_csv(filepath, nrows=5)
            except Exception as e:
                raise StatementError(f"Error reading file: {str(e)}", is_validation=True)
            
            # Check number of columns (should be 16 for Kiwibank full CSV)
            if len(df.columns) != 16:
                raise StatementError(
                    f"Expected 16 columns, got {len(df.columns)}",
                    is_validation=True
                )
            
            # Check if first column looks like an account number
            if len(df) > 0 and not df.iloc[0, 0].startswith('38'):
                first_value = str(df.iloc[0, 0])
                raise StatementError(
                    f"First column doesn't start with account number (38...), got: {first_value}",
                    is_validation=True
                )
            
            # Check if second column looks like a date (DD/MM/YY format)
            if len(df) > 0 and not re.match(r'\d{2}/\d{2}/\d{2}', str(df.iloc[0, 1])):
                date_value = str(df.iloc[0, 1])
                raise StatementError(
                    f"Second column doesn't match date format (DD/MM/YY): {date_value}",
                    is_validation=True
                )
            
            temp_instance._logger.debug(f"Successfully validated {filename} as Kiwibank full CSV")
            return True
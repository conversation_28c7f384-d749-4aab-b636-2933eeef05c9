import re
from dataclasses import dataclass
from typing import ClassVar

import pandas as pd

from ._base_statement_handler import StatementHandler
from fm.core.data_services.standards.fm_standard_columns import StandardColumns


@dataclass
class CoopStandardCSVHandler(StatementHandler):
    # Class level attributes
    name: ClassVar[str] = "Co-op Bank Standard CSV"
    file_extension: ClassVar[str] = ".csv"

    def __init__(self):
        super().__init__()
        # Column mapping
        self.column_attrs = self.ColumnAttributes(
            has_col_names=True,  # File has column names
            has_account_column=False,  # Account number is in filename
            col_names_in_header=True,  # Column names are in the header section
            source_col_names=["Date", "Details", "Amount", "Balance"],
            target_col_names=[
                StandardColumns.DATE,
                StandardColumns.DETAILS,
                StandardColumns.AMOUNT,
                StandardColumns.BALANCE,
            ],
            n_source_cols=4,  # Must have Date, Details, Amount, Balance
            date_format='%d/%m/%Y',  # Co-op uses DD/MM/YYYY format (verified with example files)
        )

        # ? add:
        # self.metadata_cols = processing_tracker.metadata_column_tracker.get()
        #  self.n_expected_cols = self.column_attrs.n_source_cols + len(self.metadata_cols)

        #
        # Account number is in filename - this is critical for Co-op statements
        # Format: 02-XXXX-XXXXXXX-XXX_other_info.csv
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"^\d{2}-\d{4}-\d{7}-\d{3}",  # Match Co-op account number at start of filename
            in_file_name=True  # Must be in filename
        )
        
        # Store the full pattern for validation in can_handle_file
        self._filename_pattern = r'^\d{2}-\d{4}-\d{7}-\d{3}.*\.csv$'  # Full filename pattern for validation

        # No metadata rows in the file
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=False
        )

        # Statement type
        self.statement_format = self.StatementFormat(
            bank_name="Co-operative Bank",
            variant="standard",
            file_type="csv",
        )

    # Using base class can_handle_file method

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract Co-op-specific account number from filename.

        Args:
            df: DataFrame containing the data with filepath attribute

        Returns:
            Account number as a string, or empty string if not found
        """
        # For Co-op handler, we know account number is always in the filename
        filename = getattr(df, 'filename', '')
        if not filename:
            self._logger.warning("No filename found in DataFrame attributes")
            return ""
            
        # Extract account number from filename (first 17 characters: 02-XXXX-XXXXXXX-XXX)
        match = re.match(r'^(\d{2}-\d{4}-\d{7}-\d{3})', filename)
        if match:
            return match.group(1)
            
        logger.warning(f"Could not extract account number from filename: {filename}")
        return ""

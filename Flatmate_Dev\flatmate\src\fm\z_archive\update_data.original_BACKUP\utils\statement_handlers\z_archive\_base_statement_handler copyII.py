"""Base class for bank statement type maps."""

import os.path
import re
from abc import ABC
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime

from typing import List, Tuple, Generator, Type, ClassVar

import pandas as pd

from fm.core.data_services.standards.fm_standard_columns import StandardColumns
from fm.core.utils.date_utils import standardize_date 
from fm.core.services.logger import Logger
from fm.core.data_services.standards.fm_standard_columns import StandardColumns

@dataclass
class StatementType:
    """Configuration for statement format identification."""
    bank_name: str  # e.g., "Kiwibank"
    variant: str    # e.g., "basic"
    file_type: str  # e.g., "csv"
    
    def validate(self):
        if not self.bank_name or not isinstance(self.bank_name, str):
            raise ValueError("bank_name must be a non-empty string")
        if not self.variant or not isinstance(self.variant, str):
            raise ValueError("variant must be a non-empty string")
        if not self.file_type or not isinstance(self.file_type, str):
            raise ValueError("file_type must be a non-empty string")


@dataclass
class ColumnAttributes:
    """Configuration for column mapping and formatting."""
    # Required fields
    has_col_names: bool
    colnames_in_header: bool
    n_source_cols: int
    date_format: str
    
    col_names_row: int = 0
    has_account_column: bool = False
    
    source_col_names: List[str] = field(default_factory=list)
    target_col_names: List['StandardColumns'] = field(default_factory=list)
    file_encoding: str = 'utf-8'
    
    def validate(self):
        """Validate column attributes."""
        if not self.date_format or not isinstance(self.date_format, str):
            raise ValueError("date_format must be a non-empty string")
        if not isinstance(self.n_source_cols, int) or self.n_source_cols <= 0:
            raise ValueError("n_source_cols must be a positive integer")
        if not isinstance(self.col_names_row, int) or self.col_names_row < 0:
            raise ValueError("col_names_row must be a non-negative integer")
        if not isinstance(self.colnames_in_header, bool):
            raise ValueError("colnames_in_header must be a boolean")
        if not isinstance(self.has_col_names, bool):
            raise ValueError("has_col_names must be a boolean")
        if self.has_col_names and not self.source_col_names:
            raise ValueError("source_col_names must be provided when has_col_names is True")
        if self.colnames_in_header and self.col_names_in_data_row:
            raise ValueError("Cannot have both colnames_in_header and col_names_in_data_row set to True")
        if self.source_col_names and len(self.source_col_names) != self.n_source_cols:
            raise ValueError(f"source_col_names length ({len(self.source_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if self.target_col_names and len(self.target_col_names) != self.n_source_cols:
            raise ValueError(f"target_col_names length ({len(self.target_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if not isinstance(self.skip_rows, int) or self.skip_rows < 0:
            raise ValueError("skip_rows must be a non-negative integer")
        if not isinstance(self.file_encoding, str) or not self.file_encoding:
            raise ValueError("file_encoding must be a non-empty string")
            


@dataclass
class AccountNumberAttributes:
    """Configuration for account number extraction."""
    pattern: str = ""
    in_data: bool = False
    location: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    in_file_name: bool = False
    in_metadata: bool = False
    
    def validate(self):
        # Check for at least one location flag set
        location_flags = [self.in_data, self.in_file_name, self.in_metadata]
        if not any(location_flags):
            raise ValueError("At least one account location (in_data, in_file_name, in_metadata) must be True")
            
        if any(location_flags) and not self.pattern:
            raise ValueError("pattern is required when any account location is specified")
            
        # Validate location coordinates
        if not isinstance(self.location, tuple) or len(self.location) != 2:
            raise ValueError("location must be a tuple of two integers")
        row, col = self.location
        if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
            raise ValueError("location coordinates must be non-negative integers")


@dataclass
class SourceMetadataAttributes:
    """Configuration for source metadata handling."""
    has_metadata_rows: bool = False
    metadata_start: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    metadata_end: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    
    def validate(self):
        if not isinstance(self.has_metadata_rows, bool):
            raise ValueError("has_metadata_rows must be a boolean")
            
        # Validate coordinates
        for name, coord in [("metadata_start", self.metadata_start), 
                          ("metadata_end", self.metadata_end)]:
            if not isinstance(coord, tuple) or len(coord) != 2:
                raise ValueError(f"{name} must be a tuple of two integers")
            row, col = coord
            if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
                raise ValueError(f"{name} coordinates must be non-negative integers")
        
        if self.has_metadata_rows and self.metadata_start > self.metadata_end:
            raise ValueError("metadata_start must not be after metadata_end")

@dataclass
class StatementAttributes:
    """Unified configuration for statement handling.
    
    Groups all statement-related attributes into a single class for better organization
    and enforces validation rules across all attributes.
    """
    statement_type: StatementType
    columns: ColumnAttributes
    account: AccountNumberAttributes
    metadata: SourceMetadataAttributes
    
    
    def validate(self):
        """Validate all statement attributes and their relationships."""
        # Validate all components
        self.statement_type.validate()
        self.columns.validate()
        self.account.validate()
        self.metadata.validate()        
    


class StatementHandler(ABC):
    """Base class for all statement handlers.
    
    Subclasses must define:
    - statement_type: StatementType
    - columns: ColumnAttributes
    - account: AccountNumberAttributes
    - metadata: SourceMetadataAttributes
    
    These will be automatically validated on initialization.
    """
    
    def __init__(self):
        """Initializes the handler and validates its configuration."""
        self.attributes = StatementAttributes(
            statement_type=self.statement_type,
            columns=self.columns,
            account=self.account,
            metadata=self.metadata,
        )
        self.attributes.validate()
        Logger.debug(f"Initialized {self.__class__.__name__}", module=self.__class__.__name__)

    @contextmanager
    def handle_errors(self, context: str = "") -> Generator[None, None, None]:
        """Context manager for consistent error handling and logging."""
        try:
            yield
        except StatementError as e:
            message = f"{context}: {str(e)}"
            if e.is_validation:
                Logger.warning(message, module=self.__class__.__name__)
            else:
                Logger.error(message, module=self.__class__.__name__)
            raise
        except Exception as e:
            message = f"Unexpected error in {self.__class__.__name__} {context}: {str(e)}"
            Logger.error(message, module=self.__class__.__name__)
            raise StatementError(message) from e

    # Type hints for statement attributes
    StatementType: ClassVar[Type[StatementType]] = StatementType
    
    # Instance attributes
    attributes: StatementAttributes
    
    # Backwards compatibility properties
    @property
    def column_attrs(self):
        return self.columns
    
    @property
    def account_num_attrs(self):
        return self.account
    
    @property
    def source_metadata_attrs(self):
        return self.metadata

    # --- Core Internal Helpers ---

    def _read_csv(self, filepath: str, nrows: Optional[int] = None) -> Optional[pd.DataFrame]:
        """Centralized method to read CSV files using handler-specific configuration."""
        try:
            col_attrs = self.columns
            # Use colnames_in_header to decide if the file has a header row.
            # If True, use the specified row index. If False, read without a header.
            header_arg = col_attrs.col_names_row if col_attrs.colnames_in_header else None

            df = pd.read_csv(
                filepath,
                header=header_arg,
                nrows=nrows,
                on_bad_lines='skip',
                engine='python',
                encoding=col_attrs.file_encoding,
                sep=','
            )
            return df
        except (FileNotFoundError, pd.errors.EmptyDataError) as e:
            Logger.warning(f"File is empty, not found, or unreadable: {filepath} ({e})")
            return None
        except Exception as e:
            Logger.error(f"Unexpected error reading {filepath}: {e}")
            return None

    def _read_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads the full statement file into a DataFrame using the centralized _read_csv method."""
        return self._read_csv(filepath, nrows=None)

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract account number using handler configuration."""
        if hasattr(self, 'account'):
            attrs = self.account
            
            # Method 1: From metadata
            if hasattr(attrs, 'in_metadata') and attrs.in_metadata:
                try:
                    row, col = attrs.location
                    account_line = str(df.iloc[row, col])
                    if hasattr(attrs, 'pattern') and attrs.pattern:
                        import re
                        match = re.search(attrs.pattern, account_line)
                        if match:
                            return match.group(1) if match.groups() else match.group(0)
                    else: # No pattern, return the whole cell content
                        return account_line.strip()
                except (IndexError, AttributeError, KeyError) as e:
                    Logger.warning(f"Could not find account number in metadata: {e}")

            # Method 2: From data column
            if hasattr(attrs, 'in_data') and attrs.in_data:
                try:
                    col_name = attrs.location[1]
                    if col_name in df.columns:
                        # Find first non-null value that matches pattern
                        for item in df[col_name].dropna():
                            item_str = str(item)
                            if hasattr(attrs, 'pattern') and attrs.pattern:
                                import re
                                match = re.search(attrs.pattern, item_str)
                                if match:
                                    return match.group(1) if match.groups() else match.group(0)
                except (IndexError, KeyError) as e:
                    Logger.warning(f"Could not find account number in data column '{attrs.location[1]}': {e}")

            # Method 3: From filename
            if hasattr(attrs, 'in_file_name') and attrs.in_file_name:
                try:
                    import re
                    from pathlib import Path
                    if hasattr(self, '_current_filepath'):
                        filename = Path(self._current_filepath).stem
                        if hasattr(attrs, 'pattern') and attrs.pattern:
                            match = re.search(attrs.pattern, filename)
                            if match:
                                return match.group(1) if match.groups() else match.group(0)
                        else: # No pattern, return the whole filename
                            return filename
                except Exception as e:
                    Logger.warning(f"Could not extract account number from filename: {e}")
        
        return ""

    def _standardize_dates(self, df: pd.DataFrame) -> None:
        """Standardize dates in the DataFrame to ISO format."""
        handler_name = self.__class__.__name__
        date_col = StandardColumns.DATE.value
        date_format = self.columns.date_format
        
        if not date_format:
            raise ValueError(f"{handler_name} must specify date_format in ColumnAttributes")

        if date_col not in df.columns:
            Logger.debug(f"[{handler_name}] No date column found for standardization")
            return

        null_before = df[date_col].isna().sum()
        
        df[date_col] = pd.to_datetime(df[date_col], format=date_format, errors='coerce')

        null_after = df[date_col].isna().sum()
        if null_after > null_before:
            Logger.debug(
                f"[{handler_name}] WARNING: {null_after - null_before} dates could not be converted to ISO format"
            )

    def _create_details_from_columns(self, df: pd.DataFrame, columns: list[str]) -> None:
        """Create a details column by concatenating specified columns."""
        existing_cols = [col for col in columns if col in df.columns]
        if not existing_cols:
            Logger.warning("No valid columns provided for details creation")
            return
            
        df[StandardColumns.DETAILS.value] = df[existing_cols].apply(
            lambda x: ' '.join(str(val) for val in x if pd.notna(val) and str(val).strip()),
            axis=1
        )
        Logger.info(f"Created details column from: {', '.join(existing_cols)}")

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns based on StandardColumns enum order, if they exist"""
        standard_order = [fmt.value for fmt in StandardColumns]
        ordered_cols = [col for col in standard_order if col in df.columns]
        remaining_cols = [col for col in df.columns if col not in ordered_cols]
        return df.reindex(columns=ordered_cols + remaining_cols)

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Hook for handlers to perform bank-specific formatting."""
        return df

    # --- Internal Orchestration ---

    def _format_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardizes the format of the input DataFrame based on handler configuration."""
        account_number = self._extract_account_number(df)

        data_start_row = 0
        if self.metadata.has_metadata_rows:
            data_start_row = self.metadata.metadata_end[0] + 1

        if self.columns.col_names_in_data_row:
            header_row_abs = self.columns.col_names_row
            temp_df = df.iloc[data_start_row:].reset_index(drop=True)
            header_row_rel = header_row_abs - data_start_row
            
            temp_df.columns = temp_df.iloc[header_row_rel]
            df = temp_df.iloc[header_row_rel + 1:].reset_index(drop=True)
        elif self.metadata.has_metadata_rows:
            df = df.iloc[data_start_row:].reset_index(drop=True)

        df = df.rename(columns=self.columns.source_to_target_map)

        df[StandardColumns.SOURCE_FILENAME.value] = os.path.basename(self._current_filepath)

        if not self.columns.has_account_column and account_number:
            df[StandardColumns.ACCOUNT.value] = account_number

        self._standardize_dates(df)
        df = self._custom_format(df)

        if StandardColumns.EMPTY_COLUMN.value in df.columns:
            df = df.drop(columns=[StandardColumns.EMPTY_COLUMN.value])

        if StandardColumns.DETAILS.value not in df.columns:
            common_detail_cols = ['Description', 'Narrative', 'Particulars', 'Payee', 'Memo']
            self._create_details_from_columns(df, common_detail_cols)

        df = self._reorder_columns(df)
        return df

# --- Public API ---

    @classmethod
    def can_handle_file(
        cls, 
        filepath: str, 
        *,
        require_filename_match: bool = False,
        require_columns: bool = True,
        require_account_number: bool = True,
    ) -> bool:
        """
        Lightweight, configuration-driven check to see if this handler can process the file.
        """
        handler_instance = None
        try:
            handler_instance = cls()
            col_attrs = handler_instance.columns
            acc_attrs = handler_instance.account

            if require_filename_match and hasattr(acc_attrs, 'in_file_name') and acc_attrs.in_file_name:
                if not re.search(acc_attrs.pattern, os.path.basename(filepath)):
                    return False

            rows_to_read = 20
            if col_attrs.has_col_names:
                rows_to_read = max(rows_to_read, col_attrs.col_names_row + 2)
            if hasattr(acc_attrs, 'in_metadata') and acc_attrs.in_metadata:
                rows_to_read = max(rows_to_read, acc_attrs.location[0] + 2)

            df_preview = handler_instance._read_csv(filepath, nrows=rows_to_read)
            if df_preview is None:
                return False

            if require_columns and col_attrs.has_col_names and col_attrs.source_col_names:
                try:
                    actual_headers = {str(h).strip() for h in df_preview.columns}
                    expected_headers = {str(h).strip() for h in col_attrs.source_col_names if h is not None}
                    if not expected_headers.issubset(actual_headers):
                        return False
                except Exception:
                    return False

            if require_account_number and hasattr(acc_attrs, 'pattern') and acc_attrs.pattern:
                if hasattr(acc_attrs, 'in_metadata') and acc_attrs.in_metadata:
                    try:
                        row, col = acc_attrs.location
                        cell_content = str(df_preview.iloc[row, col])
                        if not re.search(acc_attrs.pattern, cell_content):
                            return False
                    except IndexError:
                        return False
                elif hasattr(acc_attrs, 'in_data') and acc_attrs.in_data:
                    try:
                        col_name = acc_attrs.location[1]
                        if col_name in df_preview.columns:
                            if not df_preview[col_name].astype(str).str.contains(acc_attrs.pattern, regex=True, na=False).any():
                                return False
                        else:
                            return False # Column not in preview
                    except (IndexError, KeyError):
                        return False
                
            return True

        except (FileNotFoundError, pd.errors.EmptyDataError):
            return False
        except Exception as e:
            Logger.error(f"[{cls.__name__}] Unexpected error in can_handle_file for {filepath}: {e}", exc_info=True)
            return False

    def process_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads, formats, and validates a statement file."""
        self._current_filepath = filepath
        with self.handle_errors(f"processing file {os.path.basename(filepath)}"):
            df = self._read_file(filepath)
            if df is None or df.empty:
                Logger.warning(f"File is empty or could not be read: {filepath}", module=self.__class__.__name__)
                return None
                
            formatted_df = self._format_df(df)
            return formatted_df
# Sequential Thought MCP: Setup Guide for Windows 10

## 1. Prerequisites
- Node.js and npm installed globally
- Git Bash terminal (recommended) or Command Prompt
- Windsurf or other MCP-compatible client

## 2. Install the MCP Server

```bash
npm install -g @modelcontextprotocol/server-sequential-thinking
```

## 3. Start the MCP Server

Open a terminal and run:

```bash
npx @modelcontextprotocol/server-sequential-thinking
```

- The server will start on port 4000 by default
- Keep this terminal window open while using the MCP
- You'll see server logs in this window

## 4. Configure Windsurf

Edit your Windsurf configuration file:

```
C:\Users\<USER>\.codeium\windsurf\mcp_config.json
```

Add the following to your configuration:

```json
{
  "mcpServers": {
    // Your existing mcpServers entries here...
  },
  "servers": [
    {
      "name": "sequential-thought",
      "endpoint": "http://localhost:4000",
      "type": "node",
      "description": "Sequential Thought MCP server running locally"
    }
  ]
}
```

**Note:** If you already have a `servers` array, just add the new object to it.

## 5. Restart Windsurf

- Save the config file
- Restart <PERSON> completely
- The Sequential Thought MCP should now appear in your available tools

## Troubleshooting

- If Windsurf can't connect, verify the server is running in your terminal
- Check that port 4000 isn't being used by another application
- Ensure your config file has valid JSON syntax (no trailing commas)



---

## Running with PM2 (Recommended)

PM2 allows you to run the Sequential Thought MCP server as a background process that automatically restarts if it crashes.

### 1. Start the server with PM2 (Recommended)

1. First, create a PM2 configuration file `sequential-thought.config.js` in your project directory with the following content:

```javascript
module.exports = {
  apps: [{
    name: "sequential-thought-mcp",
    script: 'npx',
    args: '@modelcontextprotocol/server-sequential-thinking',
    interpreter: 'none',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

2. Make sure the package is installed globally:
```bash
npm install -g @modelcontextprotocol/server-sequential-thinking
```

3. Start the server using the PM2 configuration:
```bash
pm2 start sequential-thought.config.js
```

4. Save the PM2 process list to start on system boot:
```bash
pm2 save
pm2 startup
```

### 2. Useful PM2 commands

```bash
# View running processes
pm2 list

# View logs
pm2 logs sequential-thought-mcp

# Monitor processes
pm2 monit

# Stop the server
pm2 stop sequential-thought-mcp

# Restart the server
pm2 restart sequential-thought-mcp

# Remove from PM2
pm2 delete sequential-thought-mcp
```

### 3. Setup automatic startup on boot

```bash
# Generate startup script
pm2 startup

# Save current processes to restore on boot
pm2 save
```

---

*For troubleshooting or advanced configuration, refer to the official Sequential Thought MCP documentation or Windsurf MCP server guides.*

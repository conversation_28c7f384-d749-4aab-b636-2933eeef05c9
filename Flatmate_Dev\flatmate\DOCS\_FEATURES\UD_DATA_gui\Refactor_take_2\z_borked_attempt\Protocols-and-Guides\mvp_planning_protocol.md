# MVP Planning Protocol: One-Person AI Team

**Context**: One-person AI-assisted dev team, zero budget, MVP ASAP  
**User Role**: Lead dev + architect + PM (comments marked with `>>`)  
**AI Role**: Implementation assistant  

## 🎯 **KISS Principles**

- **MVP First**: Working code over perfect documentation
- **Rapid Iteration**: State tables + CSV for quick changes  
- **Existing Components**: Use what we have, avoid over-engineering
- **User-Driven**: AI assists, user decides
- **Yesterday Delivery**: Speed over perfection

## 🔄 **3-Phase Approach**

### **Phase 1: Initial Discovery (15 min)**
1. **User Requirements**: What needs to be built?
2. **Existing Code Review**: What can be reused?
3. **MVP Scope**: What's the absolute minimum?

### **Phase 2: Rapid Design (20 min)**
1. **State Schema**: CSV table defining UI behavior
2. **Component Mapping**: Map to existing widgets
3. **User Approval**: Quick review and sign-off

### **Phase 3: Implementation Prep (10 min)**
1. **Task Breakdown**: Simple, actionable steps
2. **Testing Strategy**: How to validate it works
3. **Handoff**: Clear next steps for development

## 📋 **Deliverables**

### set of develoment docs
- requirements
- design
- implementation
- tasks

### **Optional (If Time)**
- Design mockups
- Alternative approaches
- Future enhancement notes

## 🚀 **Success Criteria**

- **Clear Requirements**: User knows exactly what will be built
- **Actionable Plan**: Developer can start coding immediately
- **Realistic Scope**: Can be completed in available time
- **Testable**: Clear way to validate success

## ⚡ **Speed Optimizations**

- **Reuse Everything**: Existing widgets, patterns, code
- **CSV State Tables**: Quick to modify, easy to understand
- **Minimal Docs**: Just enough to enable development
- **User-Driven**: Skip AI speculation, follow user direction

---

**Goal**: From idea to implementation-ready in 45 minutes or less

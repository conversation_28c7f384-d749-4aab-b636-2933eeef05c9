# images from module views.

# State

current gui on first navigation after starting app
not first run  
databse loaded 
no new files in import folder 
left panel open

home module - the original bridgeing module

![home_module](image.png)


# update data 

![update_data](image/imagess_UX/1753098086479.png)

### Auto-import Folder Configuration

![auto_import](image/imagess_UX/1753098303968.png)

### Auto-import Folder Configuration -wide

![auto_import](image/imagess_UX/1753098303968.png)



![1753098496891](image/imagess_UX/1753098496891.png)

## categorise table_view

![categorise](image/imagess_UX/1753098163297.png)


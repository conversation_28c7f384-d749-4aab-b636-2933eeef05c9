#!/usr/bin/env node

/**
 * Sequential Thought MCP Server Auto-Starter
 * 
 * This script checks if the Sequential Thought MCP server is running
 * and starts it if it's not. It's designed to be run when Windsurf starts.
 */

const { execSync, spawn } = require('child_process');
const http = require('http');
const path = require('path');
const fs = require('fs');

// Log function with timestamps
function log(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

// Check if the server is already running by querying its endpoint
function isServerRunning() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:4000/resources', (res) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(2000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

// Check if the server is running in PM2
function isRunningInPM2() {
  try {
    const output = execSync('pm2 jlist').toString();
    const processes = JSON.parse(output);
    return processes.some(p => 
      p.name === 'sequential-thought-mcp' && 
      p.pm2_env.status === 'online'
    );
  } catch (error) {
    log(`Error checking PM2: ${error.message}`);
    return false;
  }
}

// Start the server using PM2
function startServer() {
  try {
    const configPath = path.resolve(__dirname, 'sequential-thought.config.js');
    
    if (!fs.existsSync(configPath)) {
      log(`Error: Config file not found at ${configPath}`);
      return false;
    }
    
    log('Starting Sequential Thought MCP server...');
    execSync(`pm2 start ${configPath}`);
    log('Server started successfully');
    return true;
  } catch (error) {
    log(`Error starting server: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  log('Checking if Sequential Thought MCP server is running...');
  
  const serverRunning = await isServerRunning();
  if (serverRunning) {
    log('Server is already running');
    return;
  }
  
  log('Server is not responding. Checking PM2...');
  const runningInPM2 = isRunningInPM2();
  
  if (runningInPM2) {
    log('Server is running in PM2 but not responding. Restarting...');
    try {
      execSync('pm2 restart sequential-thought-mcp');
      log('Server restarted');
    } catch (error) {
      log(`Error restarting server: ${error.message}`);
      startServer();
    }
  } else {
    log('Server is not running. Starting...');
    startServer();
  }
}

// Run the main function
main().catch(error => {
  log(`Unexpected error: ${error.message}`);
  process.exit(1);
});


from fm.core.data_services.standards.columns import Columns
from ._base_statement_handler import *

# ! NOTE: DO NOT TOUCH THIS HANDLER UNLESS EXPLCITLY ASKED

class KiwibankBasicCSVHandler(StatementHandler):
    """Handler for Kiwibank basic CSV statement format.
    
    Expected format:
    - First row: Account number
    - Data rows: Date, Details, [Empty], Amount, Balance
    - Date format: DD-MMM-YY (e.g., 13-Jun-24)
    - 5 columns total
    """
    statement_type = StatementType(
        bank_name="Kiwibank",
        variant="basic",
        file_type="csv"
    )
    
    columns = ColumnAttributes(
        has_col_names=False,
        colnames_in_header=False,
        data_start_row=1,
        n_source_cols=5,
        date_format="%d-%b-%y", # accurate 2025-07-12 
        source_col_names=[],  # No headers in source
        target_col_names=[
            Columns.DATE,
            Columns.DETAILS,
            Columns.EMPTY_COLUMN,
            Columns.AMOUNT,
            Columns.BALANCE
        ],
        has_account_column=False,
    )
    
    account = AccountNumberAttributes(
        in_metadata=True,
        location=(0, 0),
        pattern=r"(\d{2}-\d{4}-\d{7}-\d{2,3})"
    )
    
    metadata = SourceMetadataAttributes(
        has_metadata_rows=True,
        metadata_start=(0, 0),
        metadata_end=(0, 0)
    )

    # @classmethod
    # def can_handle_file(cls, filepath: str, **kwargs) -> bool:
    #     """Check if this handler can process the file by verifying column count and account number format."""
    #     try:
    #         # 1. Check for the expected number of columns (5 for this format)
    #         df_preview = pd.read_csv(filepath, header=None, nrows=1, encoding=cls.columns.file_encoding)
    #         if df_preview.shape[1] != cls.columns.n_source_cols:
    #             return False

    #         # 2. Check for a valid account number in the first row
    #         first_row_content = str(df_preview.iloc[0, 0])
    #         if not re.match(cls.account.pattern, first_row_content):
    #             return False

    #         return True
    #     except Exception:
    #         return False

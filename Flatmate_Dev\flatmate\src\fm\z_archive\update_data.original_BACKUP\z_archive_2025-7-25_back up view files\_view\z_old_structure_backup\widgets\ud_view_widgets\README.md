# Update Data Module View Widgets

This document explains the architecture and usage of the Update Data module's view components. It's designed to help novice developers understand how these components work together and how to extend the system.

## Overview

The Update Data module uses a **Composite Pattern** for UI components, creating a flexible and modular interface. This allows for:

- Easy addition of new UI panels
- Consistent error handling and user feedback
- Clear separation of concerns
- Simplified navigation between different views

## Core Components

### 1. BasePanelComponent

The foundation of our UI architecture is the `BasePanelComponent` class, which defines the interface that all panel components must implement:

```python
# From _base_panel_component.py
class BasePanelComponent(QWidget):
    """Base interface for panel components."""
    
    def show_component(self):
        """Show this component."""
        pass
    
    def hide_component(self):
        """Hide this component."""
        pass
    
    def show_error(self, message: str):
        """Show error message."""
        pass
    
    def show_success(self, message: str):
        """Show success message."""
        pass
```

**When to use**: Inherit from this class when creating any new panel for the Update Data module.

### 2. CompositePanel

The `CompositePanel` manages multiple panel components and allows switching between them:

```python
# From composite_panel.py
class CompositePanel(BasePanelComponent):
    """Composite container for multiple panel components."""
    
    def add_component(self, name: str, component: BasePanelComponent):
        """Add a component to this composite panel."""
        self.components[name] = component
        self.stack.addWidget(component)
    
    def show_component(self, name: str):
        """Show a specific component by name."""
        if name in self.components:
            component = self.components[name]
            self.stack.setCurrentWidget(component)
            component.show_component()  # Activate the component
```

**When to use**: When you need to manage multiple panels that can be switched between.

### 3. CenterDisplay

The `CenterDisplay` is the main display area that uses the composite pattern:

```python
# From center_display.py
class CenterDisplay(QWidget):
    """Main display area for the Update Data module."""
    
    def show_panel(self, panel_name: str):
        """Show a specific panel by name."""
        self.panel_container.show_component(panel_name)
```

**When to use**: This is typically created once by the main module view.

## Specialized Panels

### WelcomePanel

The initial panel shown when the module loads:

```python
# From welcome_panel.py
class WelcomePanel(BasePanelComponent):
    """Panel component for displaying welcome information."""
```

**When to use**: For providing introductory information and initial guidance.

### FileDisplayPanel

Manages file selection and display:

```python
# From file_display_panel.py
class FileDisplayPanel(BasePanelComponent):
    """Panel component for displaying and managing files."""
    
    # Signals
    file_removed = Signal(str)  # Publishes path of removed file
    file_selected = Signal(str)  # Publishes path of selected file
```

**When to use**: When users need to select and manage files for processing.

### DataDisplayPanel

Shows processed data in a tabular format:

```python
# From data_display_panel.py
class DataDisplayPanel(BasePanelComponent):
    """Panel component for displaying processed data."""
    
    def display_dataframe(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in the table view."""
```

**When to use**: For displaying tabular data after processing.

## Supporting Widgets

### FileDisplayWidget

A specialized widget for file display:

```python
# From file_display_widget.py
class FileDisplayWidget(QWidget):
    """Widget for displaying source files and job info."""
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree."""
    
    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
```

**When to use**: When you need a tree-based file display with selection capabilities.

### PanelActionButton

Provides consistent styling for buttons:

```python
# From btns_center_panel.py
class PanelActionButton(QPushButton):
    """A customizable button for panel-specific actions."""
    
    @classmethod
    def create_add_button(cls, compact=False, parent=None):
        """Create a pre-configured 'Add' button."""
```

**When to use**: For all action buttons in the module to ensure consistent styling.

### StatusInfoWidget

Displays status information and feedback:

```python
# From status_info.py
class StatusInfoWidget(QWidget):
    """Widget to display current module status and information."""
    
    def show_error(self, message):
        """Display an error message."""
    
    def show_warning(self, message):
        """Display a warning message."""
```

**When to use**: For providing feedback about operations and errors.

## How to Use This System

### 1. Creating a New Panel

To create a new panel:

1. Import and inherit from `BasePanelComponent`
2. Implement the required methods
3. Add your panel-specific UI and logic

Example:

```python
from ._base_panel_component import BasePanelComponent

class MyNewPanel(BasePanelComponent):
    """My new panel description."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        # Create your UI components here
        pass
    
    def show_component(self):
        # Custom show logic
        self.show()
    
    def hide_component(self):
        # Custom hide logic
        self.hide()
    
    def show_error(self, message: str):
        # Display error in this panel
        print(f"Error: {message}")
    
    def show_success(self, message: str):
        # Display success message
        print(f"Success: {message}")
```

### 2. Adding Your Panel to the System

To add your panel to the main display:

```python
# In the main module view or controller
center_display = CenterDisplay()
my_panel = MyNewPanel()

# Add to the composite panel
center_display.panel_container.add_component("my_panel", my_panel)

# Later, to show your panel
center_display.show_panel("my_panel")
```

### 3. Communication Between Components

Components communicate using the publish/subscribe pattern:

1. Define signals in your panel class:

```python
from PySide6.QtCore import Signal

class MyPanel(BasePanelComponent):
    data_processed = Signal(pd.DataFrame)  # Publishes processed data
```

2. Connect signals to slots (subscribers):

```python
# In the controller or parent component
my_panel = MyPanel()
data_display = DataDisplayPanel()

# Subscribe to the signal
my_panel.data_processed.connect(data_display.display_dataframe)
```

### 4. Error Handling

The system provides consistent error handling:

```python
# In any component
try:
    # Some operation
    process_data(file)
except Exception as e:
    self.show_error(f"Failed to process data: {str(e)}")
```

The error will be displayed appropriately based on the active panel.

## Best Practices

1. **Follow the Interface**: Always implement all methods from `BasePanelComponent`
2. **Use Signals for Communication**: Avoid direct method calls between unrelated components
3. **Consistent Styling**: Use `PanelActionButton` for buttons to maintain UI consistency
4. **Error Handling**: Use the `show_error` and `show_success` methods for user feedback
5. **Clear Naming**: Use descriptive names for components and signals
6. **Separation of Concerns**: Keep UI logic separate from business logic

## Common Patterns

### Navigation Flow

```python
# In a controller class
def handle_file_selection(self, file_path):
    # Process the file
    processed_data = self.process_file(file_path)
    
    # Update the data display
    self.data_panel.display_dataframe(processed_data, file_path)
    
    # Show the data panel
    self.center_display.show_panel("data_panel")
```

### Adding Multiple Components

```python
# In the module initialization
def setup_ui(self):
    # Create the center display
    self.center_display = CenterDisplay()
    
    # Create panels
    self.welcome_panel = WelcomePanel()
    self.file_panel = FileDisplayPanel()
    self.data_panel = DataDisplayPanel()
    
    # Add panels to the container
    container = self.center_display.panel_container
    container.add_component("welcome", self.welcome_panel)
    container.add_component("files", self.file_panel)
    container.add_component("data", self.data_panel)
    
    # Show initial panel
    container.show_component("welcome")
```

## Extending the System

To extend the system with new functionality:

1. **New Panel Types**: Inherit from `BasePanelComponent` to create new specialized panels
2. **Custom Widgets**: Create reusable widgets like `FileDisplayWidget` for specific functionality
3. **Enhanced Components**: Extend existing components to add new features

Remember that all components should follow the established patterns for consistency and maintainability.

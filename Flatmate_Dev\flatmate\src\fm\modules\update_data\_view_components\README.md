# Update Data Module View Components

This document explains the architecture and usage of the Update Data module's view components. It's designed to help novice developers understand how these components work together and how to extend the system.

## Directory Structure

The view components are organized into the following directories:

- **core/**: Base components and core structural elements
- **panels/**: Panel components that provide specific functionality
- **widgets/**: Reusable UI widgets
- **utils/**: Helper utilities for the view components

## Naming Conventions

We follow these naming conventions for better clarity and self-documentation:

- **Files**: Files are named according to their type and purpose:
  - `base_component.py`: Core base classes
  - `panel_*.py`: Panel components
  - `widget_*.py`: Widget components

- **Classes**: Classes follow standard PascalCase naming
  - `BaseComponent`: Base classes
  - `FileDisplayPanel`: Panel components
  - `StatusInfoWidget`: Widget components

- **Events**: We use publish/subscribe terminology for the event system:
  - `publish_file_selected`: Signal that publishes an event
  - `subscribe_to_file_selection`: Method that subscribes to an event

## Core Components

### 1. BaseComponent

The foundation of our UI architecture is the `BaseComponent` class, which defines the interface that all panel components must implement:

```python
# From core/base_component.py
class BaseComponent(QWidget):
    """Base interface for panel components."""
    
    def show_component(self):
        """Show this component."""
        pass
    
    def hide_component(self):
        """Hide this component."""
        pass
    
    def show_error(self, message: str):
        """Show error message."""
        pass
    
    def show_success(self, message: str):
        """Show success message."""
        pass
```

**When to use**: Inherit from this class when creating any new panel for the Update Data module.

### 2. CompositePanel

The `CompositePanel` manages multiple panel components and allows switching between them:

```python
# From core/composite_panel.py
class CompositePanel(BaseComponent):
    """Composite container for multiple panel components."""
    
    def add_component(self, name: str, component: BaseComponent):
        """Add a component to this composite panel."""
        self.components[name] = component
        self.stack.addWidget(component)
    
    def show_component(self, name: str):
        """Show a specific component by name."""
        if name in self.components:
            component = self.components[name]
            self.stack.setCurrentWidget(component)
            component.show_component()  # Activate the component
```

**When to use**: When you need to manage multiple panels that can be switched between.

### 3. CenterDisplay

The `CenterDisplay` is the main display area that uses the composite pattern:

```python
# From core/center_display.py
class CenterDisplay(QWidget):
    """Main display area for the Update Data module."""
    
    def show_panel(self, panel_name: str):
        """Show a specific panel by name."""
        self.panel_container.show_component(panel_name)
```

**When to use**: This is typically created once by the main module view.

## Panel Components

### WelcomePanel

The initial panel shown when the module loads:

```python
# From panels/panel_welcome.py
class WelcomePanel(BaseComponent):
    """Panel component for displaying welcome information."""
```

**When to use**: For providing introductory information and initial guidance.

### FileDisplayPanel

Manages file selection and display:

```python
# From panels/panel_file_display.py
class FileDisplayPanel(BaseComponent):
    """Panel component for file selection and display."""
    
    # Signals
    publish_file_selected = Signal(str)  # Publishes path of selected file
    publish_file_removed = Signal(str)  # Publishes path of removed file
```

**When to use**: When you need to provide file selection and management functionality.

### DataDisplayPanel

Displays processed data in a table format:

```python
# From panels/panel_data_display.py
class DataDisplayPanel(BaseComponent):
    """Panel component for displaying processed data."""
    
    def display_dataframe(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in the table view."""
```

**When to use**: When you need to display tabular data with metadata.

## Widget Components

### FileDisplayWidget

Provides a tree view for displaying files:

```python
# From widgets/widget_file_display.py
class FileDisplayWidget(QWidget):
    """Widget for displaying source files and job info."""
    
    # Signals
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file
```

**When to use**: When you need a file browser component within a panel.

### StatusInfoWidget

Displays status information and messages:

```python
# From widgets/widget_status_info.py
class StatusInfoWidget(QWidget):
    """Widget to display current module status, warnings, and contextual information."""
```

**When to use**: For providing status updates and feedback to the user.

### PanelActionButton

A customizable button for panel actions:

```python
# From widgets/widget_panel_action_button.py
class PanelActionButton(QPushButton):
    """A customizable button for panel-specific actions."""
```

**When to use**: For creating consistent, styled buttons throughout the interface.

### LeftPanelButtonsWidget

Contains all controls for the left panel:

```python
# From widgets/widget_left_panel_buttons.py
class LeftPanelButtonsWidget(QWidget):
    """Widget containing all controls for the Update Data left panel."""
    
    # Signals
    publish_source_select_requested = Signal(str)
    publish_save_select_requested = Signal()
    publish_process_clicked = Signal()
```

**When to use**: For the main control panel of the Update Data module.

### UpdateDataInfoWidget

Provides comprehensive information display:

```python
# From widgets/widget_update_data_info.py
class UpdateDataInfoWidget(QWidget):
    """A comprehensive information widget for Update Data module."""
```

**When to use**: When you need to display detailed status, progress, and error information.

## Utility Components

### FileDisplayHelper

Utility for extracting file display information:

```python
# From utils/file_display_helper.py
class FileDisplayHelper:
    """Utility for extracting file display information."""
    
    @staticmethod
    def process_files(file_paths):
        """Process multiple files and collect their information."""
```

**When to use**: For processing file metadata and format information.

## Event System

We use a publish/subscribe pattern for component communication:

1. **Publishers**: Components that publish events (emit signals)
2. **Subscribers**: Components that subscribe to events (connect to signals)

Example:

```python
# Publisher (in FileDisplayWidget)
self.publish_file_selected.emit(file_path)

# Subscriber (in parent component)
file_widget = FileDisplayWidget()
file_widget.publish_file_selected.connect(self.handle_file_selection)
```

## Adding New Components

To add a new component:

1. Decide if it's a panel, widget, or utility
2. Place it in the appropriate directory with the correct naming convention
3. Inherit from BaseComponent if it's a panel
4. Update the relevant __init__.py file to expose the component
5. Use publish/subscribe pattern for communication with other components

## Best Practices

1. Follow the established naming conventions
2. Use the publish/subscribe pattern for component communication
3. Keep components focused on a single responsibility
4. Reuse existing widgets when possible
5. Document your code with clear docstrings
6. Update this README when adding significant new components

# Sequential Thought MCP Server: Universal Setup Guide

## Overview
This guide explains how to set up, manage, and automate the Sequential Thought MCP server on Windows 10 using PM2, with all scripts stored in a universal location (`~/_scripts`).

## Script Locations

All scripts are now located in the universal `~/_scripts` directory:

| File | Path | Purpose |
|------|------|---------|
| `start-sequential-thought.js` | `~/_scripts/start-sequential-thought.js` | Main launcher script |
| `sequential-thought.config.js` | `~/_scripts/sequential-thought.config.js` | PM2 configuration file |
| `check-and-start-sequential-thought.js` | `~/_scripts/check-and-start-sequential-thought.js` | Auto-start script |

## Initial Setup

### 1. Install Required Packages

```bash
# Install the Sequential Thought MCP server globally
npm install -g @modelcontextprotocol/server-sequential-thinking

# Install PM2 globally if not already installed
npm install -g pm2
```

## Managing the Server

### Start the Server
```bash
# Start the server using PM2 with the universal config
pm2 start ~/_scripts/sequential-thought.config.js
```

### Check Server Status
```bash
pm2 status
```

### View Server Logs
```bash
pm2 logs sequential-thought-mcp
```

### Stop the Server
```bash
pm2 stop sequential-thought-mcp
```

### Restart the Server
```bash
pm2 restart sequential-thought-mcp
```

### Delete the Server from PM2
```bash
pm2 delete sequential-thought-mcp
```

## Auto-start Configuration

### Configure PM2 to Start on Boot
```bash
# Save the current PM2 process list
pm2 save

# Generate startup script and follow instructions
pm2 startup
```

### Auto-start with Windsurf
The universal auto-start script checks if the Sequential Thought MCP server is running and starts it if needed:

1. Create a batch file to run the script:

```bash
cat > ~/windsurf-startup.bat << 'EOL'
@echo off
node "%USERPROFILE%\_scripts\check-and-start-sequential-thought.js"
EOL
```

2. Configure this batch file to run when Windows starts or when you start Windsurf.

### Configure Windsurf to Connect to the Server
Edit your `mcp_config.json` file to include:

```json
{
  "mcpServers": {
    // Other servers...
  },
  "servers": [
    {
      "name": "sequential-thought",
      "endpoint": "http://localhost:4000",
      "type": "node",
      "description": "Sequential Thought MCP server running locally"
    }
  ]
}
```

## Creating Shell Aliases (Optional)

For even easier management, add these aliases to your `~/.bashrc` or `~/.bash_profile`:

```bash
# Add these lines to your ~/.bashrc
echo 'alias st-start="pm2 start ~/_scripts/sequential-thought.config.js"' >> ~/.bashrc
echo 'alias st-stop="pm2 stop sequential-thought-mcp"' >> ~/.bashrc
echo 'alias st-restart="pm2 restart sequential-thought-mcp"' >> ~/.bashrc
echo 'alias st-status="pm2 status sequential-thought-mcp"' >> ~/.bashrc
echo 'alias st-logs="pm2 logs sequential-thought-mcp"' >> ~/.bashrc
echo 'alias st-check="node ~/_scripts/check-and-start-sequential-thought.js"' >> ~/.bashrc

# Reload your bashrc
source ~/.bashrc
```

Then you can simply use:
```bash
st-start    # Start the server
st-stop     # Stop the server
st-restart  # Restart the server
st-status   # Check server status
st-logs     # View server logs
st-check    # Check if server is running and start if needed
```

## Troubleshooting

### Server Won't Start
1. Check if the server is already running:
   ```bash
   pm2 status
   ```

2. Check for errors in the logs:
   ```bash
   pm2 logs sequential-thought-mcp
   ```

3. Try restarting the server:
   ```bash
   pm2 restart sequential-thought-mcp
   ```

4. If all else fails, delete and restart:
   ```bash
   pm2 delete sequential-thought-mcp
   pm2 start ~/_scripts/sequential-thought.config.js
   ```

### Verify Server is Running
The Sequential Thought MCP server runs on port 4000 by default. To verify it's running:

```bash
curl http://localhost:4000/resources
```

You should see a JSON response listing available resources.

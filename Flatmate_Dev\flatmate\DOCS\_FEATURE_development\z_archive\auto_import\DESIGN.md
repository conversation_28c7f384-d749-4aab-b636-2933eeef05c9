# Technical Design: Auto-Import Folder Feature

## Current Architecture Analysis

### Existing Components (Verified from Codebase)

#### File Processing Pipeline
- **File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\utils\dw_director.py`
- **Function**: `dw_director(job_sheet: Dict[str, Any]) -> Dict[str, Any]` (lines 53-211)
- **Purpose**: Orchestrates bank statement processing pipeline
- **Integration Point**: Accepts job_sheet with filepaths, save_folder, update_database flag

#### Configuration System
- **File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\config\keys.py`
- **Class**: `ConfigKeys.AutoImport` (lines 68-74)
- **Existing Keys**: ENABLED, IMPORT_PATH, ARCHIVE_PATH, FAILED_PATH
- **Integration Point**: Configuration values accessible via `config.get_value(ConfigKeys.AutoImport.ENABLED)`

#### Services Architecture
- **Directory**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\`
- **Pattern**: Singleton services (logger.py, event_bus.py, cache_service.py)
- **Integration Point**: New AutoImportManager follows existing service patterns

#### Application Lifecycle
- **File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py`
- **Function**: `initialize_application()` (lines 19-99)
- **Integration Point**: Service initialization after line 67 (DBIOService initialization)

## Required Changes

### 1. Create AutoImportManager Service
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py`
**Purpose**: Singleton service for file system monitoring and processing
**Dependencies**: 
- `watchdog` library for file system events
- `queue.Queue` for thread-safe file processing
- `threading.Thread` for background processing

### 2. Modify Application Initialization
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py`
**Method**: `initialize_application()` (line 19)
**Change**: Add AutoImportManager initialization after DBIOService (line 67)

### 3. Update Configuration Defaults
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\config\keys.py`
**Method**: `get_defaults()` (lines 76-122)
**Change**: Set default paths for auto-import folders

## Integration Points

### dw_director Integration
- **Component**: AutoImportManager worker thread
- **Connection**: Calls `dw_director(job_sheet)` with single file path
- **Data Flow**: File path → job_sheet → dw_director → processing result → file movement

### Configuration Integration  
- **Component**: AutoImportManager.__init__()
- **Connection**: Reads config values via `config.get_value(ConfigKeys.AutoImport.*)`
- **Data Flow**: Config changes → AutoImportManager restart → new monitoring paths

### Event Bus Integration
- **Component**: AutoImportManager processing callbacks
- **Connection**: Publishes import events via `global_event_bus.publish()`
- **Data Flow**: File processed → event published → UI updates

## Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   File System   │───▶│ AutoImportManager │───▶│   dw_director   │
│     Events      │    │                  │    │    Pipeline     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Processing      │    │  File Movement  │
                       │     Queue        │    │   (Archive/     │
                       │                  │    │    Failed)      │
                       └──────────────────┘    └─────────────────┘
```

## Component Specifications

### AutoImportManager Class
```python
class AutoImportManager:
    """Singleton service for automated file import monitoring"""
    
    def __init__(self):
        # Configuration loading
        # Queue and thread setup
        # Observer initialization
    
    def start(self) -> None:
        """Start file system monitoring"""
    
    def stop(self) -> None:
        """Stop monitoring and cleanup resources"""
    
    def _process_file(self, file_path: str) -> None:
        """Process single file through dw_director pipeline"""
```

### AutoImportEventHandler Class
```python
class AutoImportEventHandler(FileSystemEventHandler):
    """Handle file system events for import folder"""
    
    def on_created(self, event) -> None:
        """Handle new file creation events"""
    
    def on_modified(self, event) -> None:
        """Handle file modification events (for large files)"""
```

## Error Handling Strategy

### File Processing Errors
- **Detection**: Monitor dw_director return status
- **Action**: Move failed files to failed_imports folder
- **Logging**: Log detailed error information
- **Recovery**: Continue monitoring for new files

### Service Errors
- **Detection**: Exception handling in worker thread
- **Action**: Restart monitoring service
- **Logging**: Log service restart events
- **Recovery**: Scan directory for missed files on restart

## Performance Considerations

### Memory Usage
- **Queue Size**: Limited to 100 pending files maximum
- **Thread Count**: Single worker thread to avoid resource contention
- **File Watching**: Uses OS-level events (no polling overhead)

### CPU Usage
- **Idle State**: <0.1% CPU usage when no files present
- **Processing**: Delegates heavy work to existing dw_director pipeline
- **Debouncing**: 2-second delay to handle large file writes

## Security Considerations

### Path Validation
- **Input**: Validate all configured paths exist and are accessible
- **Permissions**: Check read/write permissions before starting monitoring
- **Traversal**: Prevent directory traversal attacks in file paths

### File Validation
- **Extensions**: Only process .csv files initially
- **Size Limits**: Reject files larger than 100MB
- **Content**: Basic CSV format validation before processing

---
*Created: 2025-07-21*
*Based on: architecture_considerations.md and implementation_outline.md*

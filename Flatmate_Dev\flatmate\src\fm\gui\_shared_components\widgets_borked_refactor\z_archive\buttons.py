"""
Button Components

Provides reusable button components that follow the application's design patterns.
These components can be used across different modules for consistent button styling.
"""

from PySide6.QtWidgets import QPushButton


class ActionButton(QPushButton):
    """
    A styled button for primary actions.
    """
    
    def __init__(self, text, parent=None):
        """
        Initialize the action button.
        
        Args:
            text: Button text
            parent: Parent widget
        """
        super().__init__(text, parent)
        self.setProperty("type", "action_btn")


class SecondaryButton(QPushButton):
    """
    A styled button for secondary actions.
    """
    
    def __init__(self, text, parent=None):
        """
        Initialize the secondary button.
        
        Args:
            text: Button text
            parent: Parent widget
        """
        super().__init__(text, parent)
        self.setProperty("type", "select_btn")


class ExitButton(QPushButton):
    """
    A styled button for exit/cancel actions.
    """
    
    def __init__(self, text, parent=None):
        """
        Initialize the exit button.
        
        Args:
            text: Button text
            parent: Parent widget
        """
        super().__init__(text, parent)
        self.setProperty("type", "exit_btn")

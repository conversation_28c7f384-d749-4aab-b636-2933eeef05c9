"""
View components for the Update Data module.

This package provides a modular, component-based UI for the Update Data module.
Components are organized into the following categories:

- _common: Base components and core structural elements
- center_panel: Center panel components for main content display
- left_panel: Left panel components for navigation
- right_panel: Right panel components for settings and options
- utils: Helper utilities for the view components
"""

from fm.gui._shared_components.base.base_pane import BasePane
# Import common components
from fm.gui._shared_components import BasePanelComponent
from .center_panel._panel_manager import CenterPanelManager
from .center_panel._switcher import PanelSwitcher
from .center_panel.data_pane import DataPane
from .center_panel.file_pane import FilePane
# Import panel components
from .center_panel.welcome_pane import WelcomePane
# Import left panel components
from .left_panel._panel_manager import LeftPanelManager
# Import right panel components
from .right_panel._panel_manager import RightPanelManager
from .right_panel.options_pane import OptionsPane
# Import utilities
from .utils.file_helper import FileHelper

__all__ = [
    # Common components
    'BasePanelComponent',
    'BasePane',
    
    # Panel components
    'WelcomePane',
    'FilePane',
    'DataPane',
    'CenterPanelManager',
    'PanelSwitcher',
    
    # Left panel components
    'LeftPanelManager',
    
    # Right panel components
    'RightPanelManager',
    'OptionsPane',
    
    # Utilities
    'FileHelper',
]

"""
Widget Configuration System

Provides dataclass-based configuration for all shared widgets following
the App-Wide Widget Pattern established in the codebase.
"""

from dataclasses import dataclass, field
from typing import Optional, Any, Dict, List


@dataclass
class BaseWidgetConfig:
    """Base configuration for all widgets."""
    # Styling
    style_type: str = "default"
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    # Behavior
    enabled: bool = True
    tooltip: Optional[str] = None
    
    # Layout
    minimum_width: Optional[int] = None
    minimum_height: Optional[int] = None
    maximum_width: Optional[int] = None
    maximum_height: Optional[int] = None


@dataclass  
class ButtonConfig(BaseWidgetConfig):
    """Configuration for button widgets."""
    style_type: str = "action_btn"
    text: str = ""
    icon: Optional[str] = None


@dataclass
class CheckBoxConfig(BaseWidgetConfig):
    """Configuration for checkbox widgets."""
    checked: bool = False
    label_text: str = ""
    label_position: str = "right"  # "left" or "right"


@dataclass
class LabelConfig(BaseWidgetConfig):
    """Configuration for label widgets."""
    text: str = ""
    alignment: str = "left"  # "left", "center", "right"
    word_wrap: bool = False


@dataclass
class OptionMenuConfig(BaseWidgetConfig):
    """Configuration for option menu widgets."""
    options: List[str] = field(default_factory=list)
    selected_option: Optional[str] = None
    placeholder_text: str = "Select option..."
    editable: bool = False


@dataclass
class SelectorConfig(BaseWidgetConfig):
    """Configuration for selector widgets."""
    multi_select: bool = True
    compact_display: bool = True
    max_display_items: int = 3


@dataclass
class FilterConfig(BaseWidgetConfig):
    """Configuration for filter widgets."""
    filter_type: str = "date"
    default_range: Optional[str] = None
    show_presets: bool = True

# Test Tasks / To-Do List
*Last updated: 2025-07-23*

> Update this list after each test cycle with distilled, actionable items from the test report.

## Current Actionable Tasks

- [ ] **Process Button**: Improve visual indicators for active/inactive states
- [ ] **Info Pane**: Add default folder suggestion and "Set as default" option
- [ ] **Data Source Label**: Change from "Data Source" to "Import data from:"
- [ ] **Select Button**: Make inactive state more visually distinct
- [ ] **Folder Selection**: Improve dialog feedback for empty folders ("No Items Match" confusion)
- [ ] **Folder Path Display**: Make paths copyable or add truncation with tooltip
- [ ] **Source Folder Option**: Rename to more user-friendly, specific term

## Implementation Priority
1. Process button state clarity (critical for user interaction)
2. Button states visual distinction (affects all interactive elements)
3. Info pane guidance improvements (enhances user workflow)
4. Folder selection and display improvements (reduces friction)
5. Terminology updates (improves clarity but lower urgency)

---
*Check off completed tasks with date. Add new tasks after each test cycle.*

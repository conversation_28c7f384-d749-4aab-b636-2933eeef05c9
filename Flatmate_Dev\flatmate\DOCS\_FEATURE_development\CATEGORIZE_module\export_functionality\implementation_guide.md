# Export Functionality Implementation Guide

## Overview
This guide provides step-by-step implementation details for fixing the export functionality to respect table filters and sorting (WYSIWYG export).

## Problem Statement
The current export functionality exports the original DataFrame data, ignoring any filters or sorting applied through the `EnhancedFilterProxyModel`. This violates the WYSIWYG principle where users expect to export exactly what they see.

## Architecture Context

### Current Data Flow (Broken):
```
User Data → EnhancedTableModel._original_data
                ↓
         EnhancedFilterProxyModel (applies filters/sorting)
                ↓
         TableViewCore displays filtered data
                ↓
         Export calls get_dataframe() → Returns original data (WRONG!)
```

### Fixed Data Flow:
```
User Data → EnhancedTableModel._original_data
                ↓
         EnhancedFilterProxyModel (applies filters/sorting)
                ↓
         TableViewCore displays filtered data
                ↓
         Export calls get_visible_dataframe() → Returns filtered data (CORRECT!)
```

## Implementation Steps

### Step 1: Understand the Proxy Model Pattern

The table uses Qt's Model-View architecture:
- **Source Model**: `EnhancedTableModel` - holds original data
- **Proxy Model**: `EnhancedFilterProxyModel` - applies filters/sorting
- **View**: `TableViewCore` - displays the proxy model data

Key Qt Methods:
- `self.model()` - returns the proxy model
- `proxy_model.sourceModel()` - returns the source model
- `proxy_model.mapToSource(proxy_index)` - maps proxy row to source row
- `proxy_model.rowCount()` - number of visible rows after filtering

### Step 2: Implement get_visible_dataframe()

**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
**Location**: Add after line 367

```python
def get_visible_dataframe(self) -> pd.DataFrame:
    """Get the currently visible data as a pandas DataFrame.
    
    This method respects all active filters and sorting applied through
    the proxy model, returning exactly what the user sees in the table.
    
    Returns:
        pd.DataFrame: The filtered and sorted data as displayed
    """
    import pandas as pd
    from PySide6.QtCore import Qt
    
    proxy_model = self.model()  # EnhancedFilterProxyModel
    source_model = proxy_model.sourceModel()  # EnhancedTableModel
    
    # Handle empty table
    if proxy_model.rowCount() == 0:
        return pd.DataFrame()
    
    # Get visible columns and their names
    visible_columns = []
    column_names = []
    for col in range(source_model.columnCount()):
        if not self.isColumnHidden(col):
            visible_columns.append(col)
            column_names.append(source_model.headerData(col, Qt.Horizontal))
    
    # Extract visible data in display order (respects sorting)
    data_rows = []
    for proxy_row in range(proxy_model.rowCount()):
        row_data = []
        for col in visible_columns:
            # Get data from proxy model (this respects filtering and sorting)
            proxy_index = proxy_model.index(proxy_row, col)
            
            # Get the actual data value
            # Try DisplayRole first, then UserRole for original values
            value = proxy_model.data(proxy_index, Qt.DisplayRole)
            if value is None:
                # Fallback to source model if needed
                source_index = proxy_model.mapToSource(proxy_index)
                value = source_model.data(source_index, Qt.UserRole)
            
            row_data.append(value)
        data_rows.append(row_data)
    
    # Create DataFrame with proper column names
    df = pd.DataFrame(data_rows, columns=column_names)
    return df
```

### Step 3: Update _export_data() Method

**File**: `flatmate/src/fm/gui/_shared_components/table_view_v2/components/table_view_core.py`
**Location**: Lines 309-325 (replace existing method)

```python
def _export_data(self, format_type):
    """Export data to file."""
    from PySide6.QtWidgets import QFileDialog, QMessageBox
    import logging
    
    try:
        # Get visible DataFrame (respects filters and sorting)
        df = self.get_visible_dataframe()
        
        # Check if there's data to export
        if df.empty:
            QMessageBox.information(
                self, 
                "Export", 
                "No data to export. Please check your filters."
            )
            return
        
        # Handle different export formats
        if format_type == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to CSV", "", "CSV Files (*.csv)")
            if file_path:
                df.to_csv(file_path, index=False)
                QMessageBox.information(
                    self, 
                    "Export Successful", 
                    f"Data exported successfully to {file_path}\n"
                    f"Exported {len(df)} rows and {len(df.columns)} columns."
                )
                
        elif format_type == "excel":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export to Excel", "", "Excel Files (*.xlsx)")
            if file_path:
                df.to_excel(file_path, index=False)
                QMessageBox.information(
                    self, 
                    "Export Successful", 
                    f"Data exported successfully to {file_path}\n"
                    f"Exported {len(df)} rows and {len(df.columns)} columns."
                )
                
    except PermissionError:
        QMessageBox.critical(
            self, 
            "Export Error", 
            "Permission denied. Please check that the file is not open in another application."
        )
    except Exception as e:
        QMessageBox.critical(
            self, 
            "Export Error", 
            f"Failed to export data: {str(e)}"
        )
        logging.error(f"Export failed: {e}", exc_info=True)
```

## Testing Strategy

### Unit Tests
Create test cases for:
1. Empty table export
2. Filtered data export
3. Sorted data export
4. Hidden columns handling
5. Error conditions

### Integration Tests
Test with actual categorize module:
1. Load transaction data
2. Apply various filters
3. Export and verify results match visible table

### Test Data Scenarios
- Small dataset (10 rows)
- Medium dataset (100 rows)  
- Large dataset (1000+ rows)
- Edge cases (empty results, single row, single column)

## Common Pitfalls and Solutions

### Pitfall 1: Using Wrong Model Reference
**Problem**: Calling `self._model` instead of `self.model()`
**Solution**: Always use `self.model()` to get the proxy model

### Pitfall 2: Ignoring Column Visibility
**Problem**: Exporting hidden columns
**Solution**: Check `self.isColumnHidden(col)` for each column

### Pitfall 3: Wrong Data Role
**Problem**: Getting display text instead of actual values
**Solution**: Try DisplayRole first, fallback to UserRole for original values

### Pitfall 4: Performance Issues
**Problem**: Slow export for large datasets
**Solution**: Consider batch processing for very large datasets (future enhancement)

## Validation Checklist

- [ ] Export matches visible table exactly
- [ ] Filters are respected in export
- [ ] Sorting is preserved in export
- [ ] Hidden columns are excluded from export
- [ ] Empty results show user-friendly message
- [ ] File permission errors are handled gracefully
- [ ] Success messages show export statistics
- [ ] Large datasets export within reasonable time

## Future Enhancements

### Phase 2 Features:
- Export configuration dialog
- Custom column selection
- Multiple format support (JSON, XML)
- Export templates and presets

### Performance Optimizations:
- Streaming export for very large datasets
- Background export with progress indicator
- Export caching for repeated operations

## Troubleshooting

### Issue: Export is empty
**Cause**: All data filtered out
**Solution**: Check filter settings, show user-friendly message

### Issue: Export shows wrong data
**Cause**: Using source model instead of proxy model
**Solution**: Ensure `get_visible_dataframe()` uses proxy model data

### Issue: Export crashes
**Cause**: Unhandled exception in data processing
**Solution**: Add comprehensive error handling with logging

### Issue: Export is slow
**Cause**: Inefficient data extraction
**Solution**: Profile the code and optimize data access patterns

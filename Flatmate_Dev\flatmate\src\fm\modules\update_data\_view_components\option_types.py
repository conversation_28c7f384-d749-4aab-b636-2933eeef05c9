"""Type definitions for Update Data module."""

from enum import Enum


class SourceOptions(str, Enum):
    """Source selection types."""
    SELECT_FOLDER = "Select entire folder..."
    SELECT_FILES = "Select individual files..."
    SET_AUTO_IMPORT = "Set auto import folder..."
    AUTO_IMPORT_FOLDER = "Auto Import Folder"

class SaveOptions(str, Enum):
    """Save location types."""
    SAME_AS_SOURCE = "Same as Source Files"
    SELECT_LOCATION = "Select save location..."

# Centralized Mode State Architecture

**Date**: 2025-01-21
**Priority**: HIGH (Architectural Improvement)
**Status**: DESIGN_READY

## 🎯 **Problem Statement**

Current morphic UI implementation has scattered mode logic across multiple files:
- `widgets.py` - UI element visibility/state logic
- `view_context_manager.py` - Mode configuration logic  
- `ud_presenter.py` - Mode transition handling

**Result**: State management bugs, inconsistent behavior, difficult maintenance

**User Insight**: *"enumerate modes, and define in a pydantic dataclass in one place, what state each element should be in in a given mode.. use this to drive the context manager"*

## 🏗️ **Proposed Architecture**

### **Single Source of Truth Pattern**
All UI mode states defined in one immutable, type-safe model that drives all UI behavior.

### **File Structure**
```
src/fm/modules/update_data/
├── models/
│   ├── __init__.py
│   └── ui_modes.py          # NEW: Centralized mode definitions
├── view_context_manager.py  # MODIFIED: Uses mode models
└── _view/left_panel/widgets/widgets.py  # SIMPLIFIED: No mode logic
```

## 📋 **Implementation Plan**

### **Step 1: Create Mode Models** (30 min)

**File**: `src/fm/modules/update_data/models/ui_modes.py`

```python
from enum import Enum
from pydantic import BaseModel
from typing import List, Optional

class UIMode(str, Enum):
    """Enumeration of all possible UI modes."""
    DATABASE_AUTO_IMPORT = "database_auto_import"
    DATABASE_MANUAL = "database_manual"
    FILE_UTILITY = "file_utility"

class UIElementState(BaseModel):
    """State configuration for a single UI element."""
    visible: bool = True
    enabled: bool = True
    text: Optional[str] = None
    options: Optional[List[str]] = None
    checked: Optional[bool] = None
    tooltip: Optional[str] = None

class ModeConfiguration(BaseModel):
    """Complete UI configuration for a specific mode."""
    # Source section
    source_combo: UIElementState
    source_button: UIElementState
    
    # Save section  
    save_combo: UIElementState
    save_button: UIElementState
    
    # Database section
    database_checkbox: UIElementState
    
    # Process section
    process_button: UIElementState
    
    # Center panel
    center_panel_type: str  # "welcome", "file_pane", "processing"
    
    class Config:
        frozen = True  # Immutable configurations

# Mode Definitions
MODE_CONFIGS = {
    UIMode.DATABASE_AUTO_IMPORT: ModeConfiguration(
        source_combo=UIElementState(
            options=[
                "Auto Import Folder",
                "Select entire folder...", 
                "Select individual files...",
                "Set auto import folder..."
            ],
            text="Auto Import Folder"
        ),
        source_button=UIElementState(
            text="Configure...",
            tooltip="Configure auto-import settings"
        ),
        save_combo=UIElementState(
            options=[
                "Archive Location",
                "Same as source", 
                "Select location..."
            ],
            text="Archive Location"
        ),
        save_button=UIElementState(
            text="Select...",
            enabled=True
        ),
        database_checkbox=UIElementState(
            checked=True,
            text="Update Database"
        ),
        process_button=UIElementState(
            text="Update Database",
            enabled=True
        ),
        center_panel_type="file_pane"
    ),
    
    UIMode.DATABASE_MANUAL: ModeConfiguration(
        source_combo=UIElementState(
            options=[
                "Set auto import folder...",
                "Select entire folder...",
                "Select individual files..."
            ],
            text="Select entire folder..."
        ),
        source_button=UIElementState(
            text="Select...",
            tooltip="Select source files or folder"
        ),
        save_combo=UIElementState(
            options=[
                "Archive Location",
                "Same as source",
                "Select location..."
            ],
            text="Archive Location"
        ),
        database_checkbox=UIElementState(
            checked=True,
            text="Update Database"
        ),
        process_button=UIElementState(
            text="Update Database"
        ),
        center_panel_type="welcome"
    ),
    
    UIMode.FILE_UTILITY: ModeConfiguration(
        source_combo=UIElementState(
            options=[
                "Select entire folder...",
                "Select individual files..."
            ],
            text="Select entire folder..."
        ),
        source_button=UIElementState(
            text="Select...",
            tooltip="Select source files or folder"
        ),
        save_combo=UIElementState(
            options=[
                "Same as source",
                "Select location..."
            ],
            text="Same as source"
        ),
        save_button=UIElementState(
            text="Select...",
            enabled=False  # Disabled when "Same as source"
        ),
        database_checkbox=UIElementState(
            checked=False,
            text="Update Database"
        ),
        process_button=UIElementState(
            text="Process Files"
        ),
        center_panel_type="welcome"
    )
}

def get_mode_config(mode: UIMode) -> ModeConfiguration:
    """Get configuration for specified mode."""
    return MODE_CONFIGS[mode]

def determine_mode(auto_import_enabled: bool, database_enabled: bool) -> UIMode:
    """Determine UI mode based on current settings."""
    if database_enabled:
        if auto_import_enabled:
            return UIMode.DATABASE_AUTO_IMPORT
        else:
            return UIMode.DATABASE_MANUAL
    else:
        return UIMode.FILE_UTILITY
```

### **Step 2: Create UI Applier** (30 min)

**File**: `src/fm/modules/update_data/models/ui_applier.py`

```python
from PySide6.QtWidgets import QComboBox, QPushButton, QCheckBox
from .ui_modes import UIElementState, ModeConfiguration

class UIStateApplier:
    """Applies mode configurations to actual UI elements."""
    
    @staticmethod
    def apply_to_combo(combo: QComboBox, state: UIElementState):
        """Apply state to combo box."""
        if state.options:
            current = combo.currentText()
            combo.clear()
            combo.addItems(state.options)
            
            # Restore selection if still valid
            if state.text and state.text in state.options:
                combo.setCurrentText(state.text)
            elif current in state.options:
                combo.setCurrentText(current)
        
        combo.setVisible(state.visible)
        combo.setEnabled(state.enabled)
        
        if state.tooltip:
            combo.setToolTip(state.tooltip)
    
    @staticmethod
    def apply_to_button(button: QPushButton, state: UIElementState):
        """Apply state to button."""
        if state.text:
            button.setText(state.text)
        
        button.setVisible(state.visible)
        button.setEnabled(state.enabled)
        
        if state.tooltip:
            button.setToolTip(state.tooltip)
    
    @staticmethod
    def apply_to_checkbox(checkbox, state: UIElementState):
        """Apply state to checkbox (handles both QCheckBox and LabeledCheckBox)."""
        if state.checked is not None:
            if hasattr(checkbox, 'set_checked'):
                checkbox.set_checked(state.checked)
            else:
                checkbox.setChecked(state.checked)
        
        checkbox.setVisible(state.visible)
        checkbox.setEnabled(state.enabled)
        
        if state.tooltip:
            checkbox.setToolTip(state.tooltip)
    
    @classmethod
    def apply_configuration(cls, view, config: ModeConfiguration):
        """Apply complete mode configuration to view."""
        # Apply to all UI elements
        cls.apply_to_combo(view.left_buttons.source_combo, config.source_combo)
        cls.apply_to_button(view.left_buttons.source_select_btn, config.source_button)
        cls.apply_to_combo(view.left_buttons.save_combo, config.save_combo)
        cls.apply_to_button(view.left_buttons.save_select_btn, config.save_button)
        cls.apply_to_checkbox(view.left_buttons.db_update_checkbox, config.database_checkbox)
        cls.apply_to_button(view.left_buttons.process_btn, config.process_button)
        
        # Apply center panel configuration
        if config.center_panel_type == "welcome":
            view.center_display.show_welcome_pane()
        elif config.center_panel_type == "file_pane":
            view.center_display.show_file_pane()
```

### **Step 3: Refactor Context Manager** (45 min)

**File**: `src/fm/modules/update_data/view_context_manager.py`

```python
from .models.ui_modes import UIMode, determine_mode, get_mode_config
from .models.ui_applier import UIStateApplier

class ViewContextManager:
    def configure_view_for_workflow(self, view, is_database_mode, auto_import_status):
        """Configure view based on workflow requirements."""
        # Determine mode from current state
        auto_import_enabled = (
            auto_import_status.get('enabled', False) and 
            auto_import_status.get('pending_files', [])
        )
        
        mode = determine_mode(auto_import_enabled, is_database_mode)
        
        # Apply mode configuration
        config = get_mode_config(mode)
        UIStateApplier.apply_configuration(view, config)
        
        # Handle special cases (pending files display, etc.)
        if mode == UIMode.DATABASE_AUTO_IMPORT and auto_import_status.get('pending_files'):
            self._display_pending_files(view, auto_import_status)
    
    def _display_pending_files(self, view, auto_import_status):
        """Display pending files in center panel."""
        # Implementation for showing pending files
        pass
```

### **Step 4: Simplify Widgets** (15 min)

**File**: `src/fm/modules/update_data/_view/left_panel/widgets/widgets.py`

Remove all mode-specific logic - widgets become pure UI components:

```python
# Remove these methods (logic moved to centralized system):
# - show_auto_import_controls()
# - hide_auto_import_controls()
# - All mode-specific conditional logic

# Keep only:
# - UI element creation
# - Signal connections
# - Basic getters/setters
```

## 🎯 **Benefits**

### **Immediate Benefits**
- ✅ **Eliminates State Bugs**: Single source of truth prevents inconsistencies
- ✅ **Type Safety**: Pydantic validation catches configuration errors
- ✅ **Immutable Configs**: Frozen models prevent accidental mutations
- ✅ **Testable**: Easy to unit test mode configurations

### **Long-term Benefits**
- ✅ **Maintainable**: Adding new modes is straightforward
- ✅ **Debuggable**: All mode logic in one place
- ✅ **Extensible**: Easy to add new UI elements or modes
- ✅ **Consistent**: Guaranteed consistent behavior across modes

## ⏱️ **Implementation Timeline**

**Total Time**: 2 hours
- Step 1: Mode Models (30 min)
- Step 2: UI Applier (30 min)  
- Step 3: Context Manager (45 min)
- Step 4: Widget Cleanup (15 min)

## 🧪 **Testing Strategy**

### **Unit Tests**
- Test mode determination logic
- Test configuration application
- Test state transitions

### **Integration Tests**
- Test complete mode transitions
- Test UI element states in each mode
- Test edge cases and error conditions

---

**Next Action**: Implement after critical bug fixes are complete
**Impact**: Eliminates root cause of ALL UI state management issues
**Risk**: LOW - Well-defined architectural improvement with clear benefits

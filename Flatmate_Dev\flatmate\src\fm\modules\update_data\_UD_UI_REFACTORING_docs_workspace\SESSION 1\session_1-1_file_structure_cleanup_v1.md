# Session 1.1: File Structure Cleanup Implementation Guide

**Date**: 2025-07-26 14:45:22  
**Version**: 1.0  
**Session**: 1.1 (Foundation Rationalization)  
**Duration**: 2-3 hours  
**Architect**: <PERSON> 🏗️

---

## Session Overview

**Goal**: Rationalize file structure and naming conventions to support event-driven architecture  
**Prerequisites**: None (can start immediately)  
**Risk Level**: Low (mostly file operations)  
**Success Criteria**: Clean file structure with no broken imports

---

## Task Breakdown

### 📋 **Task 1.1.1: Rename `utils` → `pipeline`** (15 min)
**Rationale**: `utils` is too generic; `pipeline` better reflects data processing purpose

#### Steps:
1. **Rename directory**:
   ```bash
   cd C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data
   mv utils pipeline
   ```

2. **Update `__init__.py` if exists**:
   - Check if `pipeline/__init__.py` needs updates
   - Verify no hardcoded 'utils' references

#### Validation:
- [ ] Directory renamed successfully
- [ ] No broken file references
- [ ] Git recognizes the rename

---

### 📋 **Task 1.1.2: Move `option_types.py` → `_view_components`** (15 min)
**Rationale**: `option_types.py` contains view-specific enums, belongs with view components

#### Steps:
1. **Move file**:
   ```bash
   mv pipeline/option_types.py _view_components/option_types.py
   ```

2. **Verify file contents** - should contain:
   ```python
   class SourceOptions(str, Enum):
       SELECT_FOLDER = "Select entire folder..."
       SELECT_FILES = "Select individual files..."
       # Note: Remove SET_AUTO_IMPORT until actually needed
   
   class SaveOptions(str, Enum):
       SAME_AS_SOURCE = "Same as Source Files"
       SELECT_LOCATION = "Select save location..."
   ```

#### Validation:
- [ ] File moved to correct location
- [ ] File contents intact
- [ ] No duplicate files

---

### 📋 **Task 1.1.3: Update All Import Statements** (30 min)
**Rationale**: Fix broken imports caused by file moves

#### Files to Update:

1. **`ud_presenter.py`** - Update imports:
   ```python
   # OLD:
   from .utils.option_types import SaveOptions, SourceOptions
   from .utils.dw_director import dw_director
   
   # NEW:
   from ._view_components.option_types import SaveOptions, SourceOptions
   from .pipeline.dw_director import dw_director
   ```

2. **Any other files importing from utils** - Search and replace:
   ```bash
   # Search for files importing from utils
   grep -r "from.*utils" .
   grep -r "import.*utils" .
   ```

3. **Update relative imports** in moved files if needed

#### Validation:
- [ ] All imports updated
- [ ] No import errors when running Python
- [ ] IDE shows no import warnings

---

### 📋 **Task 1.1.4: Create `local_event_bus.py` in `services`** (45 min)
**Rationale**: Foundation for event-driven architecture

#### Implementation:
1. **Create file**: `services/local_event_bus.py`
2. **Copy implementation** from proposed design:
   ```python
   # Copy from: _UD_VIEW_docs_workspace/proposed_local_event_bus.py
   # Key components:
   # - LocalEventBus class
   # - ViewEvents enum (basic events only)
   # - EventDataFactory (core methods)
   # - update_data_local_bus instance
   ```

3. **Simplify for initial implementation**:
   - Include only essential events for now
   - Skip advanced features like `emit_and_wait`
   - Focus on basic pub/sub functionality

#### Essential Events for Session 1.1:
```python
class ViewEvents(Enum):
    # User Actions
    SOURCE_SELECT_REQUESTED = "source_select_requested"
    PROCESS_REQUESTED = "process_requested"
    
    # Business Events  
    SOURCE_DISCOVERED = "source_discovered"
    PROCESSING_STARTED = "processing_started"
    
    # State Events
    UI_STATE_CHANGED = "ui_state_changed"
    
    # Dialog Events
    FOLDER_DIALOG_REQUESTED = "folder_dialog_requested"
```

#### Validation:
- [ ] File created successfully
- [ ] Can import LocalEventBus
- [ ] Basic event emission works
- [ ] No syntax errors

---

### 📋 **Task 1.1.5: Create `view_events.py` in `_view_components`** (30 min)
**Rationale**: Centralized view event definitions close to view logic

#### Implementation:
1. **Create file**: `_view_components/view_events.py`
2. **Include essential components**:
   ```python
   # Simplified version of proposed_view_events.py
   # Include:
   # - ViewEvents enum (essential events only)
   # - EventDataFactory (core methods only)
   # - Basic validation functions
   ```

3. **Focus on immediate needs**:
   - Don't include speculative auto-import events
   - Keep event data structures simple
   - Add comprehensive events later as needed

#### Validation:
- [ ] File created successfully
- [ ] Can import ViewEvents
- [ ] EventDataFactory methods work
- [ ] No circular import issues

---

### 📋 **Task 1.1.6: Test Import Structure** (15 min)
**Rationale**: Ensure all changes work together

#### Testing Steps:
1. **Test basic imports**:
   ```python
   # Test in Python REPL or simple script
   from fm.modules.update_data._view_components.option_types import SourceOptions
   from fm.modules.update_data.services.local_event_bus import update_data_local_bus
   from fm.modules.update_data._view_components.view_events import ViewEvents
   from fm.modules.update_data.pipeline.dw_director import dw_director
   ```

2. **Test event bus functionality**:
   ```python
   # Basic event test
   def test_handler(data):
       print(f"Received: {data}")
   
   update_data_local_bus.subscribe("test_event", test_handler)
   update_data_local_bus.emit("test_event", {"message": "Hello World"})
   ```

3. **Run existing code** to check for regressions:
   ```bash
   cd flatmate
   python -m fm.modules.update_data.ud_presenter  # If it has __main__
   ```

#### Validation:
- [ ] All imports work without errors
- [ ] Event bus basic functionality works
- [ ] No regressions in existing code
- [ ] IDE shows no import warnings

---

## Session Deliverables

### ✅ **File Structure After Session 1.1**:
```
update_data/
├── pipeline/                       # ✅ Renamed from 'utils'
│   ├── dw_director.py             # ✅ Unchanged
│   ├── file_utils.py              # ✅ Unchanged  
│   └── processing_tracker.py      # ✅ Unchanged
├── services/
│   ├── events.py                  # ✅ Unchanged (global events)
│   └── local_event_bus.py         # ✅ NEW - Local event system
├── _view_components/
│   ├── option_types.py            # ✅ MOVED from utils
│   ├── view_events.py             # ✅ NEW - View event definitions
│   └── state_coordinator.py       # ✅ Unchanged (for now)
└── ud_presenter.py                # ✅ Updated imports
```

### 📋 **Updated Import Statements**:
- All `from .utils.` → `from .pipeline.`
- `option_types` imports → `from ._view_components.option_types`
- New event system imports available

### 🎯 **Foundation Ready For**:
- Event-driven state management
- Component decoupling
- Local event coordination

---

## Rollback Plan

If issues arise during implementation:

1. **Revert directory rename**:
   ```bash
   mv pipeline utils
   ```

2. **Revert file move**:
   ```bash
   mv _view_components/option_types.py utils/option_types.py
   ```

3. **Revert import changes** using git:
   ```bash
   git checkout -- ud_presenter.py  # If committed before changes
   ```

4. **Remove new files**:
   ```bash
   rm services/local_event_bus.py
   rm _view_components/view_events.py
   ```

---

## Next Session Preview

**Session 1.2: Event Integration Setup** will focus on:
- Integrating local event bus with existing components
- Setting up event bridging to global bus
- Basic state coordinator event integration

---

## Session Checklist

Before starting:
- [ ] Backup current code (git commit)
- [ ] Close IDE to avoid file lock issues
- [ ] Have terminal ready for file operations

During session:
- [ ] Complete each task in order
- [ ] Validate each step before proceeding
- [ ] Test imports after each major change

After session:
- [ ] Run full test suite if available
- [ ] Commit changes with descriptive message
- [ ] Update task status
- [ ] Plan next session

**Ready to begin Session 1.1?** This foundation work will enable all subsequent event-driven architecture improvements.

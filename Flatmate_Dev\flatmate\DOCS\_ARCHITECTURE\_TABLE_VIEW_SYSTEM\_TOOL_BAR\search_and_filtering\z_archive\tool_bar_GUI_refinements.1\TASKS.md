# Toolbar GUI Refinements - Implementation Tasks

*Document Version: 1.0.0*  
*Created: 2025-07-19*  
*Status: Planning Phase*

This document breaks down the implementation of toolbar GUI refinements into atomic, testable tasks with specific code references.

## 1. Icon Management

### Task 1.1: Create Icon Resources
- **File**: `flatmate/resources/icons/toolbar/search.svg`
- **Action**: Add search icon SVG from Google Material icons
- **Acceptance**: Icon file exists and is valid SVG

### Task 1.2: Create Icon Resources
- **File**: `flatmate/resources/icons/toolbar/clear.svg`
- **Action**: Add clear icon SVG from Google Material icons
- **Acceptance**: Icon file exists and is valid SVG

### Task 1.3: Create Icon Resources
- **File**: `flatmate/resources/icons/toolbar/filter.svg`
- **Action**: Add filter icon SVG from Google Material icons
- **Acceptance**: Icon file exists and is valid SVG

### Task 1.4: Create Icon Resources
- **File**: `flatmate/resources/icons/toolbar/columns.svg`
- **Action**: Add columns icon SVG from Google Material icons
- **Acceptance**: Icon file exists and is valid SVG

### Task 1.5: Create Icon Helper
- **File**: `flatmate/src/fm/gui/_shared_components/icons/icon_loader.py`
- **Action**: Create or update icon loader utility to handle SVG icons
- **Acceptance**: Function can load SVG icons with proper sizing and coloring
- **Example**:
```python
def load_svg_icon(path, size=16, color=None):
    """Load an SVG icon with optional size and color adjustments."""
    if not os.path.exists(path):
        log.warning(f"Icon not found: {path}")
        return QIcon()
        
    icon = QIcon(path)
    if color:
        # Apply color filter if specified
        # Implementation depends on Qt version
        pass
    return icon
```

## 2. SearchBar Component

### Task 2.1: Create SearchBar Component
- **File**: `flatmate/src/fm/gui/_shared_components/search/search_bar.py`
- **Action**: Create SearchBar component with search icon, text field, and clear button
- **Acceptance**: Component renders correctly and emits proper signals
- **Example**:
```python
class SearchBar(QWidget):
    """Enhanced search bar with clear button and search icon."""
    
    search_changed = Signal(str)
    search_cleared = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # Search icon
        self.search_icon = QLabel()
        self.search_icon.setPixmap(QPixmap(":/icons/search.svg"))
        self.search_icon.setFixedSize(16, 16)
        layout.addWidget(self.search_icon)
        
        # Search field
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("Search...")
        layout.addWidget(self.search_field)
        
        # Clear button
        self.clear_button = QToolButton()
        self.clear_button.setIcon(QIcon(":/icons/clear.svg"))
        self.clear_button.setFixedSize(16, 16)
        self.clear_button.setVisible(False)
        layout.addWidget(self.clear_button)
        
    def _connect_signals(self):
        self.search_field.textChanged.connect(self._on_text_changed)
        self.clear_button.clicked.connect(self._on_clear_clicked)
        
    def _on_text_changed(self, text):
        self.clear_button.setVisible(bool(text))
        self.search_changed.emit(text)
        
    def _on_clear_clicked(self):
        self.search_field.clear()
        self.search_cleared.emit()
```

### Task 2.2: Add SearchBar Unit Tests
- **File**: `tests/gui/components/test_search_bar.py`
- **Action**: Create unit tests for SearchBar component
- **Acceptance**: Tests verify signal emission and UI state changes

## 3. ColumnSelector Component

### Task 3.1: Create ColumnSelector Component
- **File**: `flatmate/src/fm/gui/_shared_components/table/column_selector.py`
- **Action**: Create ColumnSelector component with dropdown menu
- **Acceptance**: Component renders correctly and emits proper signals
- **Example**:
```python
class ColumnSelector(QWidget):
    """Column visibility selector with dropdown menu."""
    
    columns_changed = Signal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._columns = {}  # {column_name: is_visible}
        self._init_ui()
        
    def _init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.button = QToolButton()
        self.button.setIcon(QIcon(":/icons/columns.svg"))
        self.button.setPopupMode(QToolButton.InstantPopup)
        
        self.menu = QMenu()
        self.button.setMenu(self.menu)
        
        layout.addWidget(self.button)
        
    def set_columns(self, columns, visible_columns=None):
        """Set available columns and currently visible ones."""
        self._columns = {col: col in (visible_columns or []) for col in columns}
        self._update_menu()
        
    def _update_menu(self):
        """Update the dropdown menu with current columns."""
        self.menu.clear()
        
        # Add "Select All" and "Select None" actions
        select_all = self.menu.addAction("Show All Columns")
        select_all.triggered.connect(self._select_all)
        
        select_none = self.menu.addAction("Hide All Columns")
        select_none.triggered.connect(self._select_none)
        
        self.menu.addSeparator()
        
        # Add individual column toggles
        for col_name, is_visible in self._columns.items():
            action = self.menu.addAction(col_name)
            action.setCheckable(True)
            action.setChecked(is_visible)
            action.triggered.connect(lambda checked, col=col_name: self._on_column_toggled(col, checked))
```

### Task 3.2: Add ColumnSelector Unit Tests
- **File**: `tests/gui/components/test_column_selector.py`
- **Action**: Create unit tests for ColumnSelector component
- **Acceptance**: Tests verify menu creation and column toggling

## 4. Transaction View Panel Integration

### Task 4.1: Update Transaction View Panel
- **File**: `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`
- **Action**: Integrate SearchBar and ColumnSelector into toolbar
- **Acceptance**: Components appear in toolbar with proper layout
- **Example**:
```python
def _init_toolbar(self):
    """Initialize the toolbar with search and column controls."""
    toolbar_layout = QHBoxLayout()
    toolbar_layout.setContentsMargins(8, 8, 8, 8)
    toolbar_layout.setSpacing(8)
    
    # Search bar
    self.search_bar = SearchBar()
    toolbar_layout.addWidget(self.search_bar)
    
    # Spacer
    toolbar_layout.addStretch(1)
    
    # Column selector
    self.column_selector = ColumnSelector()
    toolbar_layout.addWidget(self.column_selector)
    
    # Apply button
    self.apply_button = QPushButton("Apply")
    toolbar_layout.addWidget(self.apply_button)
    
    return toolbar_layout
```

### Task 4.2: Connect Toolbar Signals
- **File**: `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`
- **Action**: Connect SearchBar and ColumnSelector signals to handlers
- **Acceptance**: Signal connections are established
- **Example**:
```python
def _connect_signals(self):
    """Connect signals for toolbar components."""
    # Connect search bar signals
    self.search_bar.search_changed.connect(self._on_search_changed)
    self.search_bar.search_cleared.connect(self._on_search_cleared)
    
    # Connect column selector signals
    self.column_selector.columns_changed.connect(self._on_columns_changed)
    
    # Connect apply button
    self.apply_button.clicked.connect(self._on_apply_clicked)
```

## 5. Presenter Integration

### Task 5.1: Update Presenter for Toolbar
- **File**: `flatmate/src/fm/modules/categorize/cat_presenter.py`
- **Action**: Add methods to handle toolbar signals
- **Acceptance**: Presenter responds to toolbar events
- **Example**:
```python
def _handle_search_filter(self, search_text):
    """Handle search text changes from toolbar."""
    if not self.view or not hasattr(self.view, 'center_panel'):
        return
        
    # Apply filter to table view
    table = self.view.center_panel.transaction_table
    if table and hasattr(table, 'filter_by_text'):
        table.filter_by_text(search_text)
```

### Task 5.2: Add Configuration Persistence
- **File**: `flatmate/src/fm/modules/categorize/config/config.py`
- **Action**: Add configuration options for toolbar state
- **Acceptance**: Toolbar state is saved and restored
- **Example**:
```python
# Add default configuration values
config.ensure_defaults({
    'categorize.toolbar.remember_search': False,
    'categorize.toolbar.last_search': '',
    'categorize.toolbar.visible_columns': ['date', 'details', 'amount', 'account', 'tags'],
})
```

## 6. Styling and Visual Refinements

### Task 6.1: Create Toolbar Stylesheet
- **File**: `flatmate/src/fm/modules/categorize/_view/styles/toolbar_styles.py`
- **Action**: Create stylesheet for toolbar components
- **Acceptance**: Toolbar has consistent styling
- **Example**:
```python
TOOLBAR_STYLESHEET = """
    QWidget#toolbar {
        background-color: #f5f5f5;
        border-bottom: 1px solid #ddd;
    }
    
    QLineEdit {
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 4px 8px;
        background-color: white;
    }
    
    QToolButton {
        border: none;
        padding: 4px;
    }
    
    QToolButton:hover {
        background-color: #e0e0e0;
        border-radius: 4px;
    }
    
    QPushButton {
        background-color: #3B8A45;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
    }
    
    QPushButton:hover {
        background-color: #2D6934;
    }
"""
```

### Task 6.2: Implement Responsive Layout
- **File**: `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`
- **Action**: Add responsive behavior to toolbar
- **Acceptance**: Toolbar adapts to different window sizes

## 7. Testing and Validation

### Task 7.1: Create Integration Tests
- **File**: `tests/modules/categorize/test_toolbar_integration.py`
- **Action**: Create integration tests for toolbar functionality
- **Acceptance**: Tests verify toolbar works with transaction view

### Task 7.2: Create Visual Tests
- **File**: `tests/modules/categorize/test_toolbar_visual.py`
- **Action**: Create visual tests for toolbar appearance
- **Acceptance**: Tests verify toolbar appearance in different states

## 8. Documentation

### Task 8.1: Update User Documentation
- **File**: `flatmate/DOCS/user_guide/categorize_module.md`
- **Action**: Document toolbar functionality for users
- **Acceptance**: Documentation explains toolbar features and usage

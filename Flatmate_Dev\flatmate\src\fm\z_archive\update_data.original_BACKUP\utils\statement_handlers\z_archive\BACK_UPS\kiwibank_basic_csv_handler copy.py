from dataclasses import dataclass
from fm.core.data_services.standards.fm_standard_columns import StandardColumns
from ._base_statement_handler import StatementHandler


@dataclass
class KiwibankBasicCSVHandler(StatementHandler):
    """Handler for Kiwibank basic CSV statement format.
    
    Expected format:
    - First row: Account number
    - Data rows: Date, Details, [Empty], Amount, Balance
    - Date format: DD-MMM-YY (e.g., 13-Jun-24)
    - 5 columns total
    """
    
    def __init__(self):
        super().__init__()
        
        # Define the statement format
        self.statement_format = self.StatementFormat(
            bank_name="Kiwibank",
            variant="basic",
            file_type="csv"
        )
        
        # Configure columns
        self.column_attrs = self.ColumnAttributes(
            n_source_cols=5,
            has_col_names=False,
            has_account_column=False,
            source_col_names=[],
            target_col_names=[
                StandardColumns.DATE,
                StandardColumns.DETAILS,
                StandardColumns.EMPTY_COLUMN,
                StandardColumns.AMOUNT,
                StandardColumns.BALANCE
            ],
            date_format="%d-%b-%y"  # DD-MMM-YY format
        )
        
        # Configure account number detection
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"\d{2}-?\d{4}-?\d{7}-?\d{2,3}",
            in_file_name=True,
            in_header=True
        )
        
        # No metadata rows in this format
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=True # the first row (a1) is the account number This depends on whetehr it is run headless or not.
            #if using pd.read_csv() it will be the header
            #if using pd.read_csv() with header=None it will be the first row
        )

# Component Specification: SelectGroupWidget

**Version**: 1.0  
**Date**: 2025-07-25  
**Status**: Definition

## 1. Overview

The `SelectGroupWidget` is a composite QWidget designed to reduce code duplication by combining a label, a dropdown menu (`OptionMenuWithLabel`), and a selection button (`SecondaryButton`) into a single, configurable component. It is intended for use in forms where the user needs to select a directory or file path.

## 2. Technical Specification

*   **Class Name**: `SelectGroupWidget`
*   **Inherits From**: `PyQt5.QtWidgets.QWidget`
*   **Location**: `fm.modules.update_data._view.left_panel.widgets.select_group_widget`

### 2.1. Configuration

A `dataclass` or a simple `dict` will be used for configuration to ensure a clean and predictable API.

**Example `SelectGroupConfig`:**
```python
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class SelectGroupConfig:
    label_text: str
    button_text: str
    menu_options: List[str]
    default_option: Optional[str] = None
    object_name: str = "select_group_widget"
```

### 2.2. API and Methods

*   `__init__(self, config: SelectGroupConfig, parent=None)`: Constructor that takes a configuration object.
*   `value()`: Returns the currently selected option from the dropdown menu.
*   `setValue(text: str)`: Sets the current option in the dropdown menu.
*   `set_options(options: List[str])`: Clears and sets new options for the dropdown menu.

### 2.3. Signals

*   `button_clicked = pyqtSignal()`: Emitted when the selection button is clicked.
*   `selection_changed = pyqtSignal(str)`: Emitted when the dropdown menu selection changes.

### 2.4. Example Usage

```python
# In the parent widget (e.g., LeftPanel)
from .select_group_widget import SelectGroupWidget, SelectGroupConfig

# 1. Define the configuration
source_config = SelectGroupConfig(
    label_text="1. Source Files",
    button_text="Select...",
    menu_options=["Select Folder", "Select Files"],
    default_option="Select Folder"
)

# 2. Create an instance of the widget
self.source_group = SelectGroupWidget(source_config)

# 3. Connect signals
self.source_group.button_clicked.connect(self.on_source_select_clicked)
self.source_group.selection_changed.connect(self.on_source_type_changed)

# 4. Add to layout
self.layout.addWidget(self.source_group)
```

## 3. Implementation Notes

*   The widget should be composed of instances of `OptionMenuWithLabel` and `SecondaryButton` from the shared components library to maintain visual consistency.
*   Layout within the widget should be managed by a `QHBoxLayout` or `QGridLayout`.
*   The initial implementation will be local to the `update_data` module. It can be promoted to the global shared components library after it has been proven stable and effective.

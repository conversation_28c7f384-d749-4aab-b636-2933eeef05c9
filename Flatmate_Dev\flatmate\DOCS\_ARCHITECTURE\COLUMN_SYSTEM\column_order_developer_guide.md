# Column Order System - Developer Guide

**Version:** 1.0  
**Last Updated:** 2025-07-17  

## Quick Start

### Basic Usage

```python
from fm.core.data_services.standards.columns import Columns

# Get ordered columns for a module
ordered_cols = Columns.get_ordered_display_columns('categorize')
display_names = [col.display_name for col in ordered_cols]

# Reorder DataFrame before display
ordered_db_names = [col.db_name for col in ordered_cols]
available_cols = [col for col in ordered_db_names if col in df.columns]
df_ordered = df[available_cols]
```

### User Preferences

```python
from fm.core.data_services.standards.column_order_service import ColumnOrderService

service = ColumnOrderService()

# Get order with preference hierarchy
order = service.get_column_order('my_module')

# Set custom order
custom_order = ['date', 'amount', 'details', 'category']
service.set_column_order(custom_order, 'my_module')
```

## Architecture Overview

### Core Components

1. **StandardColumnsOrder** - Defines FM-standard ordering
2. **ColumnOrderService** - Manages user preferences
3. **Column** - Enhanced with order field
4. **Columns** - Registry with ordering methods

### Preference Hierarchy

1. **Module-specific** (`module.display.column_order`)
2. **Global default** (`display.column_order_global`)
3. **FM-standard** (built-in enum ordering)

## Integration Patterns

### Table View Integration

```python
def setup_table_view(self, df: pd.DataFrame, module_name: str):
    """Standard pattern for table view setup with ordering."""
    
    # 1. Get ordered columns
    ordered_columns = Columns.get_ordered_display_columns(module_name)
    ordered_db_names = [col.db_name for col in ordered_columns]
    
    # 2. Reorder DataFrame
    available_cols = [col for col in ordered_db_names if col in df.columns]
    remaining_cols = [col for col in df.columns if col not in available_cols]
    final_order = available_cols + remaining_cols
    df_ordered = df[final_order]
    
    # 3. Get display names
    visible_columns = [col.display_name for col in ordered_columns 
                      if col.db_name in df.columns]
    
    # 4. Configure table
    self.table.configure(
        default_visible_columns=visible_columns
    ).set_dataframe(df_ordered).show()
```

### Dropdown Integration

```python
def setup_column_dropdown(self, module_name: str):
    """Standard pattern for column selection dropdowns."""
    
    # Get ordered display columns
    ordered_cols = Columns.get_ordered_display_columns(module_name)
    
    # Populate dropdown in order
    for col in ordered_cols:
        self.dropdown.addItem(col.display_name, col.db_name)
```

## Adding New Columns

### Step 1: Add to StandardColumnsOrder

```python
# In column_order.py
class StandardColumnsOrder(Enum):
    # Existing columns...
    
    NEW_COLUMN = 25  # Choose appropriate priority value
```

### Step 2: Add to Columns Registry

```python
# In columns.py
class Columns:
    # Existing columns...
    
    NEW_COLUMN = Column(
        db_name='new_column',
        display_name='New Column',
        dtype=str,
        groups=['appropriate_group'],
        width=15,
        order=StandardColumnsOrder.NEW_COLUMN.value
    )
```

### Step 3: Update Order Service (if needed)

The ColumnOrderService automatically handles new columns through the enum system.

## Order Priority Guidelines

### Priority Ranges

- **1-10:** Core transaction data (Date, Details, Amount, etc.)
- **11-20:** User workflow fields (Category, Tags, Notes)
- **21-40:** Extended transaction details
- **41-60:** Party information (TP/OP details)
- **61-80:** Source/import metadata
- **81-99:** System fields
- **100+:** Special/utility columns

### Choosing Priority Values

1. **Consider user workflow** - What do users need to see first?
2. **Group related columns** - Keep similar columns together
3. **Leave gaps** - Allow for future insertions
4. **Follow existing patterns** - Maintain consistency

## User Preference Management

### Configuration Keys

```yaml
# Per-module preferences
categorize.display.column_order:
  - date
  - details
  - amount
  - category

# Global preferences  
display.column_order_global:
  - date
  - details
  - amount
  - account
```

### Programmatic Access

```python
service = ColumnOrderService()

# Check if user has preferences
has_module_pref = service.has_user_preference('categorize')
has_global_pref = service.has_user_preference()

# Get raw preference (no fallback)
user_pref = service.get_user_preference('categorize')

# Reset preferences
service.reset_to_default('categorize')  # Module-specific
service.reset_to_default()              # Global
```

## Testing Patterns

### Unit Testing

```python
def test_column_ordering():
    """Test column ordering functionality."""
    ordered_cols = Columns.get_ordered_display_columns('test_module')
    
    # Verify order
    orders = [col.order for col in ordered_cols]
    assert orders == sorted(orders), "Columns not in order"
    
    # Verify expected columns
    db_names = [col.db_name for col in ordered_cols]
    assert 'date' in db_names
    assert 'details' in db_names
```

### Integration Testing

```python
def test_dataframe_reordering():
    """Test DataFrame column reordering."""
    # Create test DataFrame
    df = pd.DataFrame({
        'category': [1, 2, 3],
        'date': [1, 2, 3],
        'amount': [1, 2, 3]
    })
    
    # Apply ordering
    ordered_cols = Columns.get_ordered_display_columns('test')
    ordered_db_names = [col.db_name for col in ordered_cols]
    available_cols = [col for col in ordered_db_names if col in df.columns]
    df_ordered = df[available_cols]
    
    # Verify order
    expected_order = ['date', 'amount', 'category']  # Based on priorities
    assert list(df_ordered.columns) == expected_order
```

## Common Patterns

### Module Setup

```python
class MyModuleView:
    def __init__(self):
        self.module_name = 'my_module'
    
    def display_data(self, df: pd.DataFrame):
        # Standard ordering pattern
        ordered_cols = Columns.get_ordered_display_columns(self.module_name)
        # ... rest of setup
```

### Preference Updates

```python
def on_column_order_changed(self, new_order: List[str]):
    """Handle user column reordering."""
    service = ColumnOrderService()
    service.set_column_order(new_order, self.module_name)
    
    # Refresh display
    self.refresh_table()
```

## Troubleshooting

### Common Issues

1. **Columns in wrong order**
   - Check DataFrame column reordering is applied
   - Verify order values in column definitions
   - Ensure table view preserves DataFrame order

2. **Missing columns**
   - Check column group assignments
   - Verify `get_display_columns()` includes all expected groups
   - Check DataFrame contains expected columns

3. **User preferences not working**
   - Verify YAML file permissions
   - Check preference key format
   - Ensure ColumnOrderService is used correctly

### Debug Helpers

```python
# Debug column ordering
def debug_column_order(module_name: str):
    ordered_cols = Columns.get_ordered_display_columns(module_name)
    for col in ordered_cols:
        print(f"{col.display_name}: order={col.order}, db_name={col.db_name}")

# Debug preferences
def debug_preferences(module_name: str):
    service = ColumnOrderService()
    print(f"Module pref: {service.get_user_preference(module_name)}")
    print(f"Global pref: {service.get_user_preference()}")
    print(f"Final order: {service.get_column_order(module_name)}")
```

## Performance Considerations

- **Caching:** ColumnOrderService caches preferences
- **DataFrame operations:** Column reordering is O(n) operation
- **Memory:** Minimal overhead for ordering metadata
- **Startup:** No significant impact on application startup

## Migration Guide

### From Alphabetical Ordering

1. **No code changes required** for basic functionality
2. **Optional:** Update to use `get_ordered_display_columns()`
3. **Recommended:** Add DataFrame reordering for table views

### From Custom Ordering

1. **Map existing order** to StandardColumnsOrder priorities
2. **Update column definitions** with order values
3. **Migrate user preferences** to new format if needed

---

**Need Help?**
- Check existing implementations in categorize module
- Review test files for usage examples
- Consult architecture documentation for design decisions

# UX Discussion: Auto-Import Feature

This document outlines the discussion and design decisions for the statement auto-import feature, focusing on user experience (UX).

## 1. The Core Problem

How should the application handle the automatic import of new statement files dropped into a monitored folder?

-   **User Insight**: The current PoC where files are immediately moved upon detection is disconcerting. It makes it difficult for the user to track which files they have downloaded and processed, reducing their sense of control.
-   **Key Conflict**: We must balance the convenience of automation with the user's need for control, visibility, and predictability.

## 2. Design Options

### Option A: Fully Automatic Background Import

-   **Mechanism**: A system watcher detects new files and immediately processes them, updating the database in the background, regardless of whether the UI is running.
-   **Pros**: Potentially zero-click import.
-   **Cons**: 
    -   **Poor UX**: Files disappear without user action, causing confusion (the "disconcerting" experience).
    -   **Error Prone**: How are import errors handled or communicated if the user isn't present?
    -   **Lack of Control**: The user cannot review or select which files to import.

### Option B: Guided, User-Initiated Import

-   **Mechanism**: The system detects new files but takes no immediate action. When the application is launched, it notifies the user that files are ready for import.
-   **Pros**:
    -   **User is in control**: The user explicitly triggers the import process.
    -   **Clear Feedback**: The workflow is transparent. The user sees the files, initiates the import, and receives confirmation.
    -   **Reduces Astonishment**: The application's behaviour is predictable and aligns with user expectations.
-   **Cons**: Requires one or two extra clicks from the user.

## 3. Recommendation & Proposed Workflow

**We will proceed with Option B.** The small cost of an extra click is vastly outweighed by the benefits of improved user control and confidence.

### Proposed Workflow

1.  **Configuration**: The user designates a specific "Import Folder" in the application's settings. This setting is easily accessible and changeable.
2.  **Detection**: The application monitors this folder for new files (e.g., `.csv`, `.pdf`).
3.  **Notification**: Upon launching the app, if new files are detected, the UI clearly indicates this:
    -   A notification badge appears on the "Update Data" navigation element.
    -   The application navigates to the "Update Data" screen, which lists the detected files.
4.  **User Action**: The user reviews the list of files and clicks an "Import Statements" button to begin processing.
5.  **Confirmation & Archiving**: 
    -   During the import, a progress indicator is shown.
    -   Upon successful completion, a summary is displayed (e.g., "Imported 3 statements, 52 transactions").
    -   **Only after success**, the source files are moved to a designated `archive` sub-folder. This preserves the original files while keeping the import folder clean.

This approach creates a clear, predictable, and empowering experience for the user.

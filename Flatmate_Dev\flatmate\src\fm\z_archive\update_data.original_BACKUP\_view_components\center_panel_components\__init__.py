"""Center panel components for Update Data module."""

# Export main components
from .welcome_pane import WelcomePane
from .file_pane import FilePane
from .data_pane import DataPane
from ._switcher import PanelSwitcher

# Import the center panel manager from the root level
# (This avoids circular imports since center_panel.py imports from this directory)

__all__ = [
    'WelcomePane',
    'FilePane',
    'DataPane',
    'PanelSwitcher'
]
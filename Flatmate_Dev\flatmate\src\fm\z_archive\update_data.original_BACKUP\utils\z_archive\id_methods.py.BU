import re
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

import pandas as pd

from ..processing_tracker import metadata_column_tracker

md_cols = metadata_column_tracker.get()

@dataclass
class BankFormatIdentifier:
    """
    Specification for identifying a bank account number.
    
    Args:
        location (dict): Specifies column and optional row for identifier
        pattern (str): Regex pattern to match the account number
        in_header (bool): Whether the identifier is in the header row
    """
    location: Dict[str, int]
    pattern: str
    in_header: bool = False

    def id_by_bank_account_number(self, df: pd.DataFrame) -> bool:
        """
        Validate the bank account number identifier.
        
        Args:
            df (pd.DataFrame): DataFrame to validate
        
        Returns:
            bool: Whether the account number matches the identifier rules
        """
        # Define expected metadata columns
         
        
        # Remove metadata columns for validation
        core_columns = [col for col in df.columns if col not in md_cols]
        
        try:
            # Determine identifier based on in_header
            if self.in_header:
                identifier = core_columns[self.location.get('col', 0)]
            else:
                identifier = df.iloc[
                    self.location.get('row', 0), 
                    self.location.get('col', 0)
                ]
            
            # Validate against pattern
            if not re.match(self.pattern, str(identifier)):
                print(f"Validation failed: Identifier does not match pattern")
                return False
        except (IndexError, KeyError):
            print(f"Validation failed: Could not extract identifier")
            return False
        
        return True

@dataclass
class BankFormat:
    """
    Defines the structure and validation rules for a bank statement format.
    
    Args:
        name (str): Name of the bank
        type (str): Type of CSV format
        min_columns (int): Minimum number of required columns
        identifier (BankFormatIdentifier): Rules to identify the bank format
        source_cols (List[str]): Expected source column names
        rename_cols (List[str]): Desired column names after renaming
    """
    name: str
    type: str
    min_columns: int = 5
    identifier: Optional[BankFormatIdentifier] = None
    source_cols: List[str] = field(default_factory=list)
    rename_cols: List[str] = field(default_factory=list)

    def validate(self, df: pd.DataFrame) -> bool:
        """
        Validate a DataFrame against this bank format specification.
        
        Args:
            df (pd.DataFrame): DataFrame to validate
        
        Returns:
            bool: Whether the DataFrame matches this format
        """
        #md_cols = metadata_column_tracker.get()
        #held by proceessing_tracker.metadata_column_tracker
        
        # Remove metadata columns for validation
        core_columns = [col for col in df.columns if col not in md_cols]
        
        # Check minimum columns
        if len(core_columns) < self.min_columns:
            print(f"Validation failed: Not enough columns. Need at least {self.min_columns}, got {len(core_columns)}")
            return False
        
        # Validate account number if specified
        if self.identifier:
            if not self.identifier.validate(df):
                return False
        
        return True

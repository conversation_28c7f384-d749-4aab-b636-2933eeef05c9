"""
Guide Pane Widget for Update Data Module.

State-driven contextual guidance system with event-driven updates
and visual state management.
"""

from typing import Dict, Any, Optional, List
from PySide6.QtCore import Qt, Signal, QObject
from PySide6.QtGui import QFont, QPalette, QTextCharFormat, QTextCursor
from PySide6.QtWidgets import QFrame, QLabel, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout, QGroupBox, QCheckBox

class GuidePaneWidget(BasePane):
    """
    State-driven contextual guidance widget.
    
    Manages UI state transitions and provides contextual messaging
    based on user actions and system state changes.
    """
    
    # State change signal
    message_changed = Signal(str)
    
    # Message templates for different states
    MESSAGE_TEMPLATES = {
        'initial': "Select a source folder or files to begin.",
        'folder_selected': "Found {count} CSV files ready for processing",
        'files_selected': "Selected {count} files for processing",
        'archive_same': "Files will be moved to 'Archive' subfolder in source location",
        'archive_custom': "Choose where to create 'Archive' folder for processed files",
        'ready': "Ready to process {count} files",
        'processing': "Processing file {current} of {total}...",
        'success': "Successfully processed {count} files",
        'error': "Error processing {filename}: {error}",
        'warning': "No compatible files found in selected location"
    }
    
    # Visual state colors
    STATE_COLORS = {
        'inactive': '#666666',
        'info': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'processing': '#2196F3',
        'success': '#4CAF50'
    }

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_state = 'initial'
        self.context_data = {}
        self._setup_ui()
        self._setup_event_handlers()

    def _setup_ui(self):
        """Set up the UI components with enhanced styling."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)

        # Guide frame with dynamic styling
        self.guide_frame = QFrame()
        self.guide_frame.setFrameShape(QFrame.Shape.Box)
        self.guide_frame.setObjectName("guide_frame")
        self._update_frame_style('inactive')

        frame_layout = QVBoxLayout(self.guide_frame)
        frame_layout.setContentsMargins(12, 12, 12, 12)

        # Rich text display for messages and options
        self.message_display = QTextEdit()
        self.message_display.setReadOnly(True)
        self.message_display.setMaximumHeight(120)
        self.message_display.setFont(QFont("Arial", 9))
        self.message_display.setObjectName("guide_message")
        self.message_display.setStyleSheet("""
            QTextEdit {
                border: none;
                background-color: transparent;
                color: #aaa;
            }
        """)
        
        # Options container for interactive elements
        self.options_container = QGroupBox("Options")
        self.options_container.setStyleSheet("""
            QGroupBox {
                border: 1px solid #555;
                border-radius: 4px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        self.options_container.setVisible(False)
        
        options_layout = QVBoxLayout(self.options_container)
        self.options_layout = options_layout
        
        frame_layout.addWidget(self.message_display)
        frame_layout.addWidget(self.options_container)
        layout.addWidget(self.guide_frame)

    def _setup_event_handlers(self):
        """Set up signal connections for state changes."""
        self.message_changed.connect(self._on_message_changed)

    def _update_frame_style(self, state: str):
        """Update frame styling based on current state."""
        color = self.STATE_COLORS.get(state, '#666666')
        self.guide_frame.setStyleSheet(f"""
            QFrame#guide_frame {{
                border: 2px solid {color};
                border-radius: 6px;
                background-color: #2a2a2a;
                padding: 8px;
            }}
        """)

    def _on_message_changed(self, message: str):
        """Handle message change events."""
        # Could trigger additional UI updates here
        pass

    def set_state(self, state: str, context: Optional[Dict[str, Any]] = None):
        """
        Set the current state and update message accordingly.
        
        Args:
            state: The new state identifier
            context: Additional context data for message formatting
        """
        self.current_state = state
        self.context_data = context or {}
        
        if state in self.MESSAGE_TEMPLATES:
            template = self.MESSAGE_TEMPLATES[state]
            message = template.format(**self.context_data)
            self.display(message)
            
        # Update visual styling based on state
        self._update_frame_style(state)

    def display(self, message: str, state: str = 'info', format_type: str = 'plain'):
        """Display a message with state-based styling and formatting."""
        if format_type == 'html':
            self.message_display.setHtml(message)
        else:
            self.message_display.setPlainText(message)
        
        self.message_changed.emit(message)
        
        # Update color based on state
        color = self.STATE_COLORS.get(state, '#aaa')
        self.message_display.setStyleSheet(f"""
            QTextEdit {{
                border: none;
                background-color: transparent;
                color: {color};
                font-weight: {'bold' if state in ['error', 'warning'] else 'normal'};
            }}
        """)
        
        # Update frame styling
        self._update_frame_style(state)

    def update_context(self, **kwargs):
        """Update context data and refresh current message."""
        self.context_data.update(kwargs)
        if self.current_state in self.MESSAGE_TEMPLATES:
            self.set_state(self.current_state, self.context_data)

    def show_processing_progress(self, current: int, total: int):
        """Show processing progress with formatted message."""
        self.set_state('processing', {'current': current, 'total': total})

    def show_success_summary(self, count: int):
        """Show success summary with file count and options."""
        self.set_state('success', {'count': count})
        self.show_options([
            {"text": "View Results", "action": "view_results"},
            {"text": "Process More Files", "action": "reset"}
        ])

    def show_error_details(self, filename: str, error: str):
        """Show error details with filename and error message."""
        self.set_state('error', {'filename': filename, 'error': error})

    def reset_to_initial(self):
        """Reset to initial state with enhanced welcome."""
        welcome_message = """<h3>Welcome to Update Data</h3>
        <p>This module helps you process and update your data files.</p>
        <p><b>Steps:</b></p>
        <ol>
            <li>Select your source files or folder</li>
            <li>Choose a save location</li>
            <li>Click Process to begin</li>
        </ol>"""
        self.display(welcome_message, 'info', 'html')
        self.clear_options()
        self.set_state('initial')

    def get_current_context(self) -> Dict[str, Any]:
        """Get current context data for external use."""
        return self.context_data.copy()

    def is_ready_for_processing(self) -> bool:
        """Check if guide indicates ready state."""
        return self.current_state == 'ready'

    def get_state(self) -> str:
        """Get current state identifier."""
        return self.current_state

    def show_options(self, options: List[Dict[str, str]]):
        """Display interactive options."""
        self.clear_options()
        for option in options:
            button = QPushButton(option['text'])
            button.clicked.connect(
                lambda checked, action=option['action']: self._on_option_selected(action)
            )
            self.options_layout.addWidget(button)
        self.options_container.setVisible(True)

    def clear_options(self):
        """Clear all option buttons."""
        while self.options_layout.count():
            child = self.options_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        self.options_container.setVisible(False)

    def _on_option_selected(self, action: str):
        """Handle option selection."""
        # Emit signal or call appropriate handler
        self.message_changed.emit(f"option_selected:{action}")

    def add_checkbox_option(self, label: str, checked: bool = False, key: str = None):
        """Add a checkbox option."""
        checkbox = QCheckBox(label)
        checkbox.setChecked(checked)
        if key:
            checkbox.stateChanged.connect(
                lambda state, k=key: self._on_checkbox_changed(k, state)
            )
        self.options_layout.addWidget(checkbox)
        self.options_container.setVisible(True)

    def _on_checkbox_changed(self, key: str, state: int):
        """Handle checkbox state changes."""
        self.context_data[key] = bool(state)
        self.message_changed.emit(f"checkbox_changed:{key}:{state}")

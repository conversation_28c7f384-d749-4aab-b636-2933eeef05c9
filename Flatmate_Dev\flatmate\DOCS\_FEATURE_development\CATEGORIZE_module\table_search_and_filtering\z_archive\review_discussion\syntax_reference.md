# Search Syntax Reference

## Overview
Complete reference for all search and filtering syntax patterns, organized by implementation phase.

---

## Phase 1: Basic Filtering (✅ IMPLEMENTED)

### AND Logic (Space-Separated)
**Syntax:** `term1 term2`  
**Meaning:** Both terms must be present  
**Examples:**
- `coffee shop` → Matches "Coffee Shop Purchase"
- `gas station` → Matches "Shell Gas Station"
- `grocery store` → Matches "Walmart Grocery Store"

### EXCLUDE Logic (Dash Prefix)
**Syntax:** `-term`  
**Meaning:** Exclude rows containing the term  
**Examples:**
- `-refund` → Excludes all refund transactions
- `coffee -starbucks` → Coffee transactions except Starbucks
- `-transfer -payment` → Excludes transfers and payments

### Combined AND + EXCLUDE
**Examples:**
- `restaurant -mcdonalds` → Restaurant transactions except McDonald's
- `fuel gas -credit` → Fuel/gas transactions not on credit card
- `grocery -walmart -costco` → Grocery shopping except specific stores

---

## Phase 2: Enhanced Search (🚧 IN PROGRESS)

### OR Logic (Pipe Operator)
**Syntax:** `term1|term2`  
**Meaning:** Either term can be present  
**Examples:**
- `coffee|tea` → Matches transactions with coffee OR tea
- `starbucks|dunkin` → Matches either coffee chain
- `gas|fuel|petrol` → Matches any fuel-related terms

### Mixed OR + AND
**Syntax:** `term1|term2 term3`  
**Meaning:** (term1 OR term2) AND term3  
**Examples:**
- `coffee|tea hot` → Hot coffee or hot tea
- `restaurant|cafe dinner` → Dinner at restaurant or cafe
- `gas|fuel station` → Gas station or fuel station

### OR + EXCLUDE
**Syntax:** `term1|term2 -exclude`  
**Meaning:** (term1 OR term2) AND NOT exclude  
**Examples:**
- `coffee|tea -decaf` → Coffee or tea, but not decaf
- `restaurant|cafe -fastfood` → Restaurant or cafe, but not fast food
- `shop|store -online` → Physical shops/stores, not online

---

## Phase 3: Advanced Search (📋 PLANNED)

### Explicit Boolean Operators
| Operator | Syntax | Example | Meaning |
|----------|--------|---------|---------|
| **AND** | `term1 AND term2` | `coffee AND shop` | Both terms required |
| **OR** | `term1 OR term2` | `coffee OR tea` | Either term acceptable |
| **NOT** | `term1 NOT term2` | `coffee NOT decaf` | First term, not second |

### Operator Synonyms
| Logic | Syntax Options | All Equivalent |
|-------|----------------|----------------|
| **AND** | `space`, `AND` | `coffee shop` = `coffee AND shop` |
| **OR** | `\|`, `/`, `OR` | `coffee\|tea` = `coffee/tea` = `coffee OR tea` |
| **NOT** | `-`, `NOT` | `-decaf` = `NOT decaf` |

### Parentheses Grouping
**Syntax:** `(expression)`  
**Examples:**
- `(coffee OR tea) AND hot` → Hot coffee or hot tea
- `restaurant AND (lunch OR dinner)` → Restaurant meals
- `(gas OR fuel) NOT (credit OR card)` → Cash fuel purchases

### Quoted Phrases
**Syntax:** `"exact phrase"`  
**Examples:**
- `"gas station"` → Exact phrase only, not "gas" and "station" separately
- `"late fee"` → Exact fee description
- `"coffee shop" OR "tea house"` → Exact business types

### Wildcard Patterns
**Syntax:** `term*`, `*term`, `*term*`  
**Examples:**
- `coff*` → coffee, coffees, coffeehouse
- `*market` → supermarket, market, minimarket
- `*shop*` → workshop, shopping, shopkeeper

---

## Phase 4: Power Features (🔮 FUTURE)

### Regular Expressions
**Syntax:** `/regex/`  
**Examples:**
- `/coff(ee|ees?)/` → coffee, coffees, coffe
- `/\d{4}-\d{2}-\d{2}/` → Date patterns
- `/^(gas|fuel)/i` → Lines starting with gas or fuel (case-insensitive)

### Column-Specific Operators
**Syntax:** `column:operator:value`  
**Examples:**
- `amount:>100` → Amounts greater than 100
- `date:2024-01` → Transactions in January 2024
- `account:checking` → Checking account transactions
- `tags:contains:food` → Transactions tagged with food

### Field Qualifiers
**Syntax:** `field:"value"`  
**Examples:**
- `description:"coffee shop"` → Exact phrase in description field
- `amount:">50"` → Amounts over 50
- `date:"last month"` → Relative date expressions

---

## Syntax Rules and Precedence

### Operator Precedence (High to Low)
1. **Parentheses** `()` - Highest precedence
2. **NOT/EXCLUDE** `-term`, `NOT term`
3. **AND** `space`, `AND`
4. **OR** `|`, `/`, `OR` - Lowest precedence

### Parsing Rules
- **Case Insensitive** - All matching is case-insensitive by default
- **Whitespace** - Extra spaces are ignored
- **Escaping** - Use quotes to include literal operators: `"term-with-dash"`
- **Empty Patterns** - Empty search shows all results

### Examples with Precedence
```
coffee tea OR hot          → (coffee AND tea) OR hot
coffee OR tea hot          → coffee OR (tea AND hot)
coffee OR tea -decaf       → (coffee OR tea) AND NOT decaf
(coffee OR tea) -decaf     → (coffee OR tea) AND NOT decaf
coffee AND (tea OR hot)    → coffee AND (tea OR hot)
```

---

## Error Handling

### Invalid Syntax
- **Unmatched Parentheses** - Graceful fallback to simple search
- **Invalid Operators** - Treat as literal search terms
- **Empty Groups** - `()` treated as no-op
- **Malformed Quotes** - Unclosed quotes treated as literal

### User Feedback
- **Syntax Highlighting** - Color-coded terms in input (Phase 3)
- **Error Messages** - Clear explanations for invalid patterns
- **Suggestions** - Auto-correct common mistakes
- **Fallback Behavior** - Always show some results when possible

---

## Performance Considerations

### Optimized Patterns
- **Simple AND** - Fastest: `coffee shop`
- **Simple OR** - Fast: `coffee|tea`
- **Basic EXCLUDE** - Fast: `coffee -decaf`

### Complex Patterns
- **Nested Groups** - Slower: `((coffee OR tea) AND hot) NOT decaf`
- **Wildcards** - Moderate: `coff*`
- **Regex** - Slowest: `/complex.*pattern/`

### Best Practices
- **Start Simple** - Use basic patterns first, add complexity as needed
- **Specific Terms** - More specific terms = faster results
- **Avoid Deep Nesting** - Keep parentheses grouping shallow
- **Test Performance** - Monitor response time with large datasets

---

## Migration Guide

### From Phase 1 to Phase 2
- **All existing syntax continues to work**
- **New OR operator available**: `coffee|tea`
- **Mixed expressions supported**: `coffee|tea hot`

### From Phase 2 to Phase 3
- **All Phase 1 & 2 syntax continues to work**
- **New explicit operators**: `AND`, `OR`, `NOT`
- **Enhanced grouping**: Complex parentheses expressions
- **Quoted phrases**: Exact matching capabilities

### From Phase 3 to Phase 4
- **All previous syntax continues to work**
- **New power features**: Regex, column operators, field qualifiers
- **Advanced UI**: Visual query builder, syntax highlighting

---

**This syntax reference provides a complete guide to search capabilities across all implementation phases, ensuring users can leverage the full power of the search system.**

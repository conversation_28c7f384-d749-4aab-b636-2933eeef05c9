"""
Centralized UI Mode Definitions for Update Data Module.

Implements Declarative Mode-Driven UI Architecture pattern.
All UI states are defined in immutable Pydantic models that drive the entire UI behavior.
"""

from enum import Enum
from pydantic import BaseModel
from typing import List, Optional, Dict

class UIMode(str, Enum):
    """Enumeration of all possible UI modes for Update Data module."""
    DATABASE_AUTO_IMPORT = "database_auto_import"
    DATABASE_MANUAL = "database_manual"
    FILE_UTILITY = "file_utility"

class UIElementState(BaseModel):
    """State configuration for a single UI element."""
    visible: bool = True
    enabled: bool = True
    text: Optional[str] = None
    options: Optional[List[str]] = None
    checked: Optional[bool] = None
    tooltip: Optional[str] = None

class ModeConfiguration(BaseModel):
    """Complete UI configuration for a specific mode."""
    # Source section
    source_combo: UIElementState
    source_button: UIElementState
    
    # Save section  
    save_combo: UIElementState
    save_button: UIElementState
    
    # Database section
    database_checkbox: UIElementState
    
    # Process section
    process_button: UIElementState
    
    # Center panel
    center_panel_type: str  # "welcome", "file_pane", "processing"
    
    class Config:
        frozen = True  # Immutable configurations

# Mode Definitions - Single Source of Truth for all UI states
MODE_CONFIGS: Dict[UIMode, ModeConfiguration] = {
    UIMode.DATABASE_AUTO_IMPORT: ModeConfiguration(
        source_combo=UIElementState(
            options=[
                "Auto Import Folder",
                "Select entire folder...", 
                "Select individual files...",
                "Set auto import folder..."
            ],
            text="Auto Import Folder",
            tooltip="Auto-import is configured and ready"
        ),
        source_button=UIElementState(
            text="Configure...",
            tooltip="Configure auto-import settings"
        ),
        save_combo=UIElementState(
            options=[
                "Archive Location",
                "Same as source", 
                "Select location..."
            ],
            text="Archive Location",
            tooltip="Files will be archived after processing"
        ),
        save_button=UIElementState(
            text="Select...",
            enabled=True,
            tooltip="Select archive location"
        ),
        database_checkbox=UIElementState(
            checked=True,
            text="Update Database",
            tooltip="Store processed transactions in the central database"
        ),
        process_button=UIElementState(
            text="Update Database",
            enabled=True,
            tooltip="Process files and update database"
        ),
        center_panel_type="file_pane"
    ),
    
    UIMode.DATABASE_MANUAL: ModeConfiguration(
        source_combo=UIElementState(
            options=[
                "Set auto import folder...",
                "Select entire folder...",
                "Select individual files..."
            ],
            text="Select entire folder...",
            tooltip="Choose source files manually"
        ),
        source_button=UIElementState(
            text="Select...",
            tooltip="Select source files or folder"
        ),
        save_combo=UIElementState(
            options=[
                "Archive Location",
                "Same as source",
                "Select location..."
            ],
            text="Archive Location",
            tooltip="Files will be archived after processing"
        ),
        save_button=UIElementState(
            text="Select...",
            enabled=True,
            tooltip="Select archive location"
        ),
        database_checkbox=UIElementState(
            checked=True,
            text="Update Database",
            tooltip="Store processed transactions in the central database"
        ),
        process_button=UIElementState(
            text="Update Database",
            enabled=True,
            tooltip="Process files and update database"
        ),
        center_panel_type="welcome"
    ),
    
    UIMode.FILE_UTILITY: ModeConfiguration(
        source_combo=UIElementState(
            options=[
                "Select entire folder...",
                "Select individual files..."
            ],
            text="Select entire folder...",
            tooltip="Choose source files for processing"
        ),
        source_button=UIElementState(
            text="Select...",
            tooltip="Select source files or folder"
        ),
        save_combo=UIElementState(
            options=[
                "Same as source",
                "Select location..."
            ],
            text="Same as source",
            tooltip="Processed files will be saved in the same location"
        ),
        save_button=UIElementState(
            text="Select...",
            enabled=False,  # Disabled when "Same as source"
            tooltip="Select save location (disabled when using same as source)"
        ),
        database_checkbox=UIElementState(
            checked=False,
            text="Update Database",
            tooltip="Store processed transactions in the central database"
        ),
        process_button=UIElementState(
            text="Process Files",
            enabled=True,
            tooltip="Process files without updating database"
        ),
        center_panel_type="welcome"
    )
}

def get_mode_config(mode: UIMode) -> ModeConfiguration:
    """Get configuration for specified mode."""
    return MODE_CONFIGS[mode]

def determine_mode(auto_import_enabled: bool, database_enabled: bool, has_pending_files: bool = False) -> UIMode:
    """
    Determine UI mode based on current settings.
    
    Args:
        auto_import_enabled: Whether auto-import is configured and enabled
        database_enabled: Whether database update is enabled
        has_pending_files: Whether there are pending auto-import files
        
    Returns:
        The appropriate UI mode
    """
    if database_enabled:
        if auto_import_enabled and has_pending_files:
            return UIMode.DATABASE_AUTO_IMPORT
        else:
            return UIMode.DATABASE_MANUAL
    else:
        return UIMode.FILE_UTILITY

def get_all_modes() -> List[UIMode]:
    """Get list of all available modes."""
    return list(UIMode)

def validate_mode_configs() -> bool:
    """
    Validate that all mode configurations are properly defined.
    
    Returns:
        True if all configurations are valid
        
    Raises:
        ValueError: If any configuration is invalid
    """
    for mode in UIMode:
        if mode not in MODE_CONFIGS:
            raise ValueError(f"Missing configuration for mode: {mode}")
        
        config = MODE_CONFIGS[mode]
        
        # Validate required fields
        if not config.center_panel_type:
            raise ValueError(f"Missing center_panel_type for mode: {mode}")
        
        # Validate center panel type
        valid_panel_types = ["welcome", "file_pane", "processing"]
        if config.center_panel_type not in valid_panel_types:
            raise ValueError(f"Invalid center_panel_type '{config.center_panel_type}' for mode: {mode}")
    
    return True

# Validate configurations on import
validate_mode_configs()

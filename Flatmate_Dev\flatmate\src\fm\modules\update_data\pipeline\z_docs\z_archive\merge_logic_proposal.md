# Proposal: Refactor `merge_dataframes` Deduplication Logic

**Date:** 2025-07-10

## 1. Goal

To correct the deduplication logic within the `merge_dataframes` function in `dw_pipeline.py` to align with the strategy documented in `report.md`.

## 2. The Problem

The current implementation of `merge_dataframes` does not correctly apply the prioritized deduplication strategy. 

- It gathers all potential key columns (`Unique Id`, `Date`, `Details`, `Amount`, `Balance`) into a single list and uses the entire list for deduplication.
- This is incorrect because it does not prioritize using the `Unique Id` column, which is the most reliable identifier for a transaction.
- This can lead to incorrect behavior, where the presence of a less reliable column could interfere with a more reliable one.

## 3. Proposed Solution

I propose to refactor the logic to follow a clear, prioritized sequence:

1.  **Prioritize `Unique Id`**: The function will first check if the `Unique Id` column exists and contains at least one non-null value. If it does, deduplication will be performed **only** on the `Unique Id` column. This is the primary and most accurate strategy.

2.  **Fallback to Composite Key**: If the `Unique Id` column is absent or entirely empty, the function will fall back to using a composite key for deduplication. As per the `report.md`, this key will be: `['Date', 'Details', 'Amount', 'Balance']`.

3.  **No Key Found**: If neither the primary nor the fallback keys can be constructed, no deduplication will occur, and a warning will be logged.

## 4. Reasoning

-   **Aligns with Design**: This change implements the exact logic specified in your `report.md` (Section 1.3, Item 7), which the previous AI failed to restore. It brings the code back in line with the agreed-upon design.
-   **Improves Accuracy**: Prioritizing `Unique Id` is critical for accuracy. It is the strongest guarantee that a transaction is unique. The composite key is a reliable fallback but is secondary to a dedicated ID.
-   **Increases Robustness**: This clear, hierarchical logic makes the system more robust and predictable. It ensures that the best available data is used for the critical task of removing duplicates, preventing both data loss (from over-aggressive deduplication) and data corruption (from missed duplicates).

# Review & Discussion

Ongoing design decisions, review comments, and open questions for the table search and filtering feature.

## 2025-07-19
- Filtering performance and live update logic need review (see `test_results.md`).
- Columns visible to users must be pruned to exclude internal/system fields.
- Further discussion required on UI/UX for advanced search expressions and column management.

---
(Add new dated entries below as review proceeds)

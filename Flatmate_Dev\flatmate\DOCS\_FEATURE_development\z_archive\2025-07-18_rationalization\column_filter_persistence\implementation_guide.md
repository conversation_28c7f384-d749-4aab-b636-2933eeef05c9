# Implementation Guide

## 1. Persistence and Column Selection
- In the parent widget (e.g., `CustomTableView_v2`), on filter or column change, save current column/pattern to config or QSettings if enabled.
- On widget init, parent widget checks config and restores last-used filter and column if persistence is enabled.
- Default column is 'details' if no prior selection.
- FilterGroup is a passive UI component, state set externally.
- Config key: `table.filter.remember_last` (default: True).

## 2. AND/EXCLUDE Logic
- In `EnhancedFilterProxyModel.filterAcceptsRow`, parse pattern:
    - Split by spaces.
    - Terms starting with '-' (surrounded by spaces) are exclude; others are AND.
    - If '-' is not surrounded by spaces, treat as part of search term.
    - Quotes or * prefix force literal search for rare cases.
- Match: all AND terms must be present in row (case-insensitive, substring); none of the EXCLUDE terms may be present.

## 3. UI Operator Hint
- In `FilterInput`, update placeholder to: `foo bar = AND, -foo = exclude. Operators must be spaced. Use quotes/* for literal.`
- Optionally add tooltip for more detail.
- UI must make it clear that dropdown is selectable, persists, and defaults to details.

## 4. Testing
- Set filter, close/reopen app/module, confirm persistence.
- Test AND/exclude logic: 'foo bar', 'foo -bar', '-foo bar', etc.
- Toggle config, confirm persistence disables/enables.
- Confirm UI hint is visible and clear.

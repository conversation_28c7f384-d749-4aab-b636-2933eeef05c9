#!/usr/bin/env python3
"""
Safe archive script for update_data cleanup - reversible
"""
import os
import shutil
import datetime
from pathlib import Path

def main():
    base_dir = Path(r"c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate")
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_dir = base_dir / f"z_archive_update_data_{timestamp}"
    archive_dir.mkdir(exist_ok=True)
    
    # Safe files to archive (non-breaking)
    safe_files = [
        "src/fm/modules/update_data/_view/__pycache__",
        "src/fm/modules/update_data/_view/components/__pycache__",
        "src/fm/modules/update_data/_view/right_panel/__pycache__",
        "src/fm/modules/update_data/_view/ud_view_BACKUP_2025-07-25.py",
        "src/fm/modules/update_data/_view/ud_presenter_BACKUP_2025-07-25.py",
        "src/fm/modules/update_data/_view/state_engine.py",
        "src/fm/modules/update_data/_view/update_data_events.py",
        "src/fm/modules/update_data/_view/.pytest_cache",
        "src/fm/modules/update_data/_view/build",
        "src/fm/modules/update_data/_view/dist",
    ]
    
    archived = 0
    for item in safe_files:
        source = base_dir / item
        if source.exists():
            target = archive_dir / item
            target.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(target))
            print(f"Archived: {item}")
            archived += 1
    
    print(f"Archive complete: {archived} items")
    print(f"Location: {archive_dir}")

if __name__ == "__main__":
    main()

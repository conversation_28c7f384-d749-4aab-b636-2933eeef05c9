# Update Data Refactoring: Implementation Guide
**Date**: 2025-07-28  
**Version**: 1.0  
**Status**: Ready for Implementation

## Overview

This guide provides a step-by-step implementation plan for resolving the critical issues in the update_data module refactoring and completing the guide_pane component integration. It focuses on breaking circular imports, establishing proper component boundaries, and creating a testable architecture.

## Phase 1: Break Circular Imports (Priority 1)

### Task 1.1: Create Interface Abstractions

**File**: `src/fm/modules/update_data/interfaces.py`

```python
"""
Interface definitions for Update Data module.

Provides abstract interfaces to break circular dependencies.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from PySide6.QtCore import QObject, Signal


class IUpdateDataView(ABC):
    """Interface for Update Data View."""
    
    @abstractmethod
    def setup_ui(self) -> None:
        """Set up the UI components."""
        pass
    
    @abstractmethod
    def show_component(self) -> None:
        """Show this component."""
        pass
    
    @abstractmethod
    def hide_component(self) -> None:
        """Hide this component."""
        pass
    
    @abstractmethod
    def set_processing_state(self, is_processing: bool) -> None:
        """Set the processing state of the UI."""
        pass
    
    @abstractmethod
    def show_error(self, message: str) -> None:
        """Show an error message."""
        pass
    
    @abstractmethod
    def show_success(self, message: str) -> None:
        """Show a success message."""
        pass


class IUpdateDataPresenter(ABC):
    """Interface for Update Data Presenter."""
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize the presenter."""
        pass
    
    @abstractmethod
    def show(self) -> None:
        """Show the module."""
        pass
    
    @abstractmethod
    def hide(self) -> None:
        """Hide the module."""
        pass
    
    @abstractmethod
    def process_files(self) -> None:
        """Process the selected files."""
        pass
```

### Task 1.2: Modify Module Coordinator

**File**: `src/fm/module_coordinator.py`

Modify to use interface instead of concrete implementation:

```python
# Replace this import
from .modules.update_data.ud_presenter import UpdateDataPresenter

# With this import
from .modules.update_data.interfaces import IUpdateDataPresenter
```

And use dependency injection:

```python
def _initialize_modules(self):
    """Initialize all modules."""
    # Get concrete implementations from factories
    self.update_data_presenter = self._create_update_data_presenter()
    
def _create_update_data_presenter(self) -> IUpdateDataPresenter:
    """Create the update data presenter."""
    # Import here to avoid circular imports
    from .modules.update_data.ud_presenter import UpdateDataPresenter
    return UpdateDataPresenter(self.main_window)
```

### Task 1.3: Fix Guide Pane Component

**File**: `src/fm/modules/update_data/_view_components/center_panel_components/guide_pane.py`

Ensure it can be imported and tested in isolation:

1. Fix BasePane import ✓
2. Create missing dependencies
3. Ensure proper signal connections

## Phase 2: Component Implementation (Priority 2)

### Task 2.1: Create Visual Test for Guide Pane

**File**: `src/fm/modules/update_data/_view_components/center_panel_components/test_guide_pane_visual.py`

Create a standalone test that can be run to visualize the guide pane:

```python
#!/usr/bin/env python3
"""
Visual test for GuidePaneWidget.

Run this script to see the guide pane in action.
"""

import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parents[5]
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from fm.modules.update_data._view_components.center_panel_components.guide_pane import GuidePaneWidget


class TestWindow(QMainWindow):
    """Test window for guide pane."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Guide Pane Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central = QWidget()
        self.setCentralWidget(central)
        
        # Layout
        layout = QVBoxLayout(central)
        
        # Guide pane
        self.guide_pane = GuidePaneWidget()
        layout.addWidget(self.guide_pane)
        
        # Test buttons
        btn_initial = QPushButton("Initial State")
        btn_initial.clicked.connect(self.guide_pane.reset_to_initial)
        layout.addWidget(btn_initial)
        
        btn_folder = QPushButton("Folder Selected")
        btn_folder.clicked.connect(lambda: self.guide_pane.set_state("folder_selected", {"count": 5}))
        layout.addWidget(btn_folder)
        
        # Show initial state
        self.guide_pane.reset_to_initial()


def main():
    """Run the test."""
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
```

### Task 2.2: Implement State Management

**File**: `src/fm/modules/update_data/state/state_coordinator.py`

Create a simple state coordinator to manage UI state:

```python
"""
Simple state coordinator for Update Data module.

Manages UI state transitions and provides a clean interface
for state-driven UI updates.
"""

from typing import Dict, Any, Optional, List, Callable
from enum import Enum, auto
from PySide6.QtCore import QObject, Signal


class UIState(Enum):
    """UI states for Update Data module."""
    INITIAL = auto()
    SOURCE_SELECTED = auto()
    DESTINATION_SELECTED = auto()
    READY = auto()
    PROCESSING = auto()
    SUCCESS = auto()
    ERROR = auto()


class StateCoordinator(QObject):
    """
    Simple state coordinator for Update Data module.
    
    Manages UI state transitions and provides a clean interface
    for state-driven UI updates.
    """
    
    # Signals
    state_changed = Signal(UIState, dict)
    
    def __init__(self):
        super().__init__()
        self.current_state = UIState.INITIAL
        self.context = {}
        self._state_handlers = {}
        
    def register_handler(self, state: UIState, handler: Callable[[dict], None]):
        """Register a handler for a state."""
        self._state_handlers[state] = handler
        
    def transition_to(self, state: UIState, context: Optional[Dict[str, Any]] = None):
        """Transition to a new state."""
        self.current_state = state
        self.context.update(context or {})
        self.state_changed.emit(state, self.context.copy())
        
        # Call handler if registered
        if state in self._state_handlers:
            self._state_handlers[state](self.context)
```

## Phase 3: Integration & Testing (Priority 3)

### Task 3.1: Create Integration Test

**File**: `src/fm/modules/update_data/tests/test_guide_pane_integration.py`

Test the guide pane integration with the state coordinator:

```python
"""
Integration test for guide pane and state coordinator.
"""

import pytest
from PySide6.QtWidgets import QApplication
import sys

from fm.modules.update_data._view_components.center_panel_components.guide_pane import GuidePaneWidget
from fm.modules.update_data.state.state_coordinator import StateCoordinator, UIState


class TestGuideStateIntegration:
    """Test guide pane integration with state coordinator."""
    
    @pytest.fixture
    def app(self):
        """Create QApplication for tests."""
        return QApplication.instance() or QApplication(sys.argv)
    
    @pytest.fixture
    def guide_pane(self, app):
        """Create guide pane instance."""
        widget = GuidePaneWidget()
        yield widget
        widget.deleteLater()
    
    @pytest.fixture
    def state_coordinator(self):
        """Create state coordinator instance."""
        return StateCoordinator()
    
    def test_state_transitions(self, guide_pane, state_coordinator):
        """Test state transitions update guide pane."""
        # Connect state coordinator to guide pane
        state_coordinator.state_changed.connect(
            lambda state, context: self._handle_state_change(guide_pane, state, context)
        )
        
        # Initial state
        state_coordinator.transition_to(UIState.INITIAL)
        assert guide_pane.current_state == "initial"
        
        # Source selected
        state_coordinator.transition_to(UIState.SOURCE_SELECTED, {"count": 5})
        assert guide_pane.current_state == "folder_selected"
        assert "5" in guide_pane.message_display.toPlainText()
    
    def _handle_state_change(self, guide_pane, state, context):
        """Handle state changes."""
        if state == UIState.INITIAL:
            guide_pane.reset_to_initial()
        elif state == UIState.SOURCE_SELECTED:
            guide_pane.set_state("folder_selected", context)
```

## Implementation Checklist

### Phase 1: Foundation Repair
- [ ] Create interface abstractions
- [ ] Modify module coordinator
- [ ] Fix guide pane component

### Phase 2: Component Implementation
- [ ] Create visual test for guide pane
- [ ] Implement state management
- [ ] Connect guide pane to state coordinator

### Phase 3: Integration & Testing
- [ ] Create integration test
- [ ] Verify state transitions
- [ ] Document component usage

## Success Criteria

- [ ] No circular import errors
- [ ] Guide pane can be imported and tested in isolation
- [ ] Visual test shows proper state transitions
- [ ] Integration test passes
- [ ] Clear path forward for remaining components

---

*This implementation guide provides a concrete plan for resolving the critical issues in the update_data module refactoring and completing the guide_pane component integration.*

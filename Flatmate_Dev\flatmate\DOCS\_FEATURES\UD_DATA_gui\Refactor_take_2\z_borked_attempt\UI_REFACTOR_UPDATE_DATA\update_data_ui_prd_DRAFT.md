# Update Data UI Redesign - Product Requirements Document (PRD)

**Version**: 1.0  
**Date**: 2025-07-24  
**Status**: DRAFT  
**Priority**: HIGH (MVP ASAP)  

## 🎯 **Product Overview**

### **Problem Statement**
Current Update Data UI lacks folder monitoring and requires manual file selection each time. Users want a streamlined workflow that monitors a folder and queues files for user-initiated import.
>>untruet has it and it is already implemented.. its not represented in the gui 
which is the issue also we are moving to a more database centric design from a merge file architecture.

### **Solution Summary**
Redesign Update Data UI with folder monitoring that detects files and adds them to an import queue. User maintains full control - no automatic imports, all processing requires explicit user action.
>> CEO we already have the folder monitering implemented review the code base this is about oprimising the gui - generally and for this functionality 

### **Success Criteria**
- ✅ User selects folder once, app monitors continuously
- ✅ New files automatically added to import queue (no auto-import)
- ✅ User reviews queue and initiates import when ready
- ✅ Existing file display widget shows queued files
- ✅ All processing requires explicit user action

## 🔧 **Functional Requirements**

### **FR1: Folder Selection & Monitoring**
- **FR1.1**: User selects source option via left panel dropdown, once a folder is selected is presented with option to monoiter 
- **FR1.1.1**: once folder or files are selected they are added to the queue
- **FR1.2**: if monitoring is enabled, App monitors selected folder for new files
- **FR1.3**: Folder selection persists between app sessions and is displayed in optionMenu
- **FR1.4**: User can change monitored folder at any time

### **FR2: File Detection & Queueing**
- **FR2.1**: New files automatically detected in monitored folder
- **FR2.2**: Valid files added to import queue (no auto-import)
- **FR2.3**: Queue displayed in center panel using existing file display widget
- **FR2.4**: Invalid files ignored with optional logging

### **FR3: User-Initiated Processing**
- **FR3.1**: "PROCESS FILES" button enabled only when files in queue
- **FR3.2**: User reviews queue before initiating import
- **FR3.3**: All imports require explicit user action (no automation)
- **FR3.4**: Progress feedback during processing

### **FR4: Archive Management**
- **FR4.1**: Processed files moved to archive folder
- **FR4.2**: Archive location configurable (default: same as source)
- **FR4.3**: Archive folder created automatically if needed
- **FR4.4**: File naming conflicts handled gracefully

## 🎨 **User Interface Requirements**

### **UI1: Left Panel Layout**
- **UI1.1**: Source Files section with dropdown and select button
- **UI1.2**: Archive section (conditional, shows when source set)
- **UI1.3**: Processing options (Update Database checkbox)
- **UI1.4**: PROCESS FILES button (main action)

### **UI2: Center Panel Display**
- **UI2.1**: File queue display using existing file display widget
- **UI2.2**: Monitoring status indicator
- **UI2.3**: Progress feedback during processing
- **UI2.4**: Success/completion summary

### **UI3: Widget Specifications**
- **UI3.1**: Use existing shared components (`OptionMenuWithLabel`, `SecondaryButton`, etc.)
- **UI3.2**: Maintain current variable names (`self.source_menu`, `self.save_menu`, etc.)
- **UI3.3**: Consistent styling with application theme
- **UI3.4**: Responsive layout for different screen sizes

## 🔄 **User Workflow**

### **Initial Setup**
1. User opens Update Data module
2. User selects source folder from dropdown
3. User optionally configures archive location
4. App begins monitoring selected folder

### **Daily Operation**
1. User adds files to monitored folder (external action)
2. App detects files and adds to queue
3. User opens Update Data to review queue
4. User clicks "PROCESS FILES" to import
5. App processes files and moves to archive
6. User sees completion summary
7. App continues monitoring for next files

### **Error Handling**
1. Invalid files ignored (with optional notification)
2. Processing errors displayed with retry option
3. Archive conflicts resolved with user input
4. Folder access issues reported clearly

## 🏗️ **Technical Requirements**

### **TR1: State Management**
- **TR1.1**: CSV-based state table defines UI behavior
- **TR1.2**: State engine drives UI transitions
- **TR1.3**: All component states defined in machine-readable format
- **TR1.4**: Easy modification of behavior via CSV updates

### **TR2: Component Architecture**
- **TR2.1**: Reuse existing widget base classes
- **TR2.2**: Proposed SelectGroupWidget for repeated patterns
- **TR2.3**: No abstract base classes (user preference)
- **TR2.4**: Simple inheritance and configuration

### **TR3: File Monitoring**
- **TR3.1**: Efficient folder watching (Qt QFileSystemWatcher)
- **TR3.2**: File type validation before queueing
- **TR3.3**: Duplicate detection and handling
- **TR3.4**: Graceful handling of folder access issues

### **TR4: Data Integration**
- **TR4.1**: Integration with existing database update logic
- **TR4.2**: Maintain existing file processing pipeline
- **TR4.3**: Preserve current data validation rules
- **TR4.4**: No changes to core data processing

## 🚫 **Non-Functional Requirements**

### **Performance**
- **NFR1**: File detection within 5 seconds of file creation
- **NFR2**: UI remains responsive during file processing
- **NFR3**: Minimal memory footprint for folder monitoring
- **NFR4**: Fast startup time (no blocking operations)

### **Usability**
- **NFR5**: Intuitive workflow requiring no training
- **NFR6**: Clear visual feedback for all user actions
- **NFR7**: Consistent with existing application UX
- **NFR8**: Accessible to non-technical users

### **Reliability**
- **NFR9**: Robust error handling and recovery
- **NFR10**: No data loss during processing failures
- **NFR11**: Graceful degradation if monitoring fails
- **NFR12**: Comprehensive logging for troubleshooting

## 🚨 **Constraints & Assumptions**

### **Technical Constraints**
- **C1**: Must use existing Qt widget framework
- **C2**: No external dependencies allowed
- **C3**: Must integrate with current database system
- **C4**: Maintain backward compatibility

### **Business Constraints**
- **C5**: Zero budget for external tools or libraries
- **C6**: One-person development team
- **C7**: MVP delivery required ASAP
- **C8**: No breaking changes to existing functionality

### **User Constraints**
- **C9**: No automatic imports (user must initiate)
- **C10**: Must work with existing file types
- **C11**: Simple configuration (minimal setup)
- **C12**: Clear visual feedback required

## 📋 **Acceptance Criteria**

### **MVP Acceptance**
- [ ] User can select and monitor a folder
- [ ] Files detected and added to queue automatically
- [ ] User can review queue before processing
- [ ] Processing initiated only by user action
- [ ] Files moved to archive after processing
- [ ] UI uses existing widgets and styling
- [ ] No automatic imports occur

### **Quality Acceptance**
- [ ] No crashes or data loss during normal operation
- [ ] Clear error messages for all failure scenarios
- [ ] Responsive UI during file processing
- [ ] Consistent with existing application UX
- [ ] Works with all supported file types

## 🔄 **Future Enhancements (Post-MVP)**

### **Phase 2 Features**
- Multiple folder monitoring
- Advanced file filtering options
- Batch processing controls
- Enhanced progress reporting

### **Phase 3 Features**
- Scheduled processing
- Email notifications
- Advanced archive management
- Integration with external systems

---

## 📝 **Implementation Notes**

### **Development Approach**
- State-driven architecture using CSV tables
- Incremental development with user validation
- Reuse existing components wherever possible
- KISS principles throughout

### **Testing Strategy**
- Unit tests for state engine
- Integration tests for file monitoring
- User acceptance testing for workflow
- Performance testing for large file sets

### **Rollout Plan**
- Internal testing with sample files
- User validation of workflow
- Gradual rollout with monitoring
- Documentation and training materials

**Status**: Ready for implementation planning and development

# Toolbar GUI Refinements - Implementation Guide

*Document Version: 1.0.0*
*Created: 2025-07-19*
*Status: Implementation Phase*

This guide provides step-by-step technical instructions for implementing the toolbar GUI refinements in the Categorize module.

## Prerequisites

- Familiarity with PySide6 and Qt
- Understanding of the Categorize module architecture
- Access to Google Material Design SVG icons

## Implementation Steps

### Step 1: Set Up Icon Resources

1. Create the toolbar icons directory if it doesn't exist:

   ```bash
   mkdir -p flatmate/resources/icons/toolbar
   ```
2. Download the following SVG icons from Google Material Design:

   - Search icon: `search.svg`
   - Clear icon: `clear.svg`
   - Filter icon: `filter.svg`
   - Columns icon: `view_column.svg`
3. Place the icons in the `flatmate/resources/icons/toolbar/` directory.
4. Create or update the icon loader utility in `flatmate/src/fm/gui/_shared_components/icons/icon_loader.py`:

   ```python
   import os
   from PySide6.QtGui import QIcon, QPixmap
   from PySide6.QtCore import QSize
   from fm.core.services.logger import log

   def get_icon_path(icon_name, category="toolbar"):
       """Get the full path to an icon file."""
       base_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(
           os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))), 
           "resources", "icons", category)

       # Check for SVG version first
       svg_path = os.path.join(base_path, f"{icon_name}.svg")
       if os.path.exists(svg_path):
           return svg_path

       # Fall back to PNG if SVG doesn't exist
       png_path = os.path.join(base_path, f"{icon_name}.png")
       if os.path.exists(png_path):
           return png_path

       log.warning(f"Icon not found: {icon_name} in category {category}")
       return None

   def load_icon(icon_name, category="toolbar", size=16):
       """Load an icon with the specified size."""
       path = get_icon_path(icon_name, category)
       if not path:
           return QIcon()

       icon = QIcon(path)
       return icon
   ```

### Step 2: Create the SearchBar Component

>> again - we already have one ( I thought - its in the table view - but perhaps its not properly seperated?)
>>

1. Create the file `flatmate/src/fm/gui/_shared_components/search/search_bar.py`:

   ```python
   from PySide6.QtCore import Signal, Qt
   from PySide6.QtWidgets import QWidget, QHBoxLayout, QLineEdit, QToolButton, QLabel
   from PySide6.QtGui import QIcon, QPixmap

   from fm.core.services.logger import log
   from fm.gui._shared_components.icons.icon_loader import load_icon

   class SearchBar(QWidget):
       """Enhanced search bar with clear button and search icon."""

       search_changed = Signal(str)
       search_cleared = Signal()

       def __init__(self, parent=None):
           super().__init__(parent)
           self._init_ui()
           self._connect_signals()

       def _init_ui(self):
           layout = QHBoxLayout(self)
           layout.setContentsMargins(0, 0, 0, 0)
           layout.setSpacing(2)

           # Search icon
           self.search_icon = QLabel()
           search_icon = load_icon("search")
           self.search_icon.setPixmap(search_icon.pixmap(16, 16))
           self.search_icon.setFixedSize(16, 16)
           layout.addWidget(self.search_icon)

           # Search field
           self.search_field = QLineEdit()
           self.search_field.setPlaceholderText("Search...")
           layout.addWidget(self.search_field)

           # Clear button
           self.clear_button = QToolButton()
           self.clear_button.setIcon(load_icon("clear"))
           self.clear_button.setFixedSize(16, 16)
           self.clear_button.setVisible(False)
           layout.addWidget(self.clear_button)

       def _connect_signals(self):
           self.search_field.textChanged.connect(self._on_text_changed)
           self.clear_button.clicked.connect(self._on_clear_clicked)

       def _on_text_changed(self, text):
           self.clear_button.setVisible(bool(text))
           self.search_changed.emit(text)

       def _on_clear_clicked(self):
           self.search_field.clear()
           self.search_cleared.emit()

       def set_text(self, text):
           """Set the search text programmatically."""
           self.search_field.setText(text)

       def get_text(self):
           """Get the current search text."""
           return self.search_field.text()
   ```
2. Create the directory if it doesn't exist:

   ```bash
   mkdir -p flatmate/src/fm/gui/_shared_components/search
   ```

### Step 3: Create the ColumnSelector Component

1. Create the file

   `>> we already have one what is the purpose of this to seperate the logic out ?  these locations are not accurate - one optimisation could be that the drop down menu is optomised in order of commonly used 
   flatmate/src/fm/gui/_shared_components/table/column_selector.py`:

   ```python
   from PySide6.QtCore import Signal
   from PySide6.QtWidgets import QWidget, QHBoxLayout, QToolButton, QMenu, QAction
   from PySide6.QtGui import QIcon

   from fm.core.services.logger import log
   from fm.gui._shared_components.icons.icon_loader import load_icon

   class ColumnSelector(QWidget):
       """Column visibility selector with dropdown menu."""

       columns_changed = Signal(list)

       def __init__(self, parent=None):
           super().__init__(parent)
           self._columns = {}  # {column_name: is_visible}
           self._init_ui()

       def _init_ui(self):
           layout = QHBoxLayout(self)
           layout.setContentsMargins(0, 0, 0, 0)

           self.button = QToolButton()
           self.button.setIcon(load_icon("view_column", size=20))
           self.button.setToolTip("Show/Hide Columns")
           self.button.setPopupMode(QToolButton.InstantPopup)

           self.menu = QMenu()
           self.button.setMenu(self.menu)

           layout.addWidget(self.button)

       def set_columns(self, columns, visible_columns=None):
           """Set available columns and currently visible ones."""
           self._columns = {col: col in (visible_columns or []) for col in columns}
           self._update_menu()

       def get_visible_columns(self):
           """Get list of currently visible columns."""
           return [col for col, is_visible in self._columns.items() if is_visible]

       def _update_menu(self):
           """Update the dropdown menu with current columns."""
           self.menu.clear()

           # Add "Select All" and "Select None" actions
           select_all = self.menu.addAction("Show All Columns")
           select_all.triggered.connect(self._select_all)

           select_none = self.menu.addAction("Hide All Columns")
           select_none.triggered.connect(self._select_none)

           self.menu.addSeparator()

           # Add individual column toggles
           for col_name, is_visible in sorted(self._columns.items()):
               action = self.menu.addAction(col_name)
               action.setCheckable(True)
               action.setChecked(is_visible)
               action.triggered.connect(lambda checked, col=col_name: self._on_column_toggled(col, checked))

       def _select_all(self):
           """Select all columns."""
           for col in self._columns:
               self._columns[col] = True
           self._update_menu()
           self.columns_changed.emit(self.get_visible_columns())

       def _select_none(self):
           """Deselect all columns."""
           for col in self._columns:
               self._columns[col] = False
           self._update_menu()
           self.columns_changed.emit(self.get_visible_columns())

       def _on_column_toggled(self, column, checked):
           """Handle column visibility toggle."""
           self._columns[column] = checked
           self.columns_changed.emit(self.get_visible_columns())
   ```
2. Create the directory if it doesn't exist:

   ```bash
   mkdir -p flatmate/src/fm/gui/_shared_components/table
   ```

### Step 4: Update the Transaction View Panel

1. Modify `flatmate/src/fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py`:
   ```python
   # Add imports at the top
   from fm.gui._shared_components.search.search_bar import SearchBar
   from fm.gui._shared_components.table.column_selector import ColumnSelector
   from PySide6.QtWidgets import QPushButton

   # In the _init_ui method, add toolbar initialization
   def _init_ui(self):
       """Initialize the UI components."""
       # Existing code...

       layout = QVBoxLayout(self)
       layout.setContentsMargins(margin, margin, margin, margin)

       # Add toolbar at the top
       toolbar_widget = QWidget()
       toolbar_widget.setObjectName("toolbar")
       toolbar_layout = self._init_toolbar()
       toolbar_widget.setLayout(toolbar_layout)
       layout.addWidget(toolbar_widget)

       # Create enhanced transaction table with filtering
       logger.debug("Creating EnhancedTableWidget for transactions")
       self.transaction_table = TableView()
       layout.addWidget(self.transaction_table)

       logger.debug("TransactionViewPanel UI setup complete")

   def _init_toolbar(self):
       """Initialize the toolbar with search and column controls."""
       toolbar_layout = QHBoxLayout()
       toolbar_layout.setContentsMargins(8, 8, 8, 8)
       toolbar_layout.setSpacing(8)

       # Search bar
       self.search_bar = SearchBar()
       toolbar_layout.addWidget(self.search_bar)

       # Spacer
       toolbar_layout.addStretch(1)

       # Column selector
       self.column_selector = ColumnSelector()
       toolbar_layout.addWidget(self.column_selector)

       # Apply button
       self.apply_button = QPushButton("Apply")
       self.apply_button.setFixedWidth(80)
       toolbar_layout.addWidget(self.apply_button)

       return toolbar_layout

   # Add signal connections in _connect_signals method
   def _connect_signals(self):
       """Connect signals for toolbar components."""
       # Existing signal connections...

       # Connect search bar signals
       self.search_bar.search_changed.connect(self._on_search_changed)
       self.search_bar.search_cleared.connect(self._on_search_cleared)

       # Connect column selector signals
       self.column_selector.columns_changed.connect(self._on_columns_changed)

       # Connect apply button
       self.apply_button.clicked.connect(self._on_apply_clicked)

   # Add handler methods
   def _on_search_changed(self, search_text):
       """Handle search text changes."""
       if hasattr(self.transaction_table, 'filter_by_text'):
           self.transaction_table.filter_by_text(search_text)

   def _on_search_cleared(self):
       """Handle search cleared."""
       if hasattr(self.transaction_table, 'clear_filters'):
           self.transaction_table.clear_filters()

   def _on_columns_changed(self, visible_columns):
       """Handle column visibility changes."""
       if hasattr(self.transaction_table, 'set_visible_columns'):
           self.transaction_table.set_visible_columns(visible_columns)

       # Save column selections to config
       self._save_column_selections(visible_columns)

   def _on_apply_clicked(self):
       """Handle apply button click."""
       # Apply current filters to the table
       search_text = self.search_bar.get_text()
       if search_text and hasattr(self.transaction_table, 'filter_by_text'):
           self.transaction_table.filter_by_text(search_text)

   def _save_column_selections(self, visible_columns):
       """Save column selections to config."""
       from fm.modules.categorize.config import config

       # Convert display names to database names if needed
       visible_db_names = visible_columns  # Adjust if needed

       # Save to config
       config.set_value('categorize.display.visible_columns', visible_db_names)

       logger.debug(f"Saved column selections: {visible_db_names}")
   ```

### Step 5: Update the Presenter

1. Modify `flatmate/src/fm/modules/categorize/cat_presenter.py`:
   ```python
   # In the _connect_view_signals method, add new connections
   def _connect_view_signals(self):
       """Connect signals from the view to presenter methods."""
       # Existing connections...

       # Connect toolbar signals if available
       if (hasattr(self.view, 'center_panel') and 
           hasattr(self.view.center_panel, 'transaction_view') and
           hasattr(self.view.center_panel.transaction_view, 'apply_button')):

           self.view.center_panel.transaction_view.apply_button.clicked.connect(
               self._handle_apply_filters)

   # Add handler method for apply filters
   def _handle_apply_filters(self):
       """Handle apply filters button click."""
       log("Applying filters from toolbar", level="debug")

       # Get current filters from view
       if (hasattr(self.view, 'center_panel') and 
           hasattr(self.view.center_panel, 'transaction_view') and
           hasattr(self.view.center_panel.transaction_view, 'search_bar')):

           search_text = self.view.center_panel.transaction_view.search_bar.get_text()

           # Apply filters to current data
           # This doesn't trigger a database reload, just filters the current view
           if self._original_df is not None and not self._original_df.empty:
               filtered_df = self._apply_text_filter(self._original_df, search_text)
               self._update_view_with_data(filtered_df)

   def _apply_text_filter(self, df, search_text):
       """Apply text filter to DataFrame."""
       if not search_text:
           return df

       # Convert to lowercase for case-insensitive search
       search_text = search_text.lower()

       # Search in text columns
       mask = False
       for col in df.columns:
           if df[col].dtype == 'object':  # Only search text columns
               # Convert column to string and handle NaN values
               col_values = df[col].astype(str).str.lower()
               mask = mask | col_values.str.contains(search_text, na=False)

       return df[mask]
   ```

### Step 6: Update Configuration

1. Modify `flatmate/src/fm/modules/categorize/config/config.py`:
   ```python
   # In the __init__ method or where defaults are defined
   def ensure_defaults(self):
       """Ensure default configuration values exist."""
       # Existing defaults...

       # Add toolbar-specific defaults
       self.defaults.update({
           'categorize.toolbar.remember_search': False,
           'categorize.toolbar.last_search': '',
           'categorize.toolbar.visible_columns': ['date', 'details', 'amount', 'account', 'tags'],
       })
   ```

### Step 7: Add Styling

1. Create the file `flatmate/src/fm/modules/categorize/_view/styles/toolbar_styles.py`:

   ```python
   """Stylesheet for toolbar components."""

   TOOLBAR_STYLESHEET = """
       QWidget#toolbar {
           background-color: #f5f5f5;
           border-bottom: 1px solid #ddd;
       }

       QLineEdit {
           border: 1px solid #ccc;
           border-radius: 4px;
           padding: 4px 8px;
           background-color: white;
       }

       QToolButton {
           border: none;
           padding: 4px;
       }

       QToolButton:hover {
           background-color: #e0e0e0;
           border-radius: 4px;
       }

       QPushButton {
           background-color: #3B8A45;
           color: white;
           border: none;
           border-radius: 4px;
           padding: 6px 12px;
       }

       QPushButton:hover {
           background-color: #2D6934;
       }
   """
   ```
2. Apply the stylesheet in `transaction_view_panel.py`:

   ```python
   # Add import at the top
   from fm.modules.categorize._view.styles.toolbar_styles import TOOLBAR_STYLESHEET

   # In _init_ui method, after creating toolbar_widget
   toolbar_widget.setStyleSheet(TOOLBAR_STYLESHEET)
   ```

### Step 8: Implement Responsive Behavior

1. Update the `transaction_view_panel.py` to handle resize events:
   ```python
   # Add import
   from PySide6.QtCore import QSize

   # Add method to handle resize events
   def resizeEvent(self, event):
       """Handle resize events for responsive layout."""
       super().resizeEvent(event)

       # Adjust toolbar based on width
       width = event.size().width()

       if width < 500:
           # Compact mode
           self.search_bar.search_field.setPlaceholderText("Search")
           self.apply_button.setText("")
           self.apply_button.setFixedWidth(30)
       else:
           # Normal mode
           self.search_bar.search_field.setPlaceholderText("Search...")
           self.apply_button.setText("Apply")
           self.apply_button.setFixedWidth(80)
   ```

## Testing

### Unit Testing

1. Create test for SearchBar component:

   ```python
   def test_search_bar_signals():
       """Test that SearchBar emits proper signals."""
       search_bar = SearchBar()

       # Test search_changed signal
       signals_received = []
       search_bar.search_changed.connect(lambda text: signals_received.append(text))

       search_bar.set_text("test")
       assert "test" in signals_received

       # Test search_cleared signal
       cleared_signal_received = False
       search_bar.search_cleared.connect(lambda: setattr(cleared_signal_received, "value", True))

       search_bar.clear_button.click()
       assert cleared_signal_received
   ```
2. Create test for ColumnSelector component:

   ```python
   def test_column_selector():
       """Test that ColumnSelector manages columns correctly."""
       selector = ColumnSelector()

       # Test column setting
       columns = ["date", "amount", "description"]
       visible = ["date", "amount"]
       selector.set_columns(columns, visible)

       assert selector.get_visible_columns() == visible

       # Test column toggling
       selector._on_column_toggled("description", True)
       assert "description" in selector.get_visible_columns()
   ```

### Integration Testing

1. Test the toolbar integration with the transaction view:
   ```python
   def test_toolbar_integration():
       """Test that toolbar components interact correctly with the view."""
       panel = TransactionViewPanel()

       # Test search filtering
       panel.search_bar.set_text("test")
       # Verify that filter is applied

       # Test column visibility
       panel.column_selector.set_columns(["date", "amount", "description"])
       panel.column_selector._on_column_toggled("date", False)
       # Verify that column visibility is updated
   ```

## Known Pitfalls and Solutions

### Pitfall 1: Icon Loading Failures

**Problem**: Icons fail to load due to incorrect paths or missing files.

**Solution**:

- Implement fallback to text-only buttons when icons are missing
- Add proper error logging for missing icons
- Use Qt resource system for more reliable icon loading

### Pitfall 2: Performance Issues with Large Datasets

**Problem**: Live filtering becomes slow with large datasets.

**Solution**:

- Implement debouncing for search input (300ms delay)
- Use optimized filtering algorithms
- Consider pagination for very large datasets

### Pitfall 3: Layout Issues on Different Platforms

**Problem**: Toolbar layout looks inconsistent across platforms.

**Solution**:

- Use platform-specific style adjustments
- Test on all target platforms
- Use Qt's layout system properly with appropriate spacing

## Implementation Checkpoints

- [ ] Icon resources are properly set up
- [ ] SearchBar component is created and tested
- [ ] ColumnSelector component is created and tested
- [ ] Transaction view panel is updated with toolbar
- [ ] Presenter is updated to handle toolbar signals
- [ ] Configuration is updated with toolbar settings
- [ ] Styling is applied to toolbar components
- [ ] Responsive behavior is implemented
- [ ] All tests pass

## References

- [PySide6 Documentation](https://doc.qt.io/qtforpython-6/)
- [Google Material Design Icons](https://fonts.google.com/icons)
- [Qt Style Sheets Reference](https://doc.qt.io/qt-6/stylesheet-reference.html)

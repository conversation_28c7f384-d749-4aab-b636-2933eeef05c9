# Update Data View API

This document outlines the proposed API for the Update Data view component. The goal is to establish clear boundaries between the presenter and view components without introducing unnecessary abstraction layers.

## Design Philosophy

Rather than using formal interfaces, we're taking a pragmatic approach by defining a clear set of methods that the view must implement. This creates a "contract" between the presenter and view while keeping the codebase straightforward and maintainable.

## View API Methods

### UI State Methods

- `set_exit_mode()`: Set left panel to exit mode
- `set_process_mode()`: Set left panel to process mode
- `set_save_select_enabled(enabled: bool)`: Enable/disable save select button

### Display Methods

- `display_selected_source(source_info: dict)`: Display the selected source files in the center panel
- `display_master_csv(df: pd.DataFrame)`: Display a DataFrame in the center panel table
- `display_welcome()`: Display welcome message in center panel

### Info Widget Methods

- `set_status(message: str, is_error: bool = False)`: Set status message in the info widget
- `set_progress(current: int, total: int)`: Update progress bar in the info widget
- `set_error(message: str)`: Display error message in the info widget
- `clear_info()`: Clear all info widget displays

### Dialog Methods

- `show_error(message: str, title: str = "Error")`: Show error message box
- `show_success(message: str, title: str = "Success")`: Show success message box
- `get_save_option()`: Get the current save option from the left buttons
- `show_folder_dialog(title: str, initial_dir: Optional[str] = None)`: Show a folder selection dialog
- `show_files_dialog(title: str, initial_dir: Optional[str] = None, filter_str: str = "CSV Files (*.csv)")`: Show a file selection dialog

## Benefits

1. **Clean Separation of Concerns**:
   - Presenter focuses on business logic and coordination
   - View handles all UI concerns (widgets, dialogs, display)

2. **Improved Testability**:
   - The presenter can be tested without needing actual UI widgets
   - You can mock the view for testing

3. **Maintainable Code**:
   - Changes to the UI implementation won't affect the presenter
   - The API between components is clearly defined

## Implementation Notes

- The view should handle all direct widget manipulation
- The presenter should only interact with the view through these defined methods
- All UI-related functionality (dialogs, widget updates) should be in the view
- Event handling should use pub/sub terminology for consistency

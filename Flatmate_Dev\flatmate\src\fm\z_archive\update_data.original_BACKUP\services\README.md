# Update Data Module Services

## Overview
This directory contains the module-specific service wrappers for the Update Data module. These wrappers provide clean interfaces to core services while allowing module-specific customization.

## Structure
```
services/
├── __init__.py         # Exports ud_services
├── local_services.py   # Module service wrapper
└── README.md          # This file
```

## How It Works

### Local Services Wrapper
The `local_services.py` file defines `UpdateDataServices` which wraps core services:

```python
from ....core.services import master_file_service as core_master_service

class UpdateDataServices:
    def __init__(self):
        self._master_service = core_master_service
```

This wrapper:
1. Provides module-specific access to core services
2. Can add module-specific service methods
3. Keeps module code isolated from core implementation details

### Usage
In module code:
```python
from .services import ud_services

# Use a service
if ud_services.update_master_location(file_path):
    # Success handling
    pass
```

### Benefits
1. **Encapsulation**: Module code doesn't need to know about core service details
2. **Flexibility**: Can add module-specific service functionality
3. **Maintainability**: Changes to core services only need to be handled in wrapper
4. **Isolation**: Each module's service needs are separated

## Adding New Services
1. Import needed core service in `local_services.py`
2. Add wrapper methods to `UpdateDataServices`
3. Add any module-specific service functionality

## Available Services
- **Master File Service**: Manages master file locations and history
  - `update_master_location(file_path)`
  - `get_current_master()`
  - `get_master_history()`

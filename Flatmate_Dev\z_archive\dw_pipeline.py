#!/usr/bin/env python3
"""
Pipeline functions for processing bank CSV files.
"""

import datetime

# Standard library imports
import os
import re
import shutil
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

# Local imports
from .file_utils import load_csv_to_df 

# Core imports
from fm.core.services.logger import Logger as log
from fm.core.config.config import config
from fm.core.config.keys import ConfigKeys
from fm.core.data_services.standards.fm_standard_columns import StandardColumns
from fm.core.utils.date_utils import convert_df_dates
from fm.core.data_services import DBIOService
from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository 
# Data service and converter imports for database updates
# from fm.database_service.converters import CSVToTransactionConverter
# from fm.database_service.service import DataService
from fm.core.database.repository.transaction_repository import ImportResult

# Initialize data service
#data_service = DataService()

# Local imports
from ..config.ud_config import ud_config
from ..config.ud_keys import UpdateDataKeys
from ..services.events import UpdateDataEventService
from .processing_tracker import clear_all_trackers, processed_files_tracker
from .file_utils import load_csv_to_df as load_csv_file
from .statement_handlers._handler_registry import get_handler


@dataclass
class UnrecognisedFile:
    filepath: str
    reason: str



def merge_dataframes(
    formatted_dfs: List[pd.DataFrame],
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Merge formatted DataFrames, ensuring consistent column structure, and handle duplicates.
    This function standardizes all incoming DataFrames to a master set of columns
    derived from StandardColumns, ensuring no data is lost during concatenation.
    """
    if not formatted_dfs or all(df.empty for df in formatted_dfs):
        return pd.DataFrame(), {"duplicates_removed": 0, "total_rows": 0}

    merged_df = pd.concat(formatted_dfs, axis=0, ignore_index=True)

    master_column_list = [col.value for col in StandardColumns]
    final_columns = [col for col in master_column_list if col in merged_df.columns]
    merged_df = merged_df[final_columns]

    merged_df.dropna(how='all', inplace=True)

    before_count = len(merged_df)



    # --- Deduplication ---
    potential_keys = [
        StandardColumns.DATE.value,
        StandardColumns.DETAILS.value,
        StandardColumns.AMOUNT.value,
        StandardColumns.BALANCE.value,
        StandardColumns.UNIQUE_ID.value
    ]
    dedup_subset = [key for key in potential_keys if key in merged_df.columns]

    if not dedup_subset:
        log.warning("Could not determine a key for deduplication. No duplicates removed.")
        deduplicated_df = merged_df
    else:
        log.info(f"Deduplicating based on columns: {dedup_subset}")
        deduplicated_df = merged_df.drop_duplicates(subset=dedup_subset, keep='first')
    
    duplicates_removed = before_count - len(deduplicated_df)
    log.info(f"Removed {duplicates_removed} duplicate transactions.")



    stats = {"duplicates_removed": duplicates_removed, "total_rows": len(deduplicated_df)}

    return deduplicated_df, stats


# The process_files function has been removed and its flow logic moved to the director


def validate_final_data(df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    Performs final validation checks on the merged DataFrame.

    Args:
        df: The merged DataFrame to validate.

    Returns:
        A tuple containing a boolean indicating if validation passed,
        and a list of error messages.
    """
    errors = []
    if df.empty:
        return True, []

    # 1. Check for nulls in critical columns
    critical_cols = [
        StandardColumns.DATE, 
        StandardColumns.AMOUNT, 
        StandardColumns.DETAILS
    ]
    for col in critical_cols:
        if df[col.value].isnull().any():
            errors.append(f"Column '{col.value}' contains null values.")

    # 2. Check data types
    if not pd.api.types.is_datetime64_any_dtype(df[StandardColumns.DATE.value]):
        errors.append(f"Column '{StandardColumns.DATE.value}' is not a datetime type.")

    if not pd.api.types.is_numeric_dtype(df[StandardColumns.AMOUNT.value]):
        errors.append(f"Column '{StandardColumns.AMOUNT.value}' is not a numeric type.")

    # 3. Check for suspicious balance changes, ignoring NaN values
    if StandardColumns.BALANCE.value in df.columns and pd.api.types.is_numeric_dtype(df[StandardColumns.BALANCE.value]):
        # Create a view of the DataFrame that only includes rows with a valid balance
        df_with_balance = df.dropna(subset=[StandardColumns.BALANCE.value])
        
        if not df_with_balance.empty:
            # Sort by date to ensure correct row-over-row comparison
            df_sorted = df_with_balance.sort_values(by=StandardColumns.DATE.value).reset_index(drop=True)
            
            # Find rows where Amount is not zero but Balance is the same as the previous row
            suspicious_balance_rows = df_sorted[
                (df_sorted[StandardColumns.AMOUNT.value] != 0) &
                (df_sorted[StandardColumns.BALANCE.value] == df_sorted[StandardColumns.BALANCE.value].shift(1))
            ]
            
            if not suspicious_balance_rows.empty:
                count = len(suspicious_balance_rows)
                log.warning(f"{count} transaction(s) have a non-zero amount but the balance did not change.")

    is_valid = len(errors) == 0
    if not is_valid:
        log.error(f"Final data validation failed: {errors}")
        
    return is_valid, errors




# This function has been moved to a more comprehensive version below


def move_to_unrecognised(filepath: str) -> bool:
    """Move unrecognised file to an 'unrecognised' subfolder in the same directory.

    Args:
        filepath: Path to the file to move

    Returns:
        Boolean indicating success or failure
    """
    try:
        # Create 'unrecognised' subdirectory in the same folder as the file
        file_dir = os.path.dirname(filepath)
        unrecognised_dir = os.path.join(file_dir, 'unrecognised')
        os.makedirs(unrecognised_dir, exist_ok=True)

        filename = os.path.basename(filepath)
        new_path = os.path.join(unrecognised_dir, filename)

        # If file exists, add timestamp
        if os.path.exists(new_path):
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(filename)
            new_path = os.path.join(unrecognised_dir, f"{name}_{timestamp}{ext}")

        shutil.move(filepath, new_path)
        log(f"Moved unrecognised file to: {new_path}", level="info")
        return True
    except Exception as e:
        log(f"Failed to move file to unrecognised folder: {e}", level="warning")
        return False

def back_up_originals(
    filepaths_list: List[str], save_dir: Optional[str] = None
) -> Tuple[bool, Union[str, Dict[str, Any]]]:
    """Backup original files.

    Args:
        filepaths_list: List of file paths to back up
        save_dir: Optional directory to save backups

    Returns:
        Tuple of (success boolean, message)
    """
    if not filepaths_list:
        return False, "No files to backup"

    try:
        # Get backup directory
        if save_dir:
            backup_dir = os.path.join(save_dir, "originals")
        else:
            backup_dir = ud_config.get_path(UpdateDataKeys.Paths.BACKUP)

        # Create backup directory if it doesn't exist
        os.makedirs(backup_dir, exist_ok=True)

        import hashlib

        # Track statistics and file lists
        backed_up = 0
        skipped = 0
        backed_up_files = []
        skipped_files = []

        for filepath in filepaths_list:
            filename = os.path.basename(filepath)
            backup_path = os.path.join(backup_dir, filename)

            # Check if file already exists in backup directory
            if os.path.exists(backup_path):
                # Calculate hash of source file
                with open(filepath, "rb") as f:
                    source_hash = hashlib.md5(f.read()).hexdigest()

                # Calculate hash of existing backup file
                with open(backup_path, "rb") as f:
                    backup_hash = hashlib.md5(f.read()).hexdigest()

                # If files are identical, skip backup
                if source_hash == backup_hash:
                    log(
                        f"Skipping backup of {filename} - identical file already exists",
                        level="debug",
                    )
                    skipped += 1
                    skipped_files.append(filepath)
                    continue
                else:
                    # Files are different but have same name - create a new filename with incremental number
                    file_root, file_ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(backup_path):
                        new_filename = f"{file_root}_{counter}{file_ext}"
                        backup_path = os.path.join(backup_dir, new_filename)
                        counter += 1
                    log(
                        f"Files differ - saving as {os.path.basename(backup_path)}",
                        level="debug",
                    )

            # Copy file to backup location
            with open(filepath, "rb") as src, open(backup_path, "wb") as dst:
                dst.write(src.read())
            backed_up += 1
            backed_up_files.append(filepath)

        # Return structured stats for better reporting
        backup_stats = {
            "message": f"Backed up {backed_up} files to {backup_dir}, skipped {skipped} identical files",
            "backed_up_count": backed_up,
            "skipped_count": skipped,
            "backup_dir": backup_dir,
            "backed_up_files": backed_up_files,
            "skipped_files": skipped_files,
        }
        return True, backup_stats
    except Exception as e:
        return False, f"Backup failed: {str(e)}"


        log(f"Failed to move file to unrecognised folder: {e}", level="warning")
        return False





def save_master_file(df: pd.DataFrame, save_path: str) -> Dict[str, Any]:
    """Save DataFrame to CSV file.

    Args:
        df: DataFrame to save
        save_path: Path to save the file

    Returns:
        Dictionary with save results
    """
    result = {"save_success": False}

    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Find the date column if it exists
        date_column = None
        for col in df.columns:
            if col.lower() == "date":
                date_column = col
                break

        # Create a copy for CSV output
        df_to_save = df.copy()

        # Format dates for CSV output if needed
        if date_column and pd.api.types.is_datetime64_any_dtype(
            df_to_save[date_column]
        ):
            # Format as DD-MM-YY (UK style) for CSV
            df_to_save[date_column] = df_to_save[date_column].dt.strftime("%d-%m-%y")
            log.info("Formatted dates as DD-MM-YY for CSV output")

        # Save to CSV
        df_to_save.to_csv(save_path, index=False)
        log.info(f"Master file saved to: {save_path}")
        result["save_success"] = True

        return result
    except Exception as e:
        log.error(f"Error saving master file: {str(e)}")
        return result


def update_database_from_df(
    df: pd.DataFrame,
    source_file: Optional[str] = None
) -> Optional[ImportResult]:
    """Update the database with transactions from a DataFrame, uses DBIOService.update_database(df) method

    Args:
        df: DataFrame containing transaction data
        source_file: Optional source file path for reference


    Returns:
        ImportResult object or None if update fails
    """
    # a thin wrapper for the DBIOService.update_database(df) method
    try:
        # Check if DataFrame is empty
        if df.empty:
            log.info("Empty DataFrame provided, nothing to update")
            return ImportResult()

        # Make a copy to avoid modifying the original
        df = df.copy()


        # Initialize DBIOService with the repository
        repo = SQLiteTransactionRepository()
        db_service = DBIOService(repo=repo)
        
        # Update database using DBIOService
        import_result = db_service.update_database(df, source_file=source_file)

        if import_result.error_count > 0:
            log.error(
                f"Database update completed with {import_result.error_count} errors: "
                f"{import_result.errors}"
            )
        
        log.info(
            f"Database update complete: {import_result.added_count} added, "
            f"{import_result.duplicate_count} duplicates, "
            f"{import_result.error_count} errors"
        )

        return import_result

    except Exception as e:
        log.error(f"Critical error updating database: {str(e)}")
        return ImportResult(
            added_count=0,
            duplicate_count=0,
            error_count=len(df) if not df.empty else 0,
            errors=[f"Critical error: {str(e)}"]
        )


#----------------------------------------------------------------------------
######### Error Handling #########
# ? many of these are basically dummy classes, purpose?
#-----------------------------------------------------------------------------

class PipelineError(Exception):
    """Base exception for all pipeline-related errors.

    This is the parent class for all custom pipeline errors. It adds:
    1. A details dictionary to store additional error context
    2. Automatic error printing for non-GUI usage
    3. Common error handling patterns for all pipeline errors

    Args:
        message (str): Main error message
        details (dict, optional): Additional error context like file paths or data state
    """

    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details or {}
        # Log the error for non-GUI usage
        log(f"Pipeline Error: {message}", level="error")
        if details:
            log(f"Error details: {details}", level="debug")


class FileLoadError(PipelineError):
    """Raised when there's an error loading or validating CSV files.

    Used in load_csv_file() when:
    1. A CSV file cannot be opened or read
    2. The CSV format doesn't match expected bank format
    3. Required columns are missing
    """


class DataProcessingError(PipelineError):
    """Raised when there's an error processing the data.

    Used in process_with_handler() when:
    1. Data type conversion fails
    2. Required data transformations fail
    3. Data validation checks fail after processing
    """


class FileBackupError(PipelineError):
    """Raised when there's an error backing up original files.

    Used in back_up_originals() when:
    1. Backup directory cannot be created
    2. Files cannot be copied/moved to backup
    3. Source files cannot be cleaned up after backup
    """


class FileSaveError(PipelineError):
    """Raised when there's an error saving the output file.

    Used in save_master_file() when:
    1. Output directory is not writable
    2. Master CSV file cannot be created
    3. User cancels the save operation in file dialog
    """

if __name__ == "__main__":
    pass

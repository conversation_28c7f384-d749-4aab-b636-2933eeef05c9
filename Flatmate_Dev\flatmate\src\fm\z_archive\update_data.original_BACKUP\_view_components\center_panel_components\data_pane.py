"""
Data pane component for showing processed data.
"""

import pandas as pd
from PySide6.QtGui import QStandardItem, QStandardItemModel
from PySide6.QtWidgets import (QFrame, QHBoxLayout, QHeaderView, QLabel,
                               QTableView, QVBoxLayout)

from fm.gui._shared_components.base.base_pane import BasePane


class DataPane(BasePane):
    """Pane component for displaying processed data."""
    
    def __init__(self, parent=None):
        """Initialize the data pane."""
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Header
        self.header_label = QLabel("Processed Data")
        self.header_label.setObjectName("heading")
        layout.addWidget(self.header_label)
        
        # Info section
        info_layout = QHBoxLayout()
        
        # File info
        self.file_info_label = QLabel("File:")
        self.file_info_label.setObjectName("subheading")
        self.file_info = QLabel()
        info_layout.addWidget(self.file_info_label)
        info_layout.addWidget(self.file_info)
        info_layout.addStretch()
        
        # Record count
        self.record_count_label = QLabel("Records:")
        self.record_count_label.setObjectName("subheading")
        self.record_count = QLabel()
        info_layout.addWidget(self.record_count_label)
        info_layout.addWidget(self.record_count)
        
        # Add info layout to main layout
        layout.addLayout(info_layout)
        
        # Separator
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # Table view
        self.table_view = QTableView()
        self.table_view.setAlternatingRowColors(True)
        self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_model = QStandardItemModel()
        self.table_view.setModel(self.table_model)
        
        layout.addWidget(self.table_view, 1)  # Give table view stretch priority
    
    def show_component(self):
        """Show this component."""
        self.show()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
    
    def display_dataframe(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in the table view.
        
        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        if df is None or df.empty:
            self.show_error("No data to display")
            return
        
        # Update file info and record count
        self.file_info.setText(file_info)
        self.record_count.setText(str(len(df)))
        
        # Clear existing model
        self.table_model.clear()
        
        # Set headers - convert pandas Index to list of strings
        self.table_model.setHorizontalHeaderLabels([str(col) for col in df.columns])
        
        # Add data rows
        for row_idx, row in df.iterrows():
            items = [QStandardItem(str(value)) for value in row]
            self.table_model.appendRow(items)
        
        # Resize columns to content
        self.table_view.resizeColumnsToContents()
        
        # Update header
        self.header_label.setText("Processed Data")
    
    def show_error(self, message: str):
        """Show error message.
        
        Args:
            message: Error message to display
        """
        self.header_label.setText(f"Error: {message}")
        self.header_label.setStyleSheet("color: red;")
    
    def show_success(self, message: str):
        """Show success message.
        
        Args:
            message: Success message to display
        """
        self.header_label.setText(message)
        self.header_label.setStyleSheet("color: green;")

# Feature Requirements

## User Story
As a user, I want the column filter tool in the categorise table view to remember my filter terms (even after closing the app), and to support simple AND/exclude logic, so that I can efficiently filter data with minimal effort.
- The search columns dropdown in the toolbar must:
  - Display all available columns as selectable options.
  - Default to the "details" column on first use.
  - Persist the last selected column between sessions (if persistence enabled).


## Acceptance Criteria
- [ ] Filter terms persist across app restarts (unless disabled by config).
- [ ] Persistence is user-configurable (can enable/disable).
- [ ] Multiple space-separated terms are treated as AND (all must match).
- [ ] Terms prefixed with `-` (with spaces before/after) are excluded (rows containing them are filtered out).
- [ ] Operator tokens (AND/exclude) must be surrounded by spaces to be treated as operators; otherwise, they are part of the search term.
- [ ] Quotes or * prefix may be used to force literal search (edge case, rare).
- [ ] UI clearly distinguishes between operators and search terms.
- [ ] Filtering is case-insensitive and matches substrings.
- [ ] No speculative features (OR, advanced operators) in phase 1.

## Success Metrics
- Filter state restored after restart if enabled.
- Filtering logic is intuitive and matches user expectations.
- No accidental interpretation of `-` as a search term.
- Operators (such as `-` for exclude) are only treated as operators if surrounded by spaces. Otherwise, they are part of the search term. For rare cases where a search term starts with `-`, use quotes or a * prefix to force literal matching.

---

## Phase 2 (Planned Enhancement)

### Advanced Search Operators
- [ ] **OR Logic**: Pipe-separated terms (e.g., `coffee|tea` shows transactions with either term)
- [ ] **Bracketing/Grouping**: Parentheses for complex expressions (e.g., `(coffee|tea) -decaf`)
- [ ] **Exact Match**: Quoted terms for literal matching (e.g., `"coffee shop"` matches exact phrase)
- [ ] **Wildcard Support**: Asterisk for pattern matching (e.g., `coff*` matches coffee, coffees, etc.)

### Enhanced User Interface
- [ ] **Search Term Constructor**: Visual query builder widget for non-technical users
- [ ] **Operator Hints**: Dynamic help text that updates based on current input
- [ ] **Syntax Highlighting**: Color-coded terms in the input field (AND=blue, OR=green, EXCLUDE=red)
- [ ] **Auto-completion**: Suggest terms based on actual data content

### Advanced Features
- [ ] **Saved Filter Presets**: Named filters for common searches (e.g., "Dining Out", "Large Expenses")
- [ ] **Filter History**: Quick access dropdown with recent filter patterns
- [ ] **Column-specific Operators**: Date ranges (`date:2024-01`), numeric comparisons (`amount:>100`)
- [ ] **Regex Support**: Regular expression matching for power users

### Acceptance Criteria (Phase 2)
- [ ] OR operator works: `coffee|tea` shows transactions containing either term
- [ ] Grouping works: `(coffee|tea) -decaf` shows coffee or tea but excludes decaf
- [ ] Exact match works: `"gas station"` only matches exact phrase, not "gas" and "station" separately
- [ ] UI constructor allows building complex queries without typing syntax
- [ ] Performance remains acceptable (< 100ms) for complex expressions
- [ ] Backward compatibility maintained with Phase 1 syntax

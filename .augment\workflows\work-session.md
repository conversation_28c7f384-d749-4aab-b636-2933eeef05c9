---
description: "Augment pointer to unified work session workflow"
type: "pointer_workflow"
target: "flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md"
---

# Work Session Workflow (Augment)

**This is a pointer workflow. The complete workflow is located at:**
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`

---

## Quick Start for Augment

### 1. **Access Complete Workflow**
The unified work session workflow is available at:
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`

### 2. **Augment-Specific Advantages**
- **Codebase Retrieval**: Use context engine for architecture analysis
- **Task Management**: Leverage task management for complex sessions
- **Context Preservation**: Excellent context maintenance across sessions
- **Git Integration**: Deep git commit history analysis

---

## Session Type Selection

**Choose your session type and leverage Augment's strengths:**

### **FEATURE Development**
→ **Primary Workflow**: `unified-work-session.md`
→ **Augment Advantage**: Use codebase retrieval for requirements analysis
→ **Context Engine**: Understand existing patterns before building new features

### **REFACTOR Work**
→ **Primary Workflow**: `unified-work-session.md`
→ **Augment Advantage**: Analyze existing architecture with context engine
→ **Pattern Recognition**: Identify similar refactoring patterns in codebase

### **TROUBLESHOOT Issues**
→ **Primary Workflow**: `troubleshoot-enhanced.md`
→ **Augment Advantage**: Use git commit retrieval to find when issues were introduced
→ **Context Analysis**: Understand related code that might be affected

### **MAINTENANCE Tasks**
→ **Primary Workflow**: `unified-work-session.md`
→ **Augment Advantage**: Systematic analysis of maintenance needs across codebase

---

## Augment-Specific Optimizations

### **Session Setup with Context Engine**
```markdown
## Session Setup Enhanced

### Codebase Analysis (Before Starting Work)
1. **Use codebase retrieval** to understand current state
2. **Analyze existing patterns** related to your work
3. **Identify dependencies** and integration points
4. **Review similar implementations** for consistency

### Context Preservation
1. **Document context** thoroughly in SESSION_LOG.md
2. **Reference specific files and line numbers** for future sessions
3. **Capture architectural insights** for knowledge base
4. **Link related components** and their relationships
```

### **During Work Session**
```markdown
## Augment-Enhanced Work Process

### Before Making Changes
- **Query codebase** for similar implementations
- **Understand existing patterns** and conventions
- **Identify all affected components** using context engine
- **Review recent related changes** with git commit retrieval

### During Implementation
- **Maintain context** in SESSION_LOG.md with specific references
- **Document architectural decisions** with full context
- **Reference related code** with file paths and line numbers
- **Track dependencies** and integration points

### Testing and Verification
- **Use context engine** to identify test requirements
- **Find related tests** that might be affected
- **Verify integration points** identified during analysis
- **Document test coverage** and verification steps
```

### **Session Completion with Knowledge Capture**
```markdown
## Enhanced Session Completion

### Comprehensive Documentation
- **Capture architectural insights** gained during session
- **Document patterns discovered** for future reference
- **Record integration points** and dependencies
- **Update knowledge base** with new understanding

### Context for Future Sessions
- **Detailed file references** with line numbers
- **Architectural decision rationale** with full context
- **Integration impact analysis** for future changes
- **Pattern documentation** for consistency
```

---

## Task Management Integration

### **For Complex Sessions**
```markdown
## Task Management with Augment

### Session Planning
1. **Break down complex work** into manageable tasks
2. **Use task management tools** to track progress
3. **Set clear success criteria** for each task
4. **Plan dependencies** and sequencing

### Progress Tracking
1. **Update task status** as work progresses
2. **Document blockers** and resolution approaches
3. **Track time estimates** vs. actual time
4. **Maintain task context** in session logs

### Session Handoffs
1. **Clear task status** for continuation
2. **Context preservation** for task resumption
3. **Dependency tracking** for related work
4. **Progress documentation** for stakeholders
```

---

## Codebase Retrieval Best Practices

### **Architecture Analysis**
```markdown
## Using Codebase Retrieval Effectively

### Before Starting Work
**Query**: "Show me all components similar to [component_name] and their patterns"
**Purpose**: Understand existing approaches and maintain consistency

### During Implementation
**Query**: "Find all files that import or use [module_name]"
**Purpose**: Identify integration points and potential impacts

### For Troubleshooting
**Query**: "Show me recent changes related to [functionality]"
**Purpose**: Identify potential causes of issues

### For Testing
**Query**: "Find all tests related to [component] and similar components"
**Purpose**: Understand testing patterns and requirements
```

---

## Git Commit History Analysis

### **For Understanding Changes**
```markdown
## Git Commit Retrieval Usage

### Understanding Evolution
**Query**: "Show me how [component] has evolved over time"
**Purpose**: Understand design decisions and change patterns

### Troubleshooting
**Query**: "Find commits that modified [functionality] in the last month"
**Purpose**: Identify when issues might have been introduced

### Pattern Recognition
**Query**: "Show me similar refactoring work done previously"
**Purpose**: Learn from past approaches and avoid repeated mistakes
```

---

## Integration with Unified Protocol

### **Session Documentation Enhanced**
```markdown
## Augment-Enhanced Session Log

### Context Analysis Section
**Codebase Queries Made**: [List of retrieval queries and insights]
**Architecture Understanding**: [Key architectural insights gained]
**Pattern Recognition**: [Patterns identified and applied]
**Integration Points**: [Dependencies and connections discovered]

### Decision Documentation
**Architectural Decisions**: [Decisions made with full context]
**Pattern Choices**: [Why specific patterns were chosen]
**Integration Approach**: [How components will work together]
**Future Considerations**: [Implications for future development]
```

---

## Success Criteria for Augment Sessions

### **Enhanced Quality Indicators**
- [ ] **Comprehensive context analysis** completed before starting work
- [ ] **Existing patterns understood** and consistently applied
- [ ] **All integration points identified** and properly handled
- [ ] **Architectural decisions documented** with full rationale
- [ ] **Knowledge captured** for future sessions and team members
- [ ] **Context preserved** for seamless session continuation

---

## Next Steps

1. **Open Complete Workflow**: Navigate to `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`
2. **Leverage Augment Strengths**: Use codebase retrieval and context engine
3. **Enhance Documentation**: Capture architectural insights and context
4. **Maintain Consistency**: Use context engine to understand existing patterns

---

**For the complete workflow details, open:**
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`

**Leverage Augment's unique strengths for superior context understanding and architectural consistency.**

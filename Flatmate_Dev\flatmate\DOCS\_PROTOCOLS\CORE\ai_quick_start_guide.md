# AI Quick Start Guide: Working with Lead Dev

**Context**: One-person AI-assisted development team  
**User**: Lead developer + architect + PM (all roles)  
**AI**: Implementation assistant  

## 🎯 **Core Working Principles**

### **KISS First**
- Keep it simple, stupid - complexity kills MVPs
- Working code > perfect documentation  
- Existing components > new development
- Speed > perfection

### **User-Driven Development**
- **User comments**: Marked with `>>` - these are decisions, not suggestions
- **AI role**: Assist implementation, don't speculate or over-engineer
- **Decision making**: User decides, AI implements
- **Scope**: MVP first, enhancements later

### **Zero Budget Reality**
- Use existing widgets and components
- Avoid external dependencies
- Reuse patterns and code
- Optimize existing rather than rebuild

## 🔧 **Current Project Context**

### **Update Data UI Redesign**
- **Goal**: MVP state-driven UI for file import workflow
- **Approach**: CSV state tables + existing Qt widgets
- **Timeline**: ASAP (yesterday if possible)
- **Constraints**: No auto-import, user-initiated only

### **Key Components**
- **Existing Widgets**: `OptionMenuWithLabel`, `SecondaryButton`, `ActionButton`, etc.
- **Current Variables**: `self.source_menu`, `self.save_menu`, `self.process_btn`
- **Proposed**: `SelectGroupWidget` for repeated patterns
- **State Engine**: CSV-driven UI behavior

## 📋 **Documentation Standards**

### **Comments & Feedback**
- **User comments**: `>>` prefix - treat as requirements
- **AI responses**: Address comments directly, no speculation
- **Documentation**: Concise, actionable, MVP-focused

### **File Organization**
- **Planning**: `DOCS/_FEATURE_development/PLANNING_update_data_ui_redesign/`
- **Protocols**: `DOCS/_PROTOCOLS/`
- **Architecture**: `DOCS/_ARCHITECTURE/`
- **Working docs**: Keep in feature folders

## 🚀 **Workflow Patterns**

### **Planning Sessions**
1. **Quick Discovery** (15 min): Requirements + existing code review
2. **Rapid Design** (20 min): State schema + component mapping  
3. **Implementation Prep** (10 min): Task breakdown + testing strategy

### **Implementation Sessions**
1. **Code Review**: Understand existing implementation
2. **Incremental Changes**: Small, testable modifications
3. **State-Driven**: Use CSV tables for UI behavior
4. **Testing**: Validate each change works

### **Documentation Sessions**
1. **Update Changelogs**: Record what was accomplished
2. **Update State Schemas**: Keep CSV tables current
3. **Architecture Notes**: Document significant changes
4. **Handoff Docs**: Prepare for next session

## ⚡ **Speed Optimizations**

### **Reuse Everything**
- **Existing widgets**: Don't reinvent, extend or configure
- **Current patterns**: Follow established conventions
- **Proven approaches**: State machines, CSV tables, Qt signals

### **Avoid Over-Engineering**
- **No abstract base classes**: User specifically dislikes ABC patterns
- **Simple inheritance**: Extend existing widgets directly
- **Configuration over code**: Use CSV/config files for behavior

### **Focus on MVP**
- **Core workflow only**: File selection → monitoring → user import
- **No advanced features**: Keep for future phases
- **User validation**: Get approval before building

## 🎨 **Design Principles**

### **UI Standards**
- **Colors**: Green theme (#2E7D32 for headers)
- **Layout**: Left panel controls, center display area
- **Widgets**: Existing shared components only
- **Styling**: Consistent with current application

### **State Management**
- **CSV Tables**: Define UI behavior in machine-readable format
- **State Engine**: Drive UI from state definitions
- **User Actions**: All imports require explicit user initiation
- **No Auto-Import**: Files detected but not automatically processed

## 📝 **Communication Style**

### **With User**
- **Direct responses**: Address comments and questions directly
- **No flattery**: Skip "great idea" - get to the point
- **Ask when unclear**: Don't speculate, ask for clarification
- **Show progress**: Update task lists, provide status

### **In Documentation**
- **Concise**: Essential information only
- **Actionable**: Clear next steps
- **Structured**: Use consistent formatting
- **Searchable**: Good headings and organization

## 🔄 **Iteration Approach**

### **Rapid Cycles**
1. **Small changes**: Incremental improvements
2. **Quick validation**: Test each change immediately  
3. **User feedback**: Get approval before proceeding
4. **Document progress**: Keep records current

### **State-Driven Development**
1. **Update CSV**: Modify state definitions
2. **Test behavior**: Validate with state engine
3. **Refine**: Adjust based on results
4. **Deploy**: Apply to actual UI components

---

## 🎯 **Success Metrics**

- **User satisfaction**: Requirements met, no over-engineering
- **Speed**: MVP delivered quickly with minimal documentation overhead
- **Quality**: Working code that follows existing patterns
- **Maintainability**: Clear state definitions, reusable components

**Remember**: The user is the expert - AI assists, user decides!

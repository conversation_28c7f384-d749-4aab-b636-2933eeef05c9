# Database Column Requirements

*Date: 2025-06-27*

This document details how the database service handles columns during import operations, focusing on metadata columns and validation requirements. This information is critical for the column system unification project to ensure all existing functionality is preserved.

## Overview

The database service (`SQLiteTransactionRepository`) is responsible for importing transaction data into the SQLite database. It uses the canonical `StandardColumns` enum from `fm_standard_columns.py` as the source of truth for column names and mappings.

## System Metadata Columns

The following system metadata columns are automatically added during import:

| Column | Description | Added By |
|--------|-------------|----------|
| `import_date` | Timestamp when the import occurred (ISO format) | `add_transactions_from_df` |
| `modified_date` | Initially set to the same as import_date | `add_transactions_from_df` |
| `is_deleted` | Flag for soft deletion (set to 0 for new records) | `add_transactions_from_df` |
| `id` | Primary key (auto-incremented) | Database schema |

Additionally:
- If a `source_file` parameter is provided and the `SOURCE_FILENAME` column doesn't already exist in the DataFrame, it's automatically added.

## Column Mapping

The repository creates a mapping from `StandardColumns` enum values to database column names using the `db_name` property of each enum:

```python
# Create a mapping from StandardColumns to database column names
db_column_mapping = {
    # Map all StandardColumns to their database names
    **{col.value: col.db_name for col in StandardColumns},
    # Add system columns
    "import_date": "import_date",
    "modified_date": "modified_date",
    "is_deleted": "is_deleted"
}
```

This ensures consistent column naming between the application and database.

## Required Columns and Validation

The database service enforces the following validation requirements:

1. **Required Columns**:
   - `DATE` (StandardColumns.DATE)
   - `DETAILS` (StandardColumns.DETAILS)
   - `AMOUNT` (StandardColumns.AMOUNT)
   - `ACCOUNT` (StandardColumns.ACCOUNT)

2. **Non-NULL Validation**:
   - Account numbers cannot be NULL

## Duplicate Detection

The import process uses a sophisticated duplicate detection mechanism that checks:

1. **Basic Transaction Details**:
   - Date
   - Description
   - Amount
   - Account

2. **Additional Identifiers** (if available):
   - `BALANCE` - Used if present for more precise duplicate detection
   - `UNIQUE_ID` - Used as an alternative to BALANCE if BALANCE is not available

The code specifically checks for the presence of these columns and adjusts the uniqueness check accordingly:

```python
# Check if balance or unique_id columns exist in the temp table
has_balance = db_balance_col in import_columns
has_unique_id = db_unique_id_col in import_columns

# Build the uniqueness check based on available columns
uniqueness_check = f"""
    "{db_date_col}" = t."{db_date_col}" 
    AND "{db_desc_col}" = t."{db_desc_col}" 
    AND "{db_amount_col}" = t."{db_amount_col}"
    AND "{db_account_col}" = t."{db_account_col}"
"""

# Add balance check if available
if has_balance:
    uniqueness_check += f""" AND (("{db_balance_col}" IS NULL AND t."{db_balance_col}" IS NULL) OR "{db_balance_col}" = t."{db_balance_col}")"""
# Add unique_id check if available
elif has_unique_id:
    uniqueness_check += f""" AND (("{db_unique_id_col}" IS NULL AND t."{db_unique_id_col}" IS NULL) OR "{db_unique_id_col}" = t."{db_unique_id_col}")"""
```

## Database Schema Creation

The database schema is created using `StandardColumns` as the source of truth:

```python
# Map StandardColumns fields to SQL types using db_name for database column names
for field in StandardColumns:
    field_name = field.db_name  # Use db_name for database column names
    # Determine appropriate SQL type
    if field in [StandardColumns.DATE, StandardColumns.DETAILS, StandardColumns.UNIQUE_ID, 
               StandardColumns.SOURCE_FILENAME, StandardColumns.ACCOUNT]:
        columns.append(f'"{field_name}" TEXT')
    elif field in [StandardColumns.AMOUNT, StandardColumns.BALANCE,
                 StandardColumns.CREDIT_AMOUNT, StandardColumns.DEBIT_AMOUNT]:
        columns.append(f'"{field_name}" REAL')
    else:
        columns.append(f'"{field_name}" TEXT')
```

## Implications for Column System Unification

For the unified column system, we must ensure:

1. The new system preserves the `db_name` property for database column mapping
2. All system metadata columns (import_date, modified_date, is_deleted) are properly accounted for
3. The validation logic for required columns is maintained
4. The duplicate detection logic using BALANCE or UNIQUE_ID is preserved
5. The database schema creation process is updated to use the new unified column system

## Migration Considerations

When migrating to the unified column system:

1. **Database Schema Compatibility**:
   - The `db_name` values in the new system must match existing database column names to maintain compatibility with existing data
   - Any changes to column names would require database migration scripts

2. **System Column Handling**:
   - The unified system should clearly distinguish between business columns and system columns
   - System columns (import_date, modified_date, is_deleted) should be handled consistently

3. **Validation Rules**:
   - The validation logic for required columns must be preserved
   - The special handling of BALANCE and UNIQUE_ID for duplicate detection must be maintained

4. **Type Mapping**:
   - The mapping of column types to SQL types must be preserved or enhanced
   - Any changes to type mappings would require database migration scripts

component_id,widget_type,base_class,state_initial,text_initial,trigger,response,text_folder_selected,current_variable,notes,priority
title,Label,HeadingLabel,visible,Update Data,,,Update Data,self.title,Panel title,HIGH
source_menu,OptionMenu,OptionMenuWithLabel,active,1. Source Files,user_selection,update_display,folder_name,self.source_menu,Source selection dropdown,HIGH
source_select_btn,Button,SecondaryButton,active,Select...,click,open_folder_dialog,Select...,self.source_select_btn,Source selection trigger,HIGH
save_menu,OptionMenu,OptionMenuWithLabel,hidden,2. Save Location,source_folder_set,show_dropdown,Same as source,self.save_menu,Save location dropdown,MEDIUM
save_select_btn,Button,SecondaryButton,hidden,Select...,click,open_folder_dialog,Select...,self.save_select_btn,Save location trigger,LOW
process_label,Label,SubheadingLabel,visible,3. Process,,,3. Process,self.process_label,Process section header,HIGH
db_update_checkbox,Checkbox,LabeledCheckBox,checked,Update Database,user_toggle,toggle_state,Update Database,self.db_update_checkbox,Database update option,HIGH
process_btn,Button,ActionButton,disabled,Process Files,files_detected,enable_button,Process Files,self.process_btn,Main action button,HIGH
cancel_btn,Button,ExitButton,visible,Exit,click,exit_module,Exit,self.cancel_btn,Cancel/exit button,HIGH
file_display_widget,Widget,FileDisplayWidget,empty,No files detected,files_detected,show_file_list,Files ready for import,EXISTING,Optimize existing widget,HIGH
monitoring_status,Label,InfoLabel,hidden,Make default and monitor,folder_selected,show_status,Monitoring: folder_name,PROPOSED,Configuration/status display,MEDIUM
,,,,,,,,,,
# STATE TRANSITIONS,,,,,,,,,,
INITIAL_TO_FOLDER_SET,,,,,folder_selected,enable_save_group + show_monitoring,,,State transition,
FOLDER_SET_TO_MONITORING,,,,,monitoring_active,start_file_watcher,,,State transition,
MONITORING_TO_FILES_READY,,,,,files_detected,enable_process_button + show_file_list,,,State transition,
FILES_READY_TO_PROCESSING,,,,,process_clicked,disable_ui + show_progress,,,State transition,
PROCESSING_TO_COMPLETE,,,,,process_success,show_summary + move_files,,,State transition,
COMPLETE_TO_MONITORING,,,,,user_acknowledge,reset_ui + continue_monitoring,,,State transition,
,,,,,,,,,,
# VALIDATION RULES,,,,,,,,,,
folder_exists,,,,,folder_path,validate_folder_access,,,Validation check,
files_valid,,,,,file_list,validate_file_types,,,Validation check,
process_ready,,,,,ui_state,check_all_requirements,,,Validation check,
,,,,,,,,,,
# PROPOSED WIDGETS,,,,,,,,,,
source_group,Group,SelectGroupWidget,active,1. Source Files,user_interaction,handle_source_selection,folder_name,PROPOSED,Combines source_menu + source_select_btn,HIGH
save_group,Group,SelectGroupWidget,hidden,2. Save Location,source_folder_set,show_save_options,Same as source,PROPOSED,Combines save_menu + save_select_btn,MEDIUM
,,,,,,,,,,
# MVP CONSTRAINTS,,,,,,,,,,
NO_AUTO_IMPORT,,,,,,,,,,Files detected but not automatically imported,
USER_INITIATED,,,,,,,,,,All imports require explicit user action,
FOLDER_MONITORING,,,,,,,,,,App monitors folder and adds files to queue,
EXISTING_WIDGETS,,,,,,,,,,Use existing file display widget,
KISS_PRINCIPLE,,,,,,,,,,Keep implementation simple and focused,

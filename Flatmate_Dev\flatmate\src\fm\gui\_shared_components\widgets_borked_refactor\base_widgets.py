"""
DEPRECATED: Base UI Components

⚠️  THIS MODULE IS DEPRECATED ⚠️

This module has been reorganized into category-specific modules for better organization:

- Buttons: fm.gui._shared_components.widgets.buttons
- Option Menus: fm.gui._shared_components.widgets.option_menus
- Checkboxes: fm.gui._shared_components.widgets.checkboxes

This file now serves as a compatibility shim. Please update your imports to use
the new category-specific modules.

MIGRATION GUIDE:
Old: from fm.gui._shared_components.widgets.base_widgets import ActionButton
New: from fm.gui._shared_components.widgets.buttons import ActionButton

Or use the convenience imports:
from fm.gui._shared_components.widgets import ActionButton
"""

import warnings
from fm.core.services.logger import log

# Initialize logger for deprecation warnings

def _log_deprecation_warning(class_name, new_module):
    """Log a deprecation warning for the given class."""
    message = (
        f"DEPRECATED: Importing {class_name} from base_widgets is deprecated. "
        f"Please use: from fm.gui._shared_components.widgets.{new_module} import {class_name} "
        f"or from fm.gui._shared_components.widgets import {class_name}"
    )
    log.warning(message)
    # Also emit a Python warning for IDE/tooling support
    warnings.warn(message, DeprecationWarning, stacklevel=3)


# Import from new locations with deprecation warnings
def _create_deprecated_import(class_name, module_name, new_module_name):
    """Create a deprecated import that logs a warning when accessed."""
    def __getattr__(name):
        if name == class_name:
            _log_deprecation_warning(class_name, new_module_name)
            module = __import__(f'fm.gui._shared_components.widgets.{new_module_name}', fromlist=[class_name])
            return getattr(module, class_name)
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
    return __getattr__


# Button imports with deprecation warnings
class ActionButton:
    def __new__(cls, *args, **kwargs):
        _log_deprecation_warning('ActionButton', 'buttons')
        from .buttons import ActionButton as _ActionButton
        return _ActionButton(*args, **kwargs)

class SecondaryButton:
    def __new__(cls, *args, **kwargs):
        _log_deprecation_warning('SecondaryButton', 'buttons')
        from .buttons import SecondaryButton as _SecondaryButton
        return _SecondaryButton(*args, **kwargs)

class ExitButton:
    def __new__(cls, *args, **kwargs):
        _log_deprecation_warning('ExitButton', 'buttons')
        from .buttons import ExitButton as _ExitButton
        return _ExitButton(*args, **kwargs)


# Option menu imports with deprecation warnings
class OptionMenuWithLabel:
    def __new__(cls, *args, **kwargs):
        _log_deprecation_warning('OptionMenuWithLabel', 'option_menus')
        from .option_menus import OptionMenuWithLabel as _OptionMenuWithLabel
        return _OptionMenuWithLabel(*args, **kwargs)

class OptionMenuWithLabelAndButton:
    def __new__(cls, *args, **kwargs):
        _log_deprecation_warning('OptionMenuWithLabelAndButton', 'option_menus')
        from .option_menus import OptionMenuWithLabelAndButton as _OptionMenuWithLabelAndButton
        return _OptionMenuWithLabelAndButton(*args, **kwargs)


# Checkbox imports with deprecation warnings
class LabeledCheckBox:
    def __new__(cls, *args, **kwargs):
        _log_deprecation_warning('LabeledCheckBox', 'checkboxes')
        from .checkboxes import LabeledCheckBox as _LabeledCheckBox
        return _LabeledCheckBox(*args, **kwargs)


# Maintain module-level exports for compatibility
__all__ = [
    'LabeledCheckBox',
    'ActionButton',
    'SecondaryButton',
    'ExitButton',
    'OptionMenuWithLabel',
    'OptionMenuWithLabelAndButton'
]

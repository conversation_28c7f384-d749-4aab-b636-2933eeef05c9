# Table Search and Filtering

## Overview
Comprehensive search and filtering functionality for table views, providing both simple filtering for basic users and advanced search capabilities for power users.

## Current Status

### ✅ Phase 1: Basic Filtering (IMPLEMENTED)
- **Filter Persistence** - Settings automatically saved between sessions
- **AND Logic** - Space-separated terms (e.g., `coffee shop`)
- **EXCLUDE Logic** - Dash-prefixed terms (e.g., `-refund`)
- **Column Selection** - Filter specific columns or all columns
- **Live Filtering** - Results update as you type
- **Performance Optimized** - Fast search across large datasets

### 🚧 Phase 2: Enhanced Search (IN PROGRESS)
- **OR Logic** - Pipe-separated terms (e.g., `coffee|tea`) - 90% complete
- **Basic Grouping** - Simple parentheses support - Planned
- **Improved UI** - Better hints and examples - Planned

### 📋 Phase 3: Advanced Search (PLANNED)
- **Full Boolean Operators** - AND, OR, NOT keywords
- **Quoted Phrases** - Exact matching with quotes
- **Wildcard Support** - Pattern matching with asterisks
- **Search Constructor UI** - Visual query builder

### 🔮 Phase 4: Power Features (FUTURE)
- **Regular Expressions** - Advanced pattern matching
- **Column-specific Operators** - Date ranges, numeric comparisons
- **Saved Presets** - Named search templates
- **Search History** - Quick access to recent searches

## Quick Start

### Basic Usage
```
coffee shop          # Find transactions with both "coffee" AND "shop"
-refund             # Exclude transactions containing "refund"
coffee -starbucks   # Coffee transactions except Starbucks
```

### Enhanced Usage (Phase 2)
```
coffee|tea          # Find transactions with coffee OR tea
coffee|tea -decaf   # Coffee or tea, but not decaf
```

## Documentation Structure

- **[requirements.md](_requirements_prd.md)** - Feature requirements for all phases
- **[design.md](design.md)** - Technical architecture and implementation details
- **[implementation_status.md](tasks.md)** - Detailed status tracking
- **[user_guide.md](user_guide.md)** - Complete user documentation
- **[syntax_reference.md](review_discussion/syntax_reference.md)** - Search syntax guide
- **[implementation_guide.md](implementation_guide.md)** - Developer implementation guide

## Key Features

### User Experience
- **Intuitive Syntax** - Natural language-like search patterns
- **Progressive Disclosure** - Simple features first, advanced features discoverable
- **Immediate Feedback** - Live filtering with instant results
- **Persistent State** - Remembers your preferences

### Technical Excellence
- **High Performance** - Optimized for large datasets
- **Extensible Architecture** - Easy to add new operators and features
- **Robust Error Handling** - Graceful degradation for invalid syntax
- **Comprehensive Testing** - Full test coverage for all functionality

## Implementation Approach

### Current: Custom Parser
- Lightweight and fast
- Full control over behavior
- Optimized for our specific needs
- Easy to debug and extend

### Future: Hybrid Approach
- Keep custom parser for simple cases
- Consider external libraries for complex boolean logic
- Maintain backward compatibility
- Provide migration path for advanced features

## Getting Started

### For Users
See [user_guide.md](user_guide.md) for complete usage instructions and examples.

### For Developers
See [implementation_guide.md](implementation_guide.md) for technical details and extension points.

### For Product Managers
See [requirements.md](_requirements_prd.md) for feature specifications and acceptance criteria.

---

**This feature provides a foundation for powerful, intuitive search capabilities that scale from simple filtering to advanced query construction.**

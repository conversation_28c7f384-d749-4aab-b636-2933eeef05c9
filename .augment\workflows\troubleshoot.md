---
description: "Augment pointer to enhanced troubleshooting workflow"
type: "pointer_workflow"
target: "flatmate/DOCS/_PROTOCOLS/WORKFLOWS/troubleshoot-enhanced.md"
---

# Enhanced Troubleshooting Workflow (Augment)

**This is a pointer workflow. The complete enhanced troubleshooting workflow is located at:**
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/troubleshoot-enhanced.md`

---

## Quick Start for Augment

### 1. **Access Complete Workflow**
The enhanced troubleshooting workflow is available at:
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/troubleshoot-enhanced.md`

### 2. **Augment-Specific Advantages for Troubleshooting**
- **Codebase Retrieval**: Find related code that might be causing issues
- **Git Commit History**: Identify when problems were introduced
- **Context Engine**: Understand complex system interactions
- **Pattern Recognition**: Find similar issues resolved previously

---

## Augment-Enhanced Troubleshooting Process

### **Step 1: Problem Clarification with Context**
```markdown
## Enhanced Problem Analysis

### Codebase Context Queries
**Query**: "Show me all code related to [failing_functionality]"
**Purpose**: Understand the complete scope of potentially affected code

**Query**: "Find similar error patterns in the codebase"
**Purpose**: Identify if this is a recurring issue with known solutions

### System Understanding
- **Use context engine** to map all components involved
- **Identify integration points** that might be failing
- **Understand data flow** through the problematic area
- **Map dependencies** that could be causing issues
```

### **Step 2: Evidence Gathering with Deep Analysis**
```markdown
## Augment-Enhanced Evidence Collection

### Git Commit Analysis
**Query**: "Show me all commits that modified [failing_component] in the last [timeframe]"
**Purpose**: Identify when the issue might have been introduced

**Query**: "Find commits that touched files related to [error_message]"
**Purpose**: Understand recent changes that might have caused the problem

### Codebase Pattern Analysis
**Query**: "Show me how [functionality] is implemented across the codebase"
**Purpose**: Understand if the issue is isolated or systemic

**Query**: "Find all error handling patterns for similar functionality"
**Purpose**: Understand expected error handling approaches
```

### **Step 3: Issue Isolation with Context Engine**
```markdown
## Context-Driven Issue Isolation

### Component Relationship Analysis
**Query**: "Show me all components that interact with [failing_component]"
**Purpose**: Identify which components might be causing or affected by the issue

### Data Flow Analysis
**Query**: "Trace data flow through [problematic_process]"
**Purpose**: Understand where in the process the failure occurs

### Dependency Analysis
**Query**: "Show me all dependencies of [failing_functionality]"
**Purpose**: Identify external factors that might be causing the issue
```

### **Step 4: Hypothesis Generation with Historical Context**
```markdown
## Context-Informed Hypothesis Development

### Historical Issue Analysis
**Query**: "Find similar issues that were resolved previously"
**Purpose**: Learn from past solutions and avoid repeating failed approaches

### Pattern-Based Hypotheses
**Query**: "Show me common failure patterns for [component_type]"
**Purpose**: Generate hypotheses based on known failure modes

### Change Impact Analysis
**Query**: "Analyze the impact of recent changes on [failing_functionality]"
**Purpose**: Focus hypotheses on recent modifications
```

---

## Augment-Specific Troubleshooting Tools

### **Codebase Retrieval for Root Cause Analysis**
```markdown
## Advanced Root Cause Analysis

### Code Pattern Analysis
- **Query similar implementations** to understand expected behavior
- **Identify deviations** from established patterns
- **Find configuration differences** that might cause issues
- **Analyze error handling consistency** across similar components

### Integration Point Analysis
- **Map all integration points** for the failing functionality
- **Identify potential failure points** in integrations
- **Understand data contracts** between components
- **Verify integration assumptions** are still valid
```

### **Git Commit History for Timeline Analysis**
```markdown
## Timeline-Based Troubleshooting

### Change Timeline Construction
1. **Identify when issue started** occurring
2. **Map all changes** in that timeframe
3. **Prioritize changes** by likelihood of causing issue
4. **Understand change context** and rationale

### Regression Analysis
1. **Find the specific commit** that introduced the issue
2. **Understand the change intent** vs. actual impact
3. **Identify why the change** caused unexpected behavior
4. **Plan fix approach** based on change analysis
```

---

## Enhanced Documentation with Context

### **Context-Rich Problem Documentation**
```markdown
## Augment-Enhanced Problem Documentation

### System Context
**Components Involved**: [List with file paths and relationships]
**Data Flow**: [Complete data flow through problematic area]
**Integration Points**: [All external dependencies and interactions]
**Recent Changes**: [Relevant commits with context and rationale]

### Analysis Context
**Similar Issues**: [Previous similar issues and their resolutions]
**Pattern Deviations**: [How current implementation differs from patterns]
**Dependency Status**: [Status of all dependencies and external factors]
**Configuration State**: [Current configuration vs. expected configuration]
```

### **Solution Documentation with Architectural Context**
```markdown
## Context-Aware Solution Documentation

### Root Cause Analysis
**Underlying Cause**: [Deep analysis of why the issue occurred]
**Contributing Factors**: [All factors that led to the issue]
**System Impact**: [How the issue affects the broader system]
**Prevention Strategy**: [How to prevent similar issues]

### Solution Context
**Fix Rationale**: [Why this solution addresses the root cause]
**Alternative Approaches**: [Other solutions considered and why rejected]
**Integration Impact**: [How the fix affects other components]
**Future Considerations**: [Implications for future development]
```

---

## Integration with Enhanced Workflow

### **Session Setup with Context Analysis**
```markdown
## Augment-Enhanced Session Setup

### Pre-Troubleshooting Analysis
1. **Query codebase** for complete understanding of problematic area
2. **Analyze recent changes** that might have introduced the issue
3. **Map system context** and all related components
4. **Review similar issues** and their resolutions

### Context Documentation
- **Document system understanding** gained from analysis
- **Record all relevant components** and their relationships
- **Capture recent change context** and potential impacts
- **Note similar issue patterns** and resolution approaches
```

---

## Success Criteria for Augment Troubleshooting

### **Enhanced Quality Indicators**
- [ ] **Complete system context** understood before making changes
- [ ] **Root cause identified** through systematic analysis
- [ ] **Historical context** considered in solution approach
- [ ] **Integration impacts** fully analyzed and addressed
- [ ] **Pattern consistency** maintained in solution
- [ ] **Knowledge captured** for preventing similar issues

### **Context Preservation**
- [ ] **All analysis queries** documented for future reference
- [ ] **System understanding** captured in session logs
- [ ] **Solution rationale** documented with full context
- [ ] **Prevention measures** identified and documented

---

## Next Steps

1. **Open Enhanced Workflow**: Navigate to `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/troubleshoot-enhanced.md`
2. **Leverage Context Engine**: Use codebase retrieval for deep system understanding
3. **Analyze Historical Context**: Use git commit history for timeline analysis
4. **Document Comprehensively**: Capture all context and analysis for future reference

---

**For the complete troubleshooting workflow, open:**
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/troubleshoot-enhanced.md`

**Leverage Augment's context engine and git history analysis for superior troubleshooting effectiveness.**

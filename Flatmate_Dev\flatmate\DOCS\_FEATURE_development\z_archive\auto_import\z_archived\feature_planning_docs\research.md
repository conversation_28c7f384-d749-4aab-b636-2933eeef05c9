# Research: Auto-Import Implementation

## 1. File System Monitoring: Polling vs. System Events

**Objective**: Determine the best method for detecting new files in the import folder.

- **Polling**: Involves periodically checking the directory for changes. It's simple to implement but can be inefficient, causing unnecessary disk I/O and CPU usage, and introducing latency between file creation and detection.

- **System Events (Triggers)**: Uses the operating system's native file system notifications (e.g., `inotify` on Linux, `FSEvents` on macOS, `ReadDirectoryChangesW` on Windows). This is highly efficient as the application is notified instantly by the OS when a change occurs, consuming minimal resources while idle.

**Conclusion**: A system-event-based approach is strongly preferred for performance and efficiency.

### Recommended Python Package: `watchdog`

- **Description**: `watchdog` is the de-facto standard Python library for monitoring file system events. It provides a unified, cross-platform API that abstracts away the underlying OS-specific implementations.
- **Key Advantages**:
  - **Cross-Platform**: Works on Windows, macOS, and Linux.
  - **Efficient**: Uses native OS APIs where available for optimal performance.
  - **Fallback Mechanism**: Automatically falls back to polling if native APIs are not available, making it robust.
  - **Easy to Use**: Offers a clean API with event handlers to react to file creation, modification, deletion, etc.

**Implementation Note**: The core logic would involve setting up a `watchdog` observer to monitor the import directory and triggering the `dw_director` processing pipeline when a `FileCreatedEvent` is detected for a `.csv` file.

---

## 2. OFX File Parsing

**Objective**: Find a suitable Python library for parsing OFX (Open Financial Exchange) files, the standard format for bank statements.

**Conclusion**: `ofxtools` is the most robust and well-maintained library for this purpose.

### Recommended Python Package: `ofxtools`

- **Description**: A comprehensive library for parsing, validating, and creating OFX files. It handles the complexities of the OFX specification (both versions 1 and 2).
- **Key Advantages**:
  - **Standard Compliant**: Adheres to the official OFX specification.
  - **Rich Data Extraction**: Parses accounts, transactions (including type, date, amount, memo), balances, and statement periods.
  - **Data Structure**: Converts OFX data into intuitive Python objects, making it easy to work with.

**Implementation Note**: A new statement handler, inheriting from `_base_statement_handler`, would be created. Its `process_file` method would use `ofxtools` to parse the file and then map the extracted data to the application's standard transaction format.

---

## 3. PDF Table Extraction

**Objective**: Identify libraries for extracting tabular transaction data from PDF bank statements.

**Challenge**: Unlike OFX, PDF is a presentation format. There is no standard for embedding financial data, so layouts vary significantly between banks and even between statement versions from the same bank. Extraction is inherently fragile and requires custom logic for each statement type.

**Conclusion**: `pdfplumber` is the best starting point due to its flexibility, with `Camelot` as a simpler alternative for well-structured tables.

### Recommended Python Packages

1.  **`pdfplumber` (Primary Recommendation)**
    *   **Description**: Built on `pdfminer.six`, it's designed to extract detailed information about text, rectangles, and lines, making it ideal for programmatically identifying table boundaries and data even in complex layouts.
    *   **Key Advantages**:
        *   **Granular Control**: Provides access to the exact position of characters, lines, and rectangles, which is crucial for deducing table structures.
        *   **Handles Complex Layouts**: Can be used to write robust parsers for statements with inconsistent formatting.
    *   **Downside**: Requires more development effort per statement type.

2.  **`Camelot` (Secondary Recommendation)**
    *   **Description**: A library specifically dedicated to extracting tables from PDFs. It offers two methods: "Lattice" (for tables with clear grid lines) and "Stream" (for tables defined by whitespace).
    *   **Key Advantages**:
        *   **Simpler API**: Easier to use for straightforward tables.
        *   **Visual Debugging**: Can generate plots showing the detected table structure.
    *   **Downside**: Less flexible than `pdfplumber` when dealing with poorly structured or complex statements.

**Implementation Note**: Implementing PDF parsing will require a dedicated `PDFStatementHandler` base class and individual, bank-specific handlers (e.g., `KiwibankPDFStatementHandler`). Each handler will contain custom logic using `pdfplumber` or `Camelot` to locate and extract the transaction table based on the visual layout of that specific statement. This is a significant undertaking compared to OFX parsing.

---
*Updated: 2025-07-20*
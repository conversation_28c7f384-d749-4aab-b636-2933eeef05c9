"""Local services for Update Data module."""
# ! I HAVE NO F'ING IDEA WHAT THIS IS DOING....
# TODO: evaluate utility of this service wrapper

from ....core.services import master_file_service as core_master_service
from ....core.services.logger import log


class MasterFileService:
    def __init__(self):
        self._master_service = core_master_service
        log.debug("MasterFileService initialized")
    """Service wrapper for Update Data module.

    Provides module-specific access to core services with any
    additional module-specific functionality.
    """

    def __init__(self):
        self._master_service = core_master_service
        log.debug("UpdateDataServices initialized")

    
    def update_master_location(self, file_path):
        """Update master file location with module-specific validation."""
        if self._master_service.validate_master(file_path):
            self._master_service.update_location(file_path)
            return True
        return False

    def get_current_master(self):
        """Get current master file location."""
        return self._master_service.get_current_master()

    def get_master_history(self):
        """Get master file history."""
        return self._master_service.get_history()


# `Global` singelton instance #?
ud_services = MasterFileService()

# Report: Data Flow Analysis (`dw_director` & `dw_pipeline`)

## 1. Detailed Logical Flow

### 1.1 Initialization Phase (`dw_director.dw_director()`)
1. **Input Validation**
   - Accepts `job_sheet` dictionary containing:
     - `filepaths`: List of file paths to process
     - `save_folder`: Directory for output files
     - `update_database`: Boolean flag for database updates
   - Initializes processing trackers via `clear_all_trackers()`
   - Publishes processing start event via `UpdateDataEventService`

### 1.2 File Processing Phase (`_load_and_process_files()`)
2. **File Iteration**
   - For each file in `filepaths`:
     - Extracts filename using `os.path.basename()`
     - Logs processing start for the file
     - The filename is used for logging and tracking purposes, and is also stored as an attribute on the DataFrame for reference

3. **Handler Selection**
   - Calls `get_handler(filepath)` to find appropriate handler from the handler registry
   - The handler registry is imported from `.statement_handlers._handler_registry`
   - If no handler found:
     - Logs warning
     - Adds to `unrecognized_files` list
     - Updates `unrecognised_files_tracker`
     - Skips to next file

4. **File Processing**
   - Calls `handler.process_file(filepath)`
   - Handles three possible outcomes:
     - **Success**:
       - Adds processed DataFrame to `formatted_dfs`
       - Adds filepath to `processed_files`
       - Updates `processed_files_tracker`
     - **Empty Result**:
       - Logs warning
       - Adds to `unrecognized_files`
       - Updates `unrecognised_files_tracker`
     - **Error**:
       - Logs error with handler details
       - Adds to `unrecognized_files`
       - Updates `unrecognised_files_tracker`

### 1.3 Data Aggregation Phase (`merge_dataframes()` in `dw_pipeline`)
5. **Column Standardization**
   - Defines master column list from `StandardColumns` enum
   - For each DataFrame in `formatted_dfs`:
     - Creates a copy
     - Reindexes to match master column list
     - Appends to `standardized_dfs`

6. **DataFrame Concatenation**
   - If no valid DataFrames: 
     - Returns empty DataFrame with zero stats
     - Logs warning: "No valid DataFrames to concatenate"
   - Otherwise: Concatenates all DataFrames using `pd.concat()`
   - Drops completely empty rows with `dropna(how='all')`

7. **Deduplication Logic**
   - Records initial row count
   - Determines deduplication strategy:
     - **Primary**: Uses `UNIQUE_ID` column if present and non-null
     - **Required Fallback**: When `UNIQUE_ID` is not available, uses composite key of `[DATE, DETAILS, AMOUNT, BALANCE]`
       - `BALANCE` is REQUIRED in the composite key when `UNIQUE_ID` is not present
       - This is critical because two transactions might be identical in all other fields but have different balances, indicating they are unique transactions
   - Performs deduplication with `drop_duplicates()`
   - Validates that either `UNIQUE_ID` or all required composite key columns exist
   - Calculates and returns deduplication statistics
   - Logs any issues or warnings during deduplication

### 1.4 Validation Phase (`validate_core_data_structure()` in `dw_pipeline`)
8. **Core Validation**
   - Validates required columns exist:
     - `DATE`: Must be parseable to datetime
     - `DETAILS`: Must be non-empty string
     - `AMOUNT`: Must be numeric
   - Requires either `BALANCE` or `UNIQUE_ID` to be present
   - Performs type conversion and standardization

### 1.5 File Operations Phase
9. **Backup Handling**
   - Calls `_backup_and_cleanup_originals()`
   - Creates timestamped backup directory
   - Copies processed files to backup location
   - Removes original files after successful backup

10. **Master File Creation**
    - Generates timestamped filename
    - Saves merged DataFrame to CSV
    - Handles file system errors and permissions

### 1.6 Database Update Phase (`update_database_from_df()` in `dw_pipeline`)
11. **Database Operations**
    - If `update_database` flag is True:
      - Initializes `DBIOService`
      - Calls `update_database(validated_df)`
      - Handles transaction commit/rollback
      - Returns operation statistics

## 2. Critical Observations

### 2.1 Deduplication Logic Analysis
- **Current Implementation**:
  - Uses `BALANCE` in composite key when available
    - This is correct as two transactions might be identical except for their balance, indicating they are unique transactions
  - Handles partial `UNIQUE_ID` population
    - This is correct as some transactions may not have a unique ID but are still unique based on other attributes
  - Uses composite key of `[DATE, DETAILS, AMOUNT]` as fallback

- **Potential Issues**:
  - Inconsistent formatting in composite key columns across different handlers could lead to missed duplicates
    - For example, one handler might format dates as 'YYYY-MM-DD' while another uses 'DD/MM/YYYY'
    - Similarly, amount formatting might differ (e.g., '1,000.00' vs '1000.00')
### 2.2 Error Handling Gaps
- **Missing Validation**: No verification of deduplication results
- **Silent Failures**: Some error conditions only log warnings
- **Incomplete Rollback**: Failed database updates may leave partial changes

### 2.3 Performance Considerations
- **Memory Usage**: Entire dataset loaded into memory
- **Inefficient Processing**: Multiple DataFrame copies created
- **No Chunking**: Large files may cause memory issues
  - Note: Currently of low concern as we're dealing with simple CSV files, but efficiency improvements are always beneficial for future scalability

## 3. Recommendations

### 3.1 Immediate Fixes
1. **Improve Deduplication**
   ```python
   # Current Implementation (Correct)
   dedup_subset = [DATE, DETAILS, AMOUNT]
   if BALANCE in df.columns:
       dedup_subset.append(BALANCE)  # This is correct as balance helps identify unique transactions
   
   # Additional Recommendation: Standardize formats
   def standardize_formats(df):
       # Ensure consistent formatting across all handlers
       if DATE in df.columns:
           df[DATE] = pd.to_datetime(df[DATE]).dt.strftime('%Y-%m-%d')
       if AMOUNT in df.columns:
           df[AMOUNT] = df[AMOUNT].astype(str).str.replace('[^\d.-]', '').astype(float)
       return df
   ```

2. **Add Validation**
   - Verify deduplication results
   - Add row count validation before/after merge
   - Implement data quality metrics

### 3.2 Architectural Improvements
1. **Error Handling**
   - Add comprehensive error types
   - Add detailed logging for debugging
   - Note: Transaction rollback for partial operations needs further discussion as it's not a standard database transaction context

2. **Performance**
   - Implement chunked processing for large files
   - Add memory usage monitoring
   - Consider lazy evaluation where possible

### 3.3 Testing Strategy
1. **Unit Tests**
   - Test each handler independently
   - Verify deduplication edge cases
   - Test error conditions

2. **Integration Tests**
   - End-to-end pipeline tests
   - Performance benchmarking
   - Memory usage monitoring

## 4. Function Usage and Placement Analysis

### 4.1 Potentially Unused Functions in Pipeline
### 4.1 Function Usage Analysis

1. **`load_csv_file`**
   - Status: **Deprecated**
   - Reason: Statement handlers now handle their own file loading in the base class
   - Action: Mark for removal in next major version

2. **`process_with_handler`**
   - Status: **Duplicate Logic**
   - Issue: Similar functionality exists in `_load_and_process_files` in `dw_director`
   - Recommendation: 
     - Move this logic to pipeline to maintain clean orchestration
     - Organize pipeline functions in order of their utilization
     - Remove the duplicate implementation

3. **`move_to_unrecognised`**
   - Status: **Needs Refactoring**
   - Current: Similar functionality in `_handle_unrecognized_files` in `dw_director`
   - Recommendation:
     - Move file operation logic to pipeline
     - Keep only orchestration in director
     - Update director to use the pipeline function

### 4.2 Potential Function Reorganization
### 4.2 Function Reorganization Recommendations

1. **Move to Pipeline**
   - `_backup_and_cleanup_originals` (from `dw_director`)
   - `_save_master_file` (from `dw_director`)
   - Rationale: These are file operation utilities that align with the pipeline's responsibility
   - Status: **Approved** - Confirmed this is the correct approach

2. **Consolidate Functions**
   - `load_csv_file`
     - Status: **Deprecated**
     - Action: Remove entirely as statement handlers now handle their own file loading
     - Note: No need to replace with `load_csv_to_df` as file loading is now handled by the statement handlers
   
   - `move_to_unrecognised` and `_handle_unrecognized_files`
     - Action: Consolidate in pipeline and update director to use it
   - Status: **Approved** - Confirmed this is the correct approach

## 5. Conclusion and Recommendations

The current implementation provides a solid foundation but could benefit from the following improvements:

1. **Code Cleanup**:
   - CONSOLIDATE, Remove or deprecate unused functions (`load_csv_file`, `process_with_handler`, `move_to_unrecognised`)
   - Consolidate duplicate functionality

2. **Function Reorganization**:
   - Move file operation functions from `dw_director` to `dw_pipeline`
   - Keep orchestration logic in `dw_director`

3. **Documentation**:
   - Add docstrings to clarify function purposes and usage
   - Document the expected behavior of the deduplication logic

4. **Error Handling**:
   - Add more specific error messages for debugging
   - Consider adding validation for the composite key columns

## 6. Implementation Priorities

### Primary Focus Areas
1. **Clarity and Structure**
   - Ensure code is easy to understand and maintain
   - Follow consistent patterns across the codebase
   - Document architectural decisions

2. **Core Functionality**
   - Ensure accurate data processing
   - Maintain data integrity
   - Provide clear error messages

3. **Technical Debt**
   - Address critical technical debt that impacts stability
   - Document known issues for future iterations

### Secondary Considerations (Post-MVP)
- Performance optimizations
- Advanced error recovery
- Memory usage improvements
- Additional logging and monitoring

## 7. DEV_NOTES Addressed

- [x] Clarified filename usage in file processing
- [x] Specified handler registry location
- [x] Added error handling for empty DataFrames
- [x] Clarified deduplication logic and composite key usage
- [x] Corrected the recommendation about BALANCE in composite key
- [x] Added function usage and placement analysis
- [x] Provided recommendations for code reorganization
- [x] Addressed all user comments marked with '>>' 
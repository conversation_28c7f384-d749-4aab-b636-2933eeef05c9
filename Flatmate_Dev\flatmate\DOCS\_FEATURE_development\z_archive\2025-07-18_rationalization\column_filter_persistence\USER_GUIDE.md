# Filter Persistence & Advanced Search - User Guide

## Overview
The table view now includes advanced filtering capabilities with automatic persistence and powerful search operators. Your filter settings are automatically saved and restored between sessions.

## Quick Start

### Basic Filtering
1. **Select a column** from the dropdown (defaults to "Details")
2. **Type your search term** in the filter box
3. **Results update automatically** as you type
4. **Your settings are saved** automatically for next time

### Example
- Select "Details" column
- Type "coffee" → Shows only transactions containing "coffee"
- Close and reopen the app → Filter is automatically restored

## Advanced Search Operators

### AND Search (Multiple Terms)
Use **spaces** to separate terms that must AL<PERSON> be present.

**Examples:**
- `coffee shop` → Shows transactions containing both "coffee" AND "shop"
- `grocery store` → Shows transactions containing both "grocery" AND "store"
- `gas station fuel` → Shows transactions containing all three terms

### EXCLUDE Search (Negative Terms)
Use **-term** (dash prefix) to exclude transactions containing that term.

**Examples:**
- `coffee -refund` → Shows coffee transactions but excludes refunds
- `shop -online` → Shows shop transactions but excludes online purchases
- `-transfer` → Excludes all transactions containing "transfer"

### Combined Search
Mix AND and EXCLUDE operators for powerful filtering.

**Examples:**
- `restaurant -mcdonalds -kfc` → Shows restaurant transactions except fast food
- `fuel gas -credit` → Shows fuel/gas transactions not on credit card
- `grocery -walmart -costco` → Shows grocery shopping except at specific stores

## Column Selection

### Available Options
- **Details** (Default) - Search transaction descriptions
- **All Columns** - Search across all visible columns
- **Amount** - Search transaction amounts
- **Account** - Search account names
- **Date** - Search transaction dates
- **Tags** - Search user-assigned tags
- **Category** - Search transaction categories

### Performance Note
"All Columns" search is optimized but may be slightly slower with large datasets. Use specific columns for best performance.

## Filter Persistence

### Automatic Saving
- **Column selection** is remembered between sessions
- **Search pattern** is automatically saved
- **Settings persist** across app restarts
- **Per-module memory** - Each module remembers its own filters

### Manual Control
- **Clear filters** using the clear button (X)
- **Change columns** anytime - filter reapplies to new column
- **Disable persistence** in settings if preferred

## Tips & Tricks

### Effective Search Strategies
1. **Start broad, then narrow** - Begin with general terms, add exclusions
2. **Use specific terms** - "starbucks" vs "coffee shop"
3. **Combine operators** - Mix AND and EXCLUDE for precision
4. **Try different columns** - Amount column for value ranges, etc.

### Common Patterns
- **Find specific merchant**: `starbucks -gift` (Starbucks but not gift cards)
- **Exclude transfers**: `your-search -transfer -payment`
- **Find large expenses**: Switch to Amount column, search `>100` or `-1`
- **Date ranges**: Switch to Date column, search `2024-01` for January 2024

### Troubleshooting
- **No results?** Check if exclusion terms are too broad
- **Too many results?** Add more specific AND terms
- **Filter not working?** Ensure correct column is selected
- **Performance slow?** Try specific column instead of "All Columns"

## Technical Details

### Search Behavior
- **Case insensitive** - "Coffee" matches "coffee" and "COFFEE"
- **Partial matching** - "shop" matches "shopping" and "workshop"
- **Live filtering** - Results update as you type
- **Instant response** - No need to press Enter or click Apply

### Operator Rules
- **Spaces separate terms** - Each space creates a new AND condition
- **Dash prefix excludes** - Must be at start of term: `-refund`
- **Literal dashes** - Single `-` or `term-with-dash` treated literally
- **No special quotes** - All text treated as search terms

### Data Scope
- **Visible data only** - Filters only affect displayed transactions
- **Respects other filters** - Works with date ranges, account filters, etc.
- **Export integration** - Exported data matches filtered view exactly

## Settings & Configuration

### Persistence Settings
Filter persistence can be controlled in the application settings:
- **Enable/disable** automatic filter saving
- **Clear saved filters** to reset to defaults
- **Set default column** for new filter sessions

### Performance Settings
For large datasets, consider:
- **Disable "All Columns"** search if too slow
- **Use specific columns** for better performance
- **Clear filters** when not needed to improve scrolling

---

## Need Help?
- **Clear all filters** and start fresh if confused
- **Try simple terms first** before using advanced operators
- **Check column selection** if results seem wrong
- **Remember**: Spaces = AND, Dash = EXCLUDE

**Happy filtering!** 🔍

# Refactoring Plan: `dw_director` and `dw_pipeline`

## 1. Objective

This plan outlines the refactoring steps for the `dw_director` and `dw_pipeline` modules, based on the analysis in `report.md`. The primary goal is to improve the architecture by enforcing a clear separation of concerns:

-   **`dw_director`**: Will act as a pure orchestrator. Its sole responsibility is to manage the high-level workflow, call the appropriate services, and handle process tracking.
-   **`dw_pipeline`**: <PERSON> own all data processing and file system operations. This includes loading, merging, validating, and saving data, as well as file cleanup and backups.

## 2. Guiding Principles

-   **Single Responsibility**: Each module and function should have a single, well-defined purpose.
-   **Clear Data Flow**: Data should flow from the director to the pipeline for processing, with results and statistics returned.
-   **Code Consolidation**: Eliminate duplicate and deprecated code to simplify maintenance.

## 3. Actionable Steps

### Step 1: Consolidate and Remove Deprecated/Duplicate Functions

The `dw_pipeline` contains functions that are either no longer used or have logic that is duplicated in the `dw_director`.

-   [ ] **Remove `load_csv_file` from `dw_pipeline.py`**: This function is deprecated. File loading is now the responsibility of the individual statement handlers.
-   [ ] **Remove `process_with_handler` from `dw_pipeline.py`**: This function's logic is a duplicate of the handler processing loop in `dw_director._load_and_process_files`. Removing it centralizes the logic in the director.
-   [ ] **Remove `move_to_unrecognised` from `dw_pipeline.py`**: This function's logic will be consolidated into a new, more robust file-handling utility within the pipeline (see Step 2).

### Step 2: Consolidate File Operations in `dw_pipeline`

**User Concern Addressed**: `dw_pipeline.py` already contains the necessary file handling functions. The goal is to eliminate the duplicate logic in `dw_director`.

-   [ ] **Consolidate Backup Logic**: Remove `_backup_and_cleanup_originals` from `dw_director.py`. Update the director to call the existing `back_up_originals` function from the pipeline.
-   [ ] **Consolidate Save Logic**: Remove `_save_master_file` from `dw_director.py`. Update the director to call the existing `save_master_file` function from the pipeline.
-   [ ] **Consolidate Unrecognized File Logic**: Remove the logic for handling unrecognized files from `dw_director.py`. Update the director to call the existing `move_to_unrecognised` function from the pipeline for each unrecognized file.

### Step 3: Refactor `dw_director` to be a Pure Orchestrator

Update the `dw_director` to delegate all file and data operations to the `dw_pipeline`.

-   [ ] **Update `dw_director.dw_director()`**: Modify the main method to call the newly relocated pipeline functions (`save_master_file`, `backup_and_cleanup_files`, `handle_unrecognized_files`).
-   [ ] **Remove File System Logic**: Ensure no `os` or `shutil` calls related to file manipulation remain in `dw_director.py`. The director should only pass lists of file paths to the pipeline.

### Step 4: Add Final Validation Check

**User Concern Addressed**: You are correct; re-standardizing data is redundant if the handlers are working correctly. A validation check is a better approach to catch inconsistencies.

-   [ ] **Implement a `final_validation_check` function in `dw_pipeline.py`**: This function will run after data is merged but before it's saved. It will check for rows that have a non-zero `AMOUNT` but share the same `UNIQUE_ID` or `BALANCE`, as this indicates a potential data integrity issue.
-   [ ] **Integrate into `dw_director`**: Call this validation function from the director after the `merge_dataframes` step. The director will be responsible for logging any validation failures and deciding how to proceed. 

## 4. Expected Outcome

-   `dw_director.py` is significantly simplified, containing only high-level orchestration logic.
-   `dw_pipeline.py` is a comprehensive utility module for all data and file system tasks.
-   Code is cleaner, with no duplicate or dead code.
-   The separation of concerns makes the system easier to understand, maintain, and test.



# >> DEV_NOTES:
>> regards the proposed new validation step - 
- logically - same date - non zero amount, and a duplicate balance OR unique ID -this would indicate a missed duplicate-
ignoring the details column - as it may not always consistent across handlers ( it should be but may change with versions, over time )
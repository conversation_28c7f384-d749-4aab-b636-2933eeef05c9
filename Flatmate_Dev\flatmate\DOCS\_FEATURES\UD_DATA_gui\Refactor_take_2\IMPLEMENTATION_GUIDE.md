# UD_DATA_gui Refactor - Implementation Guide

## Overview
This guide provides step-by-step instructions for implementing the SimpleStateCoordinator approach based on the technical analysis and user journey flow documents.

## Architecture Decision
**Chosen Path**: SimpleStateCoordinator (Path 3)
- Centralized state management without CSV complexity
- Preserves current working architecture
- Minimal risk implementation
- Testable and maintainable

## Implementation Steps

### Phase 1: Cleanup (30 minutes)
1. **Remove unused StateEngine files**
   - Delete `StateEngine.py` if exists
   - Remove `ViewModel.py` files
   - Clean up any `state_table.csv` references

2. **Update imports**
   - Remove StateEngine imports from `ud_presenter.py`
   - Clean up disconnected state table references

### Phase 2: Create SimpleStateCoordinator (2-3 hours)

#### File: `simple_state_coordinator.py`
```python
class SimpleStateCoordinator:
    """
    Centralized state management for Update Data GUI.
    Encapsulates all UI state transitions in a single, testable location.
    """
    
    def __init__(self, view, guide_pane):
        self.view = view
        self.guide_pane = guide_pane
        self.state = {
            'source_configured': False,
            'destination_configured': False,
            'processing': False,
            'source_type': None,  # 'folder' or 'files'
            'source_path': None,
            'destination_path': None,
            'file_count': 0,
            'supported_types': ['.csv', '.pdf', '.ofx']
        }
    
    def set_source_folder(self, path: str, file_count: int):
        """Handle folder selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'folder',
            'source_path': path,
            'file_count': file_count
        })
        self._update_ui_state()
        self.guide_pane.display(f"Found {file_count} files ready for processing")
    
    def set_source_files(self, files: list[str]):
        """Handle file selection."""
        self.state.update({
            'source_configured': True,
            'source_type': 'files',
            'source_path': files,
            'file_count': len(files)
        })
        self._update_ui_state()
        self.guide_pane.display(f"Selected {len(files)} files for processing")
    
    def set_destination_same_as_source(self):
        """Handle same-as-source destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': 'same_as_source'
        })
        self._update_ui_state()
        self.guide_pane.display("Files will be moved to 'Archive' subfolder")
    
    def set_destination_custom(self, path: str):
        """Handle custom destination."""
        self.state.update({
            'destination_configured': True,
            'destination_path': path
        })
        self._update_ui_state()
        self.guide_pane.display(f"Archive folder will be created in {Path(path).name}")
    
    def is_ready_to_process(self) -> bool:
        """Check if all requirements are met."""
        return self.state['source_configured'] and self.state['destination_configured']
    
    def _update_ui_state(self):
        """Update all UI elements based on current state."""
        # Update PROCESS FILES button
        ready = self.is_ready_to_process()
        self.view.set_process_button_enabled(ready)
        
        # Update Archive section
        self.view.set_archive_section_enabled(self.state['source_configured'])
        
        # Update guide messages
        if ready:
            self.guide_pane.display(f"Ready to process {self.state['file_count']} files")
```

### Phase 3: Integration (1 hour)

#### Update `ud_presenter.py`
```python
from simple_state_coordinator import SimpleStateCoordinator

class UpdateDataPresenter:
    def __init__(self):
        self.state_coordinator = SimpleStateCoordinator(self.view, self.guide_pane)
        
    def on_source_folder_selected(self, path: str, file_count: int):
        self.state_coordinator.set_source_folder(path, file_count)
        self._update_file_display()
        
    def on_source_files_selected(self, files: list[str]):
        self.state_coordinator.set_source_files(files)
        self._update_file_display()
        
    def on_destination_same_selected(self):
        self.state_coordinator.set_destination_same_as_source()
        
    def on_destination_custom_selected(self, path: str):
        self.state_coordinator.set_destination_custom(path)
```

#### File Display Section
```python
def _update_file_display(self):
    """Update the file display section with current selections."""
    files = self.state_coordinator.state['source_path']
    if isinstance(files, str):  # folder
        file_list = self._get_files_from_folder(files)
    else:  # files list
        file_list = files
    
    self.view.file_display.update_files(file_list)
```

### Phase 4: File Type Support

#### Add PDF/OFX support in file discovery
```python
def discover_files(self, path: str) -> list[str]:
    """Discover all supported file types in path."""
    supported_extensions = ['.csv', '.pdf', '.ofx']
    files = []
    
    for ext in supported_extensions:
        files.extend(glob.glob(os.path.join(path, f"*{ext}")))
    
    return sorted(files)
```

## Testing Strategy

### Unit Tests
```python
class TestSimpleStateCoordinator(unittest.TestCase):
    def test_initial_state(self):
        coordinator = SimpleStateCoordinator(mock_view, mock_guide)
        self.assertFalse(coordinator.is_ready_to_process())
    
    def test_source_folder_selection(self):
        coordinator.set_source_folder("/test/path", 5)
        self.assertTrue(coordinator.state['source_configured'])
        self.assertEqual(coordinator.state['file_count'], 5)
    
    def test_ready_state(self):
        coordinator.set_source_folder("/test/path", 5)
        coordinator.set_destination_same_as_source()
        self.assertTrue(coordinator.is_ready_to_process())
```

### Integration Tests
- Test complete user flow from source selection to processing
- Verify guide_pane messages at each state
- Test file type recognition (CSV, PDF, OFX)

## Visual Verification Checklist
- [ ] Initial state: PROCESS FILES button disabled
- [ ] After source selection: Archive section becomes active
- [ ] After destination selection: PROCESS FILES button enabled
- [ ] guide_pane displays contextual messages
- [ ] File display section shows correct file details
- [ ] All supported file types (.csv, .pdf, .ofx) are recognized

## Risk Mitigation
- Keep existing working code untouched until new coordinator is fully tested
- Implement incrementally: one state transition at a time
- Maintain backward compatibility during transition
- Use feature flags if needed for gradual rollout

## Time Estimate
- **Phase 1**: 30 minutes
- **Phase 2**: 2-3 hours  
- **Phase 3**: 1 hour
- **Phase 4**: 30 minutes
- **Testing**: 1 hour
- **Total**: 4-5 hours

#!/usr/bin/env python3
"""
Local Event Bus for Update Data Module

Provides internal event coordination separate from global event bus.
Handles UI state management, view coordination, and local business events.
"""

from typing import Any, Callable, Dict, List, Optional
import threading
from datetime import datetime
from enum import Enum, auto


class LocalEventBus:
    """
    Local event bus for internal module communication.
    
    Separate from global event bus to avoid cross-module pollution
    and provide module-specific event handling with debugging capabilities.
    """
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self._listeners: Dict[str, List[Callable]] = {}
        self._lock = threading.Lock()
        self._event_log: List[Dict] = []
        self._max_log_size = 1000  # Prevent memory bloat
        
    def subscribe(self, event_type: str, listener: Callable):
        """
        Subscribe to local events.
        
        Args:
            event_type: String identifier for the event
            listener: Callable that will handle the event
        """
        with self._lock:
            if event_type not in self._listeners:
                self._listeners[event_type] = []
            self._listeners[event_type].append(listener)
            
    def unsubscribe(self, event_type: str, listener: Callable):
        """
        Unsubscribe from local events.
        
        Args:
            event_type: String identifier for the event
            listener: Callable to remove
        """
        with self._lock:
            if event_type in self._listeners:
                try:
                    self._listeners[event_type].remove(listener)
                except ValueError:
                    pass  # Listener not found
                    
    def emit(self, event_type: str, data: Any = None):
        """
        Emit local event to all subscribers.
        
        Args:
            event_type: String identifier for the event
            data: Optional data to pass to listeners
        """
        # Log event for debugging
        self._log_event(event_type, data)
        
        # Get listeners (copy to avoid modification during iteration)
        listeners = self._listeners.get(event_type, []).copy()
        
        # Notify all listeners
        for listener in listeners:
            try:
                listener(data)
            except Exception as e:
                print(f"Local event error in {self.module_name}.{event_type}: {e}")
                # Could also emit an error event here
                
    def emit_and_wait(self, event_type: str, data: Any = None, timeout: float = 5.0) -> Any:
        """
        Emit event and wait for a response (for dialog interactions).
        
        Args:
            event_type: String identifier for the event
            data: Optional data to pass to listeners
            timeout: Maximum time to wait for response
            
        Returns:
            Response data from the first listener that returns a value
        """
        # This is a simplified implementation
        # In practice, you might want a more sophisticated request/response pattern
        self.emit(event_type, data)
        # For now, just return None - implement proper async handling if needed
        return None
        
    def bridge_to_global(self, local_event: str, global_event: str, transform_fn: Optional[Callable] = None):
        """
        Bridge local events to global event bus.
        
        Args:
            local_event: Local event type to bridge
            global_event: Global event type to emit
            transform_fn: Optional function to transform data before bridging
        """
        def bridge_handler(data):
            transformed_data = transform_fn(data) if transform_fn else data
            # Import here to avoid circular imports
            from fm.core.services.event_bus import global_event_bus
            global_event_bus.publish(global_event, transformed_data)
            
        self.subscribe(local_event, bridge_handler)
        
    def get_event_log(self, event_type: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """
        Get recent event log for debugging.
        
        Args:
            event_type: Optional filter by event type
            limit: Maximum number of events to return
            
        Returns:
            List of event log entries
        """
        with self._lock:
            events = self._event_log[-limit:] if limit else self._event_log
            
            if event_type:
                events = [e for e in events if e['type'] == event_type]
                
            return events
            
    def clear_event_log(self):
        """Clear the event log."""
        with self._lock:
            self._event_log.clear()
            
    def _log_event(self, event_type: str, data: Any):
        """Log event for debugging purposes."""
        with self._lock:
            self._event_log.append({
                'type': event_type,
                'data': data,
                'timestamp': datetime.now(),
                'module': self.module_name
            })
            
            # Trim log if it gets too large
            if len(self._event_log) > self._max_log_size:
                self._event_log = self._event_log[-self._max_log_size//2:]


class ViewEvents(Enum):
    """Local view events for Update Data module."""
    
    # User Actions (View → Presenter)
    SOURCE_SELECT_REQUESTED = "source_select_requested"
    DESTINATION_SELECT_REQUESTED = "destination_select_requested"
    PROCESS_REQUESTED = "process_requested"
    CANCEL_REQUESTED = "cancel_requested"
    SOURCE_OPTION_CHANGED = "source_option_changed"
    SAVE_OPTION_CHANGED = "save_option_changed"
    UPDATE_DATABASE_CHANGED = "update_database_changed"
    
    # Business Events (Presenter → State)
    SOURCE_DISCOVERED = "source_discovered"
    DESTINATION_CONFIGURED = "destination_configured"
    PROCESSING_STARTED = "processing_started"
    PROCESSING_COMPLETED = "processing_completed"
    BUSINESS_ERROR = "business_error"
    
    # State Events (State → View)
    UI_STATE_CHANGED = "ui_state_changed"
    STATUS_MESSAGE_CHANGED = "status_message_changed"
    
    # Dialog Events (Bidirectional)
    FOLDER_DIALOG_REQUESTED = "folder_dialog_requested"
    FOLDER_DIALOG_COMPLETED = "folder_dialog_completed"
    FILES_DIALOG_REQUESTED = "files_dialog_requested"
    FILES_DIALOG_COMPLETED = "files_dialog_completed"
    ERROR_DIALOG_REQUESTED = "error_dialog_requested"
    SUCCESS_DIALOG_REQUESTED = "success_dialog_requested"


class ViewEventData:
    """Data structures and factory methods for view events."""
    
    @staticmethod
    def source_select_request(selection_type: str, path: str = None):
        """Create source selection request data."""
        return {
            'type': selection_type,
            'path': path,
            'timestamp': datetime.now()
        }
        
    @staticmethod
    def source_discovered(source_type: str, files: List[str], path: str, count: int):
        """Create source discovery data."""
        return {
            'type': source_type,
            'files': files,
            'path': path,
            'count': count,
            'timestamp': datetime.now()
        }
        
    @staticmethod
    def destination_configured(destination_type: str, path: str):
        """Create destination configuration data."""
        return {
            'type': destination_type,  # 'same_as_source' or 'custom'
            'path': path,
            'timestamp': datetime.now()
        }
        
    @staticmethod
    def ui_state_update(can_process: bool, message: str, **kwargs):
        """Create UI state update data."""
        return {
            'can_process': can_process,
            'status_message': message,
            'timestamp': datetime.now(),
            **kwargs
        }
        
    @staticmethod
    def business_error(error_type: str, message: str, context: Dict = None):
        """Create business error data."""
        return {
            'error_type': error_type,
            'message': message,
            'context': context or {},
            'timestamp': datetime.now()
        }
        
    @staticmethod
    def dialog_request(dialog_type: str, title: str, **kwargs):
        """Create dialog request data."""
        return {
            'dialog_type': dialog_type,
            'title': title,
            'timestamp': datetime.now(),
            **kwargs
        }
        
    @staticmethod
    def processing_context(files: List[str], destination: str, options: Dict):
        """Create processing context data."""
        return {
            'files': files,
            'destination': destination,
            'options': options,
            'file_count': len(files),
            'timestamp': datetime.now()
        }


# Module-specific event bus instance
update_data_local_bus = LocalEventBus('update_data')


# Convenience functions for common event patterns
def emit_user_action(action_type: ViewEvents, data: Dict = None):
    """Emit a user action event."""
    update_data_local_bus.emit(action_type.value, data)


def emit_business_event(event_type: ViewEvents, data: Dict = None):
    """Emit a business logic event."""
    update_data_local_bus.emit(event_type.value, data)


def emit_ui_update(ui_state: Dict):
    """Emit a UI state update event."""
    update_data_local_bus.emit(ViewEvents.UI_STATE_CHANGED.value, ui_state)


def subscribe_to_view_events(event_type: ViewEvents, handler: Callable):
    """Subscribe to view events with type safety."""
    update_data_local_bus.subscribe(event_type.value, handler)


def get_debug_log(event_filter: str = None, limit: int = 50) -> List[Dict]:
    """Get debug event log."""
    return update_data_local_bus.get_event_log(event_filter, limit)

# Update Data View Refactor Plan

**Date**: 2025-07-25  
**Status**: Ready for Implementation  
**Approach**: Specific Implementations Only (No Shared Widgets)

## Rationale

Current codebase suffers from fragmentation with 112+ files across multiple directory levels. This plan consolidates to ~20 files using specific implementations only, following Pythonic principles:

- **Single Responsibility**: Each file has one clear purpose
- **Explicit over Implicit**: Clear naming conventions
- **Flat Structure**: Maximum 2-3 directory levels
- **Testable**: Each component can be tested independently

## Proposed Structure (v2)

```
# Shared components (in main window)
main_window/shared/
├── base_widget.py          # Base widget classes (Qt-specific)
├── base_panel.py           # Base panel class
└── common_widgets.py       # Reusable Qt widgets

# Update Data module - View Components
update_data/view_components/     # High-level UI components
├── panels/                      # Panel implementations (composite components)
│   ├── center_panel.py          # Center panel (uses widgets)
│   ├── left_panel.py            # Left panel (uses widgets)  
│   └── right_panel.py           # Right panel (uses widgets)
└── widgets/                     # Qt widgets (leaf components)
    ├── shared/                  # Widgets used by multiple panels
    │   ├── file_browser.py      # File browser widget
    │   └── status_display.py    # Status display widget
    ├── center/                  # Center panel widgets
    └── left/                    # Left panel widgets
├── __init__.py                  # Clean exports
└── README.md                    # Updated documentation
```

## Naming Convention

**Panel Prefixes** (for immediate context):
- `CenterPanel` - Center panel manager
- `LeftPanel` - Left panel manager  
- `RightPanel` - Right panel (preserved)

**Widget Organization**:
- `center_widgets.py` - Center panel specific widgets
- `left_widgets.py` - Left panel specific widgets
- `right_widgets.py` - Right panel specific widgets

## Gradual Implementation Plan

### Phase 1: Foundation (Week 1)
- [ ] Create new directory structure
- [ ] Consolidate center panel files into `center_panel.py`
- [ ] Consolidate left panel files into `left_panel.py`
- [ ] Preserve right panel structure
- [ ] Update imports in main files
- [ ] Test basic functionality

### Phase 2: Widget Consolidation (Week 2)
- [ ] Merge widget files into panel-specific files
- [ ] Consolidate `_switcher.py` into `center_panel.py`
- [ ] Consolidate `_panel_manager.py` files into respective panels
- [ ] Test panel interactions
- [ ] Update documentation

### Phase 3: Cleanup (Week 3)
- [ ] Archive original fragmented files
- [ ] Update import statements
- [ ] Final testing
- [ ] Update documentation
- [ ] Verify no breaking changes

## Testing Strategy

**Incremental Testing**:
1. **Unit Tests**: Test each consolidated component
2. **Integration Tests**: Test panel interactions
3. **Regression Tests**: Ensure no functionality lost
4. **Manual Testing**: Verify UI behavior

**Testing Commands**:
```bash
# Test individual panels
python -m pytest tests/test_center_panel.py
python -m pytest tests/test_left_panel.py

# Test integration
python -m pytest tests/test_panel_interactions.py

# Full regression test
python tests/test_real_csvs.py
```

## Migration Checklist

**Before Consolidation**:
- [ ] Backup current implementation
- [ ] Document current functionality
- [ ] Identify shared functionality
- [ ] Create test cases for current behavior

**During Consolidation**:
- [ ] Implement new structure
- [ ] Migrate functionality incrementally
- [ ] Test at each step
- [ ] Update documentation

**After Consolidation**:
- [ ] Archive original files
- [ ] Update import statements
- [ ] Final testing
- [ ] Update README

## Risk Mitigation

**Reversible Process**:
- All changes are incremental and testable
- Original files archived before deletion
- Rollback plan available
- Comprehensive testing at each phase

**Verification Points**:
- Right panel structure preserved
- Essential functionality maintained
- No breaking changes to existing code
- Clear entry points maintained

## Success Metrics

- **File Count**: 112 → ~20 files
- **Directory Levels**: 4-5 → 2-3 levels
- **Import Clarity**: Explicit imports only
- **Test Coverage**: >90% for new structure
- **Developer Experience**: Clear, discoverable code

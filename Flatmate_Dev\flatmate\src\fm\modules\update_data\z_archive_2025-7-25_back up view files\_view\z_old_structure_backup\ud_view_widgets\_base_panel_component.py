"""
Base component interface for the Composite pattern in the center panel.
"""

from abc import abstractmethod

from PySide6.QtWidgets import QWidget


class BasePanelComponent(QWidget):
    """Base component for all center panel UI elements.
    
    This abstract class defines the interface for all components
    in the center panel composite pattern.
    """
    
    def __init__(self, parent=None):
        """Initialize the base component."""
        super().__init__(parent)
    
    @abstractmethod
    def show_component(self):
        """Show this component."""
    
    @abstractmethod
    def hide_component(self):
        """Hide this component."""
    
    def is_visible(self) -> bool:
        """Check if the component is visible."""
        return self.isVisible()
    
    def set_enabled(self, enabled: bool):
        """Enable or disable the component."""
        self.setEnabled(enabled)
    
    # Common methods that specific implementations may override
    def show_error(self, message: str):
        """Show error message."""
    
    def show_success(self, message: str):
        """Show success message."""
    
    def clear(self):
        """Clear component data."""
    
    def set_source_path(self, path: str):
        """Set source path information."""
    
    def set_save_path(self, path: str):
        """Set save path information."""
    
    def set_mode(self, mode: str):
        """Set component mode."""
    
    def get_files(self) -> list[str]:
        """Get files from this component if applicable."""
        return []
    
    def set_files(self, files: list, source_dir: str = ""):
        """Set files to be displayed in this component.
        
        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
    
    def display_dataframe(self, df, file_info: str = ""):
        """Display a DataFrame in this component if applicable.
        
        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """

# Column Order System Implementation

**Date:** 2025-07-17  
**Status:** ✅ COMPLETED  
**Version:** 1.0  

## Overview

The Column Order System provides consistent, logical column ordering throughout the Flatmate application, replacing the previous alphabetical ordering with FM-standard ordering based on user workflow and data importance.

## Architecture

### Core Components

1. **StandardColumnsOrder Enum** (`column_order.py`)
   - Defines canonical FM-standard column order
   - Provides utility methods for ordering operations
   - Serves as single source of truth for column priorities

2. **Column Dataclass** (`column_definition.py`)
   - Enhanced with `order` field
   - Maintains backward compatibility
   - Supports both ordering and legacy functionality

3. **ColumnOrderService** (`column_order_service.py`)
   - Manages user preferences with hierarchy
   - Handles persistence to YAML files
   - Provides API for order management

4. **Columns Registry** (`columns.py`)
   - Updated with order values for all columns
   - Provides ordering methods
   - Integrates with ColumnOrderService

## FM-Standard Column Order

The logical ordering prioritizes columns by user workflow importance:

### Priority Groups
1. **Core Transaction Data (1-10)**
   - Date (1), Details (2), Amount (3), Account (4), Balance (5)
   - Most frequently accessed information

2. **User Workflow Fields (11-20)**
   - Category (11), Tags (12), Notes (13)
   - User categorization and annotation

3. **Extended Transaction Details (21-40)**
   - Credit/Debit amounts, Payment types, Unique IDs
   - Additional transaction metadata

4. **Party Details (41-60)**
   - This Party (TP) and Other Party (OP) information
   - Detailed transaction participants

5. **Source/Import Metadata (61-80)**
   - Source filename, bank, import dates
   - Technical import information

6. **System Fields (81-99)**
   - Internal IDs, hashes, system metadata
   - Database management fields

## User Preference Hierarchy

The system implements a three-tier preference hierarchy:

1. **Per-module user preference** (highest priority)
   - Module-specific column orders (e.g., `categorize.display.column_order`)
   - Allows different ordering per module

2. **Global user preference** (medium priority)
   - Application-wide default (`display.column_order_global`)
   - Applies to all modules unless overridden

3. **FM-standard default** (fallback)
   - Built-in logical ordering from StandardColumnsOrder enum
   - Always available as fallback

## Implementation Details

### Key Files Modified

- `fm/core/data_services/standards/column_order.py` (NEW)
- `fm/core/data_services/standards/column_order_service.py` (NEW)
- `fm/core/data_services/standards/column_definition.py` (UPDATED)
- `fm/core/data_services/standards/columns.py` (UPDATED)
- `fm/modules/categorize/_view/components/center_panel/transaction_view_panel.py` (UPDATED)

### Critical Integration Points

1. **DataFrame Column Reordering**
   - Columns are reordered at DataFrame level before display
   - Ensures table view preserves intended order
   - Prevents alphabetical sorting override

2. **Display Name Mapping**
   - Order is maintained through db_name → display_name conversion
   - Consistent across all UI components

3. **Toolbar Integration**
   - Dropdowns automatically inherit correct ordering
   - Search and column selection menus use FM-standard order

## Usage Examples

### Getting Ordered Columns
```python
from fm.core.data_services.standards.columns import Columns

# Get ordered display columns for a module
ordered_cols = Columns.get_ordered_display_columns('categorize')

# Get default FM-standard order
default_order = Columns.get_default_order()
```

### Managing User Preferences
```python
from fm.core.data_services.standards.column_order_service import ColumnOrderService

service = ColumnOrderService()

# Set module-specific order
custom_order = ['category', 'tags', 'date', 'details', 'amount']
service.set_column_order(custom_order, 'categorize')

# Get order with preference hierarchy
order = service.get_column_order('categorize')

# Reset to default
service.reset_to_default('categorize')
```

### DataFrame Integration
```python
# Reorder DataFrame columns before display
ordered_columns = Columns.get_ordered_display_columns('categorize')
ordered_db_names = [col.db_name for col in ordered_columns]

# Reorder DataFrame
available_cols = [col for col in ordered_db_names if col in df.columns]
df_ordered = df[available_cols]
```

## Configuration

### User Preferences Storage
- **Location:** `~/.flatmate/preferences.yaml`
- **Format:** YAML with hierarchical keys
- **Example:**
```yaml
categorize.display.column_order:
  - date
  - details
  - amount
  - category
  - tags

display.column_order_global:
  - date
  - details
  - amount
  - account
```

### Module Integration
Modules can easily integrate the ordering system:

```python
# In module view components
ordered_columns = Columns.get_ordered_display_columns(module_name)
visible_columns = [col.display_name for col in ordered_columns]

# Reorder DataFrame
ordered_db_names = [col.db_name for col in ordered_columns]
available_ordered_cols = [col for col in ordered_db_names if col in df.columns]
df = df[available_ordered_cols]
```

## Benefits

1. **Consistent User Experience**
   - Logical column ordering across all modules
   - Predictable column placement

2. **User Customization**
   - Per-module and global preferences
   - Persistent user settings

3. **Developer Friendly**
   - Simple integration API
   - Clear ordering logic
   - Type-safe implementation

4. **Maintainable**
   - Single source of truth for ordering
   - Centralized preference management
   - Clean separation of concerns

## Testing

The implementation includes comprehensive testing:
- Unit tests for ordering logic
- Integration tests for UI components
- User preference persistence tests
- DataFrame reordering validation

## Future Enhancements

1. **UI Preference Management**
   - Settings panel for column order customization
   - Drag-and-drop column reordering

2. **Advanced Filtering**
   - Column visibility preferences
   - Context-sensitive column sets

3. **Performance Optimization**
   - Caching for frequently accessed orders
   - Lazy loading for large column sets

---

*Implementation completed: 2025-07-17*  
*Status: Production ready*

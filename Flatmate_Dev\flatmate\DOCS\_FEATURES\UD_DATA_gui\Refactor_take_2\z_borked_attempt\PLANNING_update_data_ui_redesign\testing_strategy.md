# Update Data UI - Testing Strategy

**Version**: 1.0  
**Date**: 2025-07-25  
**Status**: Definition

## 1. Overview

This document outlines the testing strategy for the redesigned Update Data UI. The approach includes unit tests for new, isolated components and integration/UAT tests for the complete, state-driven workflow.

## 2. Unit Testing

Unit tests will focus on the new components in isolation to ensure they function correctly before being integrated into the main application.

### 2.1. `SelectGroupWidget`

*   **Objective**: Verify the widget's internal logic, configuration, and signal emissions.
*   **Tests**:
    *   Test instantiation with a valid `SelectGroupConfig`.
    *   Verify that `value()` and `setValue()` correctly get and set the dropdown's current text.
    *   Verify that `set_options()` correctly updates the items in the dropdown.
    *   Test that the `button_clicked` signal is emitted exactly once when the button is pressed.
    *   Test that the `selection_changed` signal is emitted with the correct text when the dropdown selection is changed by the user.

### 2.2. `StateEngine`

*   **Objective**: Verify the engine's ability to correctly parse the state CSV.
*   **Tests**:
    *   Test with a valid mock CSV: ensure `get_initial_config` returns the correct dictionary for a given `component_id`.
    *   Test with a missing `component_id`: ensure it returns an empty dictionary.
    *   Test with a malformed CSV: ensure it handles the error gracefully.
    *   Test with a non-existent file path: ensure it logs an error.

### 2.3. `SourceSelectionViewModel`

*   **Objective**: Verify the `ViewModel`'s internal state logic and signal emissions.
*   **Setup**: Use a mock `StateEngine` to provide predictable configuration data.
*   **Tests**:
    *   **Initial State**: On instantiation, verify that the `ViewModel` emits the correct signals to configure the UI for the `INITIAL` state.
    *   **State Transitions**: Call the `ViewModel`'s public methods (e.g., `on_select_button_clicked`) and verify that it transitions to the correct new state.
    *   **Signal Emission**: For each state transition, use `QSignalSpy` to verify that the correct signals (e.g., `update_select_button`, `update_process_button`) are emitted with the expected values.
    *   **Logic**: Test edge cases, such as handling different dropdown selections (`Folder` vs. `Files`), to ensure the internal logic is sound.

## 3. Integration Testing

Integration tests will verify that the new components work together correctly within the live application context.

*   **Objective**: Test the end-to-end workflow between the View, `ViewModel`, and `StateEngine`.
*   **Key Scenarios**:
    1.  **Initialization**: Verify the UI loads and correctly configures itself based on the `ViewModel`'s initial signals, which are driven by the `StateEngine`.
    2.  **User Interaction**: Simulate a button click in the View. Verify that the correct method on the `ViewModel` is called.
    3.  **State Update**: Verify the `ViewModel` transitions its internal state and emits the correct update signals.
    4.  **UI Update**: Verify the View's slots receive the signals and update the UI widgets accordingly.
    5.  **Full Workflow**: Test the complete user journey from initial state -> folder selection -> source selected -> process, ensuring the UI reflects the `ViewModel`'s state at every step.

## 4. User Acceptance Testing (UAT)

UAT will be conducted to ensure the new UI is intuitive, meets the requirements outlined in the PRD, and provides a good user experience.

*   **Objective**: Validate the workflow from a user's perspective.
*   **Methodology**: Provide a user with a set of common tasks and observe their interaction with the UI.
*   **Tasks**:
    *   "Configure the application to monitor your 'Downloads' folder for new bank statements."
    *   "Add a new statement to the folder and process it."
    *   "Change the archive location to a different folder."

Feedback from UAT will be used to make final refinements to the UI and the state table definitions.

# Update Data Refactoring Analysis

## Presenter-View Interface Analysis

### Current Signal Connections (View → Presenter)
```python
# From ud_presenter.py lines 76-84
self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
self.view.source_select_requested.connect(self._handle_source_select)
self.view.save_select_requested.connect(self._handle_save_select)
self.view.source_option_changed.connect(self._handle_source_option_change)
self.view.save_option_changed.connect(self._handle_save_option_change)
self.view.process_clicked.connect(self._handle_process)
self.view.update_database_changed.connect(self._handle_update_database_change)
```

### Current Method Calls (Presenter → View)
```python
# View state queries
self.view.get_save_option()                    # line 87, 323, 435
self.view.get_update_database()                # line 136, 435

# View state updates  
self.view.set_save_select_enabled(bool)        # line 89, 364
self.view.set_source_option(str)              # line 133, 246
self.view.set_save_path(str)                  # line 335, 368, 388
self.view.set_process_mode()                  # line 316 (commented)

# Dialog displays
self.view.show_folder_dialog()                # line 179, 328
self.view.show_files_dialog()                 # line 208
self.view.show_error(str)                     # line 197, 270, 294, 421, 476, 488

# Display updates
self.view.display_selected_source(data)       # line 315 (commented)
self.view.cleanup()                           # line 521

# Widget access (violates abstraction)
self.view.left_panel_manager.buttons_widget   # line 253, 405
```

## Required View Interface

### Signals (View → Presenter)
```python
class ViewInterface:
    # Existing signals to keep
    cancel_clicked = Signal()
    source_select_requested = Signal()
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)
```

### Methods (Presenter → View)
```python
class ViewInterface:
    # State queries
    def get_save_option(self) -> str: ...
    def get_update_database(self) -> bool: ...
    
    # State updates
    def set_save_select_enabled(self, enabled: bool): ...
    def set_source_option(self, option: str): ...
    def set_save_path(self, path: str): ...
    def set_process_button_text(self, text: str): ...
    
    # Dialogs
    def show_folder_dialog(self, title: str, initial_dir: str) -> str: ...
    def show_files_dialog(self, title: str, initial_dir: str) -> list[str]: ...
    def show_error(self, message: str): ...
    
    # Display management
    def display_selected_source(self, source_data: dict): ...
    def cleanup(self): ...
```

## Migration Steps

### Phase 1: Create View Interface
1. Create `i_view_interface.py` with abstract methods
2. Create `i_view_signal_router.py` which provides the concrete implementation of the view interface, mapping interface methods and signals to the actual Qt widgets and logic.
3. Update view to implement interface

### Phase 2: Update Presenter
1. Replace all `self.view.method()` with `self.i_view_interface.method()`
2. Update signal connections to use interface signals
3. Remove direct widget access

### Phase 3: Test
1. Create mock view for presenter testing
2. Verify all presenter logic works with interface
3. Test view implementation matches interface

## Files to Update
- `ud_presenter.py` - replace view calls with interface
- `ud_view.py` - implement view interface
- `i_view_interface.py` - new interface definition
- Tests - update to use interface

## files no longer needed
files or folders of files no longer needed should have z_deprecated_ prefix added

### identified for deprecation: 

**Files to deprecate:**
- `_view_components/state/view_context_manager.py` - replaced by view interface methods
- `_view_components/state/ui_modes.py` - replaced by presenter state management
- `_view_components/state/state_coordinator.py` - replaced by presenter state logic

**Files to keep:**
- `ud_view.py` - becomes view interface implementation
- `center_panel.py` - complex widgets stay separate
- `left_panel.py` - complex widgets stay separate
- `right_panel.py` - complex widgets stay separate
- `ud_presenter.py` - updated to use interface
- `view_events.py` - kept for event bus communication

**New files to create:**
- `i_view_interface.py` - view interface definition
- `i_view_signal_router.py` - interface implementation mapping to Qt widgets

**Directory structure after refactoring:**
```
update_data/
├── interface/
│   ├── i_view_interface.py          # View interface definition (NEW)
│   └── i_view_signal_router.py      # Interface-to-Qt signal mapping (NEW)
├── ud_presenter.py                  # Presenter (uses interface, UPDATED)
├── ud_view.py                       # View implementation (UPDATED)
├── components/
│   ├── center_panel.py              # Complex widgets (KEEP)
│   ├── left_panel.py                # Complex widgets (KEEP)
│   ├── right_panel.py               # Complex widgets (KEEP)
│   └── state/
│       ├── z_deprecated_view_context_manager.py
│       ├── z_deprecated_ui_modes.py
│       ├── z_deprecated_state_coordinator.py
│       └── view_events.py           #>> Event bus (KEEP) events should stay in state ? - >> # TODO: Consider moving out of state/ ?

2025-07-28 @ 13:24:52
from enum import Enum
from dataclasses import dataclass
from pydantic import BaseModel

class UI_STATES(Enum):
    INITIAL = "initial"
    FOLDER_SELECTED = "folder_selected"
    READY_TO_PROCESS = "ready_to_process"
    PROCESSING = "processing"
    COMPLETE = "complete"
    ERROR = "error"
    ARCHIVE_PENDING = "archive_pending"

class ComponentState:
    enabled: bool = True
    visible: bool = True
    text: str = ""
    options: list = None
    checked: bool = None
    tooltip: str = None
    
@dataclass
class option_select_group_state:
    label_text: str
    button_text: str
    menu_options: list
    default_option: str = None
    object_name: str = "select_group_widget"
    

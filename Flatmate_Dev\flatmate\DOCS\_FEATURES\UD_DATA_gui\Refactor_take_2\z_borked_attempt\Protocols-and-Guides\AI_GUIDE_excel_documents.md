# AI Excel Guide: Quick Reference

**Key Rule**: Use `'` prefix to prevent formula interpretation

## 🎯 **Core Rules**

1. **Escape Formula Triggers**: Use `'=== HEADER ===` not `=== HEADER ===`
2. **Format for Humans**: Use colors, bold headers, merged cells - make it readable
3. **ASCII Over Unicode**: Use `->` not `→` for arrows
4. **Structure Data**: Separate sheets for different data types

## 📋 **Quick Examples**

```python
# ✅ SAFE - Properly escaped
data = [["'=== COMPONENTS ===", "Type", "Status"],
        ["source_menu", "OptionMenu", "active"]]

# ❌ RISKY - Could trigger formulas
data = [["=== COMPONENTS ===", "Type", "=active"]]
```

## 🎨 **Formatting Standards**

- **Headers**: Bold + green background (#2E7D32)
- **Sub-headers**: Light green background (#E8F5E8)
- **Structure**: Merged cells for major sections
- **Colors**: Consistent green theme throughout

## ⚠️ **Warning Signs**

Watch for these formula triggers:
- Text starting with `=`, `+`, `-`, `@`
- Unicode symbols (arrows, math symbols)
- Complex expressions that look like formulas

## 🚀 **Best Practice**

**Before writing Excel data**: Check for formula triggers → Add `'` prefix if needed → Format for human readability

**Result**: Clean, formatted Excel files without corruption
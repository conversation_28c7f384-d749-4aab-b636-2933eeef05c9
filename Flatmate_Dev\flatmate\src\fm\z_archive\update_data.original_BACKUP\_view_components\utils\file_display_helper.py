"""
Utility for extracting file display information.
"""

import os
import pprint
from typing import Dict, Any, Optional

import pandas as pd

from ...utils.statement_handlers._handler_registry import get_handler


class FileDisplayHelper:
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        Extract file information using the new file-first handler detection.

        Args:
            file_path: Path to the file

        Returns:
            Dictionary containing file information including size and format.
        """
        try:
            # Get file size
            size = os.path.getsize(file_path)
            size_str = f"{size / 1024:.1f} KB" if size < 1024 * 1024 else f"{size / (1024 * 1024):.1f} MB"

            # Use statement handlers for format detection directly on the file path
            handler = get_handler(file_path)

            # Prepare format info
            format_info = {
                'bank_name': 'Unknown',
                'variant': 'Unknown',
                'file_type': 'Unknown',
                'handler_class': None,
                'display_name': 'Unrecognized File Type'
            }

            if handler:
                fmt = handler.statement_type
                display_name = f"{fmt.bank_name} {fmt.variant} {fmt.file_type}".replace("  ", " ").strip()
                format_info.update({
                    'bank_name': fmt.bank_name,
                    'variant': fmt.variant,
                    'file_type': fmt.file_type,
                    'handler_class': handler.__class__.__name__,
                    'display_name': display_name
                })

            # Prepare file info dictionary
            file_info = {
                'path': file_path,
                'size_bytes': size,
                'size_str': size_str,
                **format_info
            }

            return file_info

        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return {
                'path': file_path,
                'size_bytes': 0,
                'size_str': 'N/A',
                'display_name': 'Error processing file',
                'error': str(e)
            }

    @staticmethod
    @staticmethod
    def process_files(file_paths):
        """
        Process multiple files and collect their information
        
        Args:
            file_paths: List of file paths to process
            
        Returns:
            Dictionary mapping file paths to their processed information
        """
        results = {}
        for file_path in file_paths:
            try:
                results[file_path] = FileDisplayHelper.get_file_info(file_path)
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                results[file_path] = {
                    'path': file_path,
                    'error': str(e),
                    'size_bytes': 0,
                    'size_str': 'N/A',
                    'bank_type': None,
                    'format_type': None,
                    'handler': None
                }
        return results

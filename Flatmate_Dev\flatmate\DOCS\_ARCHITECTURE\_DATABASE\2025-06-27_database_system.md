# Database System Architecture

## Overview

The Flatmate application uses a repository pattern to abstract database operations, providing a clean separation between business logic and data access. The system is designed with maintainability, extensibility, and clean architectural boundaries in mind.

## Key Components

### 1. Transaction Repository Interface

The `TransactionRepository` abstract class defines the interface for all database operations:

- Provides methods for CRUD operations on transactions
- Contains the `Transaction` class that represents the data model
- Includes an `ImportResult` class to track batch import results

This interface ensures that different database implementations can be swapped without affecting the rest of the application.

### 2. SQLite Implementation

The `SQLiteTransactionRepository` class implements the repository interface using SQLite:

- Handles database creation, connection management, and query execution
- Creates tables and indexes based on the `StandardColumns` enum
- Provides efficient batch operations for importing transactions
- Implements duplicate detection to prevent redundant data

### 3. Data Model

The `Transaction` class represents a financial transaction with attributes like:

- Date
- Details
- Amount
- Account
- Balance
- Various metadata fields

### 4. Standard Column Definitions

The `StandardColumns` enum (previously `FmColumnFormat`) provides consistent column naming across the application:

- Defines standard display names for transaction fields
- Provides methods to convert between display names and database column names
- Serves as the single source of truth for column definitions

## Data Flow

1. **Import Process**:
   - CSV files are processed by statement handlers specific to each bank
   - Data is normalized to the standard format defined by `StandardColumns`
   - Transactions are added to the database with duplicate detection

2. **Query Process**:
   - Application code uses the repository interface to query transactions
   - Filters can be applied to retrieve specific transactions
   - Results are returned as `Transaction` objects

3. **Export Process**:
   - Transactions can be exported to CSV using the CLI tool
   - Column names in the export match the standard display names

## Design Principles

1. **Clean Architectural Boundaries**:
   - The repository pattern creates a clear boundary between data access and business logic
   - All database operations go through the repository interface

2. **Single Source of Truth**:
   - The `StandardColumns` enum serves as the single source of truth for column definitions
   - Database schema is derived from this enum, ensuring consistency

3. **Extensibility**:
   - New database implementations can be added by implementing the repository interface
   - Additional fields can be added to the `StandardColumns` enum and will automatically be included in the database schema

4. **Maintainability**:
   - Consistent naming conventions make the code easier to understand
   - Clear separation of concerns makes it easier to modify specific parts of the system

## Recent Changes

The system recently underwent a standardization process:

1. Renamed `FmColumnFormat` to `StandardColumns` for clarity
2. Moved the column definitions to `fm.core.data_services.standards.fm_standard_columns` to make them accessible throughout the application
3. Updated all imports to reference the new location
4. Removed unused functionality related to extracting fields from notes

These changes have improved code clarity and maintainability while ensuring consistent column naming across the application.

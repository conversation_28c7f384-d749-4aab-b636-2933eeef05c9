"""Base class for bank statement type maps."""

import os.path
import re
from abc import ABC
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime

from typing import List, Optional,Tuple, Generator, Type, ClassVar

import pandas as pd

from fm.core.data_services.standards.fm_standard_columns import StandardColumns
 
from fm.core.services.logger import Logger
from fm.core.data_services.standards.fm_standard_columns import StandardColumns

@dataclass
class StatementType:
    """Configuration for statement format identification."""
    bank_name: str  # e.g., "Kiwibank"
    variant: str    # e.g., "basic"
    file_type: str  # e.g., "csv"
    
    def validate(self):
        if not self.bank_name or not isinstance(self.bank_name, str):
            raise ValueError("bank_name must be a non-empty string")
        if not self.variant or not isinstance(self.variant, str):
            raise ValueError("variant must be a non-empty string")
        if not self.file_type or not isinstance(self.file_type, str):
            raise ValueError("file_type must be a non-empty string")


@dataclass
class ColumnAttributes:
    """Configuration for column mapping and formatting."""
    # Required fields
    has_col_names: bool
    colnames_in_header: bool
    n_source_cols: int
    date_format: str
    data_start_row: int = 0

    col_names_row: int = 0
    has_account_column: bool = False
    
    source_col_names: List[str] = field(default_factory=list)
    target_col_names: List['StandardColumns'] = field(default_factory=list)
    concat_cols_for_details: Optional[List['StandardColumns']] = None
    file_encoding: str = 'utf-8'
    
    def validate(self):
        """Validate column attributes."""
        if not self.date_format or not isinstance(self.date_format, str):
            raise ValueError("date_format must be a non-empty string")
        if not isinstance(self.n_source_cols, int) or self.n_source_cols <= 0:
            raise ValueError("n_source_cols must be a positive integer")
        if not isinstance(self.data_start_row, int) or self.data_start_row < 0:
            raise ValueError("data_start_row must be a non-negative integer")
        if not isinstance(self.col_names_row, int) or self.col_names_row < 0:
            raise ValueError("col_names_row must be a non-negative integer")
        if not isinstance(self.colnames_in_header, bool):
            raise ValueError("colnames_in_header must be a boolean")
        if not isinstance(self.has_col_names, bool):
            raise ValueError("has_col_names must be a boolean")
        if self.has_col_names and not self.source_col_names:
            raise ValueError("source_col_names must be provided when has_col_names is True")
        if self.source_col_names and len(self.source_col_names) != self.n_source_cols:
            raise ValueError(f"source_col_names length ({len(self.source_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if self.target_col_names and len(self.target_col_names) != self.n_source_cols:
            raise ValueError(f"target_col_names length ({len(self.target_col_names)}) must match n_source_cols ({self.n_source_cols})")
        if not isinstance(self.file_encoding, str) or not self.file_encoding:
            raise ValueError("file_encoding must be a non-empty string")
            


@dataclass
class AccountNumberAttributes:
    """Configuration for account number extraction."""
    pattern: str = ""
    in_data: bool = False
    location: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    in_file_name: bool = False
    in_metadata: bool = False
    
    def validate(self):
        # Check for at least one location flag set
        location_flags = [self.in_data, self.in_file_name, self.in_metadata]
        if not any(location_flags):
            raise ValueError("At least one account location (in_data, in_file_name, in_metadata) must be True")
            
        if any(location_flags) and not self.pattern:
            raise ValueError("pattern is required when any account location is specified")
            
        # Validate location coordinates
        if not isinstance(self.location, tuple) or len(self.location) != 2:
            raise ValueError("location must be a tuple of two integers")
        row, col = self.location
        if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
            raise ValueError("location coordinates must be non-negative integers")


@dataclass
class SourceMetadataAttributes:
    """Configuration for source metadata handling."""
    has_metadata_rows: bool = False
    metadata_start: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    metadata_end: Tuple[int, int] = field(default_factory=lambda: (0, 0))
    
    def validate(self):
        if not isinstance(self.has_metadata_rows, bool):
            raise ValueError("has_metadata_rows must be a boolean")
            
        # Validate coordinates
        for name, coord in [("metadata_start", self.metadata_start), 
                          ("metadata_end", self.metadata_end)]:
            if not isinstance(coord, tuple) or len(coord) != 2:
                raise ValueError(f"{name} must be a tuple of two integers")
            row, col = coord
            if not isinstance(row, int) or not isinstance(col, int) or row < 0 or col < 0:
                raise ValueError(f"{name} coordinates must be non-negative integers")
        
        if self.has_metadata_rows and self.metadata_start > self.metadata_end:
            raise ValueError("metadata_start must not be after metadata_end")

@dataclass
class StatementAttributes:
    """Unified configuration for statement handling.
    
    Groups all statement-related attributes into a single class for better organization
    and enforces validation rules across all attributes.
    """
    statement_type_attrs: [StatementType] = None
    columns_attrs: [ColumnAttributes] = None
    account_attrs: [AccountNumberAttributes] = None
    metadata_attrs: [SourceMetadataAttributes] = None
    
    
    def validate(self):
        """Validate all statement attributes and their relationships."""
        # Validate all components
        self.statement_type_attrs.validate()
        self.columns_attrs.validate()
        self.account_attrs.validate()
        self.metadata_attrs.validate()        
    


class StatementHandler(ABC):
    """Base class for all statement handlers.
    
    Subclasses must define:
    - statement_type: StatementType
    - columns: ColumnAttributes
    - account: AccountNumberAttributes
    - metadata: SourceMetadataAttributes
    
    These will be automatically validated on initialization.
    """
    
    def __init__(self):
        """Initializes the handler and validates its configuration."""
        self.attributes = StatementAttributes(
            statement_type_attrs=self.statement_type,
            columns_attrs=self.columns,
            account_attrs=self.account,
            metadata_attrs=self.metadata,
        )
        self.attributes.validate()
        Logger.debug(f"Initialized {self.__class__.__name__}", module=self.__class__.__name__)

    # Type hints for statement attributes
    StatementType: ClassVar[Type[StatementType]] = StatementType
    
    # Instance attributes
    attributes: StatementAttributes
    
    # # Backwards compatibility properties
    # @property
    # def column_attrs(self):
    #     return self.columns_attrs
    
    # @property
    # def account_num_attrs(self):
    #     return self.account_attrs
    
    # @property
    # def source_metadata_attrs(self):
    #     return self.metadata_attrs

    # --- Core Internal Helpers ---

    def _read_csv(self, filepath: str, nrows: Optional[int] = None) -> Optional[pd.DataFrame]:
        """Centralized method to read CSV files using handler-specific configuration."""
        try:
            col_attrs = self.columns
            # Use colnames_in_header to decide if the file has a header row.
            # If True, use the specified row index. If False, read without a header.
            header_arg = col_attrs.col_names_row if col_attrs.colnames_in_header else None

            df = pd.read_csv(
                filepath,
                header=header_arg,
                nrows=nrows,
                on_bad_lines='skip',
                engine='python',
                encoding=col_attrs.file_encoding,
                sep=','
            )
            return df
        except (FileNotFoundError, pd.errors.EmptyDataError) as e:
            Logger.warning(f"File is empty, not found, or unreadable: {filepath} ({e})")
            return None
        except Exception as e:
            Logger.error(f"Unexpected error reading {filepath}: {e}")
            return None

    def _read_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads the full statement file into a DataFrame using the centralized _read_csv method."""
        return self._read_csv(filepath, nrows=None)

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract account number using handler configuration."""
        attrs = self.account

        # Method 1: From metadata
        if attrs.in_metadata:
            try:
                row, col = attrs.location
                account_line = str(df.iloc[row, col])
                if attrs.pattern:
                    match = re.search(attrs.pattern, account_line)
                    if match:
                        return match.group(1) if match.groups() else match.group(0)
                else: # No pattern, return the whole cell content
                    return account_line.strip()
            except (IndexError, AttributeError, KeyError) as e:
                Logger.warning(f"Could not find account number in metadata: {e}")

        # Method 2: From data column
        elif attrs.in_data:
            try:
                col_name = attrs.location[1]
                if col_name in df.columns:
                    # Find first non-null value that matches pattern
                    for item in df[col_name].dropna():
                        item_str = str(item)
                        if attrs.pattern:
                            match = re.search(attrs.pattern, item_str)
                            if match:
                                return match.group(1) if match.groups() else match.group(0)
            except (IndexError, KeyError) as e:
                Logger.warning(f"Could not find account number in data column '{attrs.location[1]}': {e}")

        # Method 3: From filename
        elif attrs.in_file_name:
            try:
                from pathlib import Path
                if hasattr(self, '_current_filepath'):
                    filename = Path(self._current_filepath).stem
                    if attrs.pattern:
                        match = re.search(attrs.pattern, filename)
                        if match:
                            return match.group(1) if match.groups() else match.group(0)
                    else: # No pattern, return the whole filename
                        return filename
            except Exception as e:
                Logger.warning(f"Could not extract account number from filename: {e}")
        
        return ""

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardizes DataFrame columns based on handler configuration.

        If the source file has a header (`colnames_in_header` is True), it renames
        the columns based on the `source_col_names` to `target_col_names` mapping.

        If the file is headless, it assigns the `target_col_names` directly.
        """
        target_names = [col.value for col in self.columns.target_col_names]

        if self.columns.colnames_in_header:
            # For files with headers, rename based on source->target mapping.
            df.columns = [str(c).strip() for c in df.columns]
            rename_map = dict(zip(self.columns.source_col_names, target_names))
            df.rename(columns=rename_map, inplace=True)
        else:
            # For headless files, assign target names directly, ensuring column counts match.
            num_target_cols = len(target_names)
            df = df.iloc[:, :num_target_cols]  # Ensure we don't have extra columns
            df.columns = target_names
        return df

    def _standardize_dates(self, df: pd.DataFrame, date_format: Optional[str] = None) -> None:
        """Standardize dates in the DataFrame to ISO format."""
        handler_name = self.__class__.__name__
        date_col = StandardColumns.DATE.value
        date_format = self.columns.date_format
        
        if not date_format:
            raise ValueError(f"{handler_name} must specify date_format in ColumnAttributes")

        if date_col not in df.columns:
            Logger.debug(f"[{handler_name}] No date column found for standardization")
            return

        # Store original dates to identify failures
        original_dates = df[date_col].copy()
        
        # Convert to datetime, coercing errors to NaT (Not a Time)
        df[date_col] = pd.to_datetime(original_dates, format=date_format, errors='coerce')

        # Find rows where conversion failed (was not null before, but is NaT now)
        failed_mask = df[date_col].isna() & original_dates.notna()
        
        if failed_mask.any():
            failed_count = failed_mask.sum()
            Logger.warning(
                f"[{handler_name}] {failed_count} date(s) could not be converted using format '{date_format}'."
            )
            # Log the first 5 problematic values for debugging
            for index, value in original_dates[failed_mask].head(5).items():
                Logger.warning(f"  - Row {index}: Found value '{value}'")

    def _create_details_column(self, df: pd.DataFrame, columns: list['StandardColumns']) -> None:
        """Create a details column by concatenating specified standard columns."""
        # Convert enums to their string values for DataFrame operations
        col_names = [col.value for col in columns]
        existing_cols = [col for col in col_names if col in df.columns]

        if not existing_cols:
            Logger.warning("No valid columns provided for details creation")
            return

        df[StandardColumns.DETAILS.value] = df[existing_cols].apply(
            lambda x: ' '.join(str(val) for val in x if pd.notna(val) and str(val).strip()),
            axis=1
        )
        Logger.info(f"Created details column from: {', '.join(existing_cols)}")

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns based on StandardColumns enum order, if they exist"""
        standard_order = [fmt.value for fmt in StandardColumns]
        ordered_cols = [col for col in standard_order if col in df.columns]
        remaining_cols = [col for col in df.columns if col not in ordered_cols]
        return df.reindex(columns=ordered_cols + remaining_cols)

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Hook for handlers to perform bank-specific formatting."""
        return df

    # --- Internal Orchestration ---

    def _format_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardizes the format of the input DataFrame based on handler configuration."""
        account_number = self._extract_account_number(df)

        # --- 1. Slice DataFrame to exclude metadata/header rows ---
        data_start_row = 0
        if not self.columns.colnames_in_header:
            # Slice off any metadata rows from the top of the file so that the
            # first row of the DataFrame is either the header or the first data row.
            if self.columns.data_start_row > 0:
                df = df.iloc[self.columns.data_start_row :].reset_index(drop=True)

        # --- 3. Standardize column headers --- 
        # This is the core unification step. After this, all DataFrames, regardless
        # of their original format, have the same standardized column headers.
        df = self._standardize_columns(df)

        # --- Clean the DataFrame ---
        df.dropna(how='all', inplace=True) # Drop fully empty rows

        # 2. Drop any remaining header row by checking for a non-date in the date column.
        # This is safer than slicing by index.
        date_col = StandardColumns.DATE.value
        if date_col in df.columns:
            # Temporarily convert the date column to datetime, coercing errors
            original_dates = pd.to_datetime(df[date_col], format=self.columns.date_format, errors='coerce')
            # Filter out rows where the original date was not a valid date (i.e., the header)
            df = df[original_dates.notna()].reset_index(drop=True)


        df[StandardColumns.SOURCE_FILENAME.value] = os.path.basename(self._current_filepath)

        if not self.columns.has_account_column:
            df[StandardColumns.ACCOUNT.value] = account_number if account_number else ''

        self._standardize_dates(df, date_format=self.columns.date_format)
        df = self._custom_format(df)

        if StandardColumns.EMPTY_COLUMN.value in df.columns:
            df = df.drop(columns=[StandardColumns.EMPTY_COLUMN.value])

        # If the handler has explicitly configured columns for concatenation,
        # run the creation logic using that specific list.
        if self.columns.concat_cols_for_details:
            self._create_details_column(df, self.columns.concat_cols_for_details)

        df = self._reorder_columns(df)
        self._standardize_amount_column(df)

        return df

    def _standardize_amount_column(self, df: pd.DataFrame) -> None:
        """Standardize the 'Amount' column to a numeric type."""
        amount_col = StandardColumns.AMOUNT.value
        if amount_col in df.columns:
            # Ensure the column is of string type before trying string operations
            if df[amount_col].dtype == 'object':
                # Remove currency symbols, commas, and whitespace
                df[amount_col] = df[amount_col].str.replace(r'[$,]', '', regex=True).str.strip()
            
            # Convert to numeric, coercing errors to NaN (which can be caught in validation)
            df[amount_col] = pd.to_numeric(df[amount_col], errors='coerce')

# --- Public API ---

    @classmethod
    def can_handle_file(
        cls,
        filepath: str,
        *,
        require_filename_match: bool = False,
        require_columns: bool = True,
        require_account_number: bool = True,
    ) -> bool:
        """Check if the handler can process the given file based on its configuration."""
        try:
            col_attrs = cls.columns
            acc_attrs = cls.account

            # --- Column Header Validation ---
            if require_columns and col_attrs.has_col_names and col_attrs.source_col_names:
                try:
                    expected_headers = {str(h).strip() for h in col_attrs.source_col_names}
                    
                    if col_attrs.colnames_in_header:
                        # Scenario 1: Header is in the file, read by pandas
                        df_preview = pd.read_csv(filepath, encoding=col_attrs.file_encoding, header=col_attrs.col_names_row, nrows=5)
                        actual_headers = {str(h).strip() for h in df_preview.columns}
                    else:
                        # Scenario 2: Headless file, names are in a data row
                        df_preview = pd.read_csv(filepath, encoding=col_attrs.file_encoding, header=None, nrows=col_attrs.col_names_row + 5)
                        if len(df_preview) <= col_attrs.col_names_row:
                            return False # Not enough rows to check for headers
                        actual_headers = {str(h).strip() for h in df_preview.iloc[col_attrs.col_names_row]}

                    if not expected_headers.issubset(actual_headers):
                        return False
                except Exception as e:
                    Logger.debug(f"[{cls.__name__}] Failed column check for {filepath}: {e}")
                    return False

            # --- Account Number Validation ---
            if require_account_number and acc_attrs.pattern: 
                # todo wtf is this?
                # This check requires reading the file again, let's keep it simple for now
                # and assume the main process_file will handle it. A full implementation
                # would require reading a preview and checking metadata/data/filename here.
                pass

            return True

        except (FileNotFoundError, pd.errors.EmptyDataError):
            return False
        except Exception as e:
            Logger.error(f"[{cls.__name__}] Unexpected error in can_handle_file for {filepath}: {e}", exc_info=True)
            return False

    def process_file(self, filepath: str) -> Optional[pd.DataFrame]:
        """Reads, formats, and validates a statement file."""
        self._current_filepath = filepath
        try:
            df = self._read_file(filepath)
            if df is None or df.empty:
                Logger.warning(f"File is empty or could not be read: {filepath}", module=self.__class__.__name__)
                return None
                
            formatted_df = self._format_df(df)
            return formatted_df
        except Exception as e:
            context = f"processing file {os.path.basename(filepath)}"
            Logger.error(
                f"Error in {self.__class__.__name__} while {context}: {e}",
                exc_info=True
            )
            raise
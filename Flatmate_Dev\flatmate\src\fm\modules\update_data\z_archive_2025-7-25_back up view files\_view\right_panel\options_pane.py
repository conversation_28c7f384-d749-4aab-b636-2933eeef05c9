"""
Options pane for the Update Data module.
Provides settings and configuration options.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QCheckBox, QComboBox, QFormLayout, QVBoxLayout

from fm.gui._shared_components.base.base_pane import BasePane

#this is a claytons module, it's not implemented..afaik an ai artefact...
#will leave for now... uses a QT form technique. I preffer check boxes on the left, descriptions on the right fwiw...

class OptionsPane(BasePane):
    """Pane for displaying and managing options and settings."""
    
    # Signals for publishing events to subscribers
    publish_setting_changed = Signal(str, object)  # Setting name, new value
    
    def __init__(self, parent=None):
        """Initialize the options pane."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Form layout for settings
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        
        # Auto-save option
        self.auto_save = QCheckBox()
        self.auto_save.setChecked(True)
        form_layout.addRow("Auto-save:", self.auto_save)
        
        # Display mode
        self.display_mode = QComboBox()
        self.display_mode.addItems(["Compact", "Detailed", "Custom"])
        form_layout.addRow("Display mode:", self.display_mode)
        
        # Date format
        self.date_format = QComboBox()
        self.date_format.addItems(["DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"])
        form_layout.addRow("Date format:", self.date_format)
        
        main_layout.addLayout(form_layout)
        main_layout.addStretch(1)
    
    def _connect_signals(self):
        """Connect signals to handlers."""
        # Subscribe to widget signals
        self.auto_save.stateChanged.connect(
            lambda state: self.publish_setting_changed.emit("auto_save", bool(state))
        )
        
        self.display_mode.currentTextChanged.connect(
            lambda text: self.publish_setting_changed.emit("display_mode", text)
        )
        
        self.date_format.currentTextChanged.connect(
            lambda text: self.publish_setting_changed.emit("date_format", text)
        )
    
    def get_settings(self):
        """Get the current settings as a dictionary."""
        return {
            "auto_save": self.auto_save.isChecked(),
            "display_mode": self.display_mode.currentText(),
            "date_format": self.date_format.currentText()
        }

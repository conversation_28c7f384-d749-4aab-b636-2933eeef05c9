# User Test Notes - Mock GUI Prototype (23/07/25)

This document collates direct feedback from the user during the interactive prototyping session.

---

### Test Feedback 1

- **State Label:** The state label was observed to be empty on initial run (e.g., `state:     `).
- **UI Text:** Questioned if the label `1. Source Files` should be more descriptive, like `File Source:`.
- **Dropdown Default:** Debated whether the source dropdown should default to `-- Select --` or a more common option like `Folder`.
- **Button State:** Discussed whether inactive buttons should be disabled or hidden entirely.
- **Progressive Disclosure:** Raised the need to apply progressive disclosure principles to guide the user.
- **Architecture Suggestion:** Proposed using a YAML file or a state table to manage UI configurations, making them easier to modify.

### Test Feedback 2

- **State Table Visibility:** Noted that the state table and view model were not visible as separate files in the mockup (clarified they are within `run_prototype.py` for simplicity).
- **State Label Bug (re-stated):** Confirmed the state label had no value on startup.
- **Info Pane Position:** Observed that the info pane was located at the very bottom of the layout, feeling disconnected.
- **Feature Request:** Suggested adding a `Reset` button to the left panel to return the prototype to its initial state for easier testing.

### Test Feedback 3 (Critical Sync Issue)

- **State Label Bug (Persistent):** The state label was still not showing a value.
- **Missing Reset Button:** The `Reset` button, which was implemented in the code, was not visible in the UI.
- **Unclear Button State:** It was difficult to determine the state of the `Process` button.

**Conclusion:** This final round of feedback revealed a critical synchronization failure. The code changes implemented were not being reflected in the user's running instance of the prototype.


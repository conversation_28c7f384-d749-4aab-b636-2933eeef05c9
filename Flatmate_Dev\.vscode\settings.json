{
  "files.exclude": {
    "flatmate/__pycache__": true,
    "flatmate/.pytest_cache": true,
    "**/__pycache__": true,
    "flatmate/src/fm/modules/update_data/utils/statement_handlers/__init__.py": false
  },
  "files.autoSave": "afterDelay",
  "python.defaultInterpreterPath": "${workspaceFolder}/flatmate/.venv_fm313/Scripts/python.exe",
  
  // Terminal settings
  "terminal.integrated.cwd": "${workspaceFolder}/flatmate",
  // "terminal.integrated.profiles.windows": {
  //   "Git Bash": {
  //     "path": "C:\\Program Files\\Git\\bin\\bash.exe",
  //     "args": ["--init-file", "${workspaceFolder}/terminal-startup.sh"]
  //   }
  // },
  // "terminal.integrated.defaultProfile.windows": "Git Bash",
  
  // File watcher exclusions
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/.venv_fm313/**": true
  },
  "accessibility.verbosity.terminal": false
}
# Refactoring Plan: DBIOService and Caching Architecture

**Objective:** Refactor the `DBIOService` and caching mechanism to align with the architectural recommendations outlined in the `2025-07-16_architectural_analysis_dbio_dependency_injection.md` report. The goal is to simplify the architecture, improve maintainability, and resolve critical issues like circular dependencies and timing problems.

---

## Phase 1: Convert `DBIOService` to a Singleton

**Goal:** Eliminate dependency injection for `DBIOService` and ensure a single, globally accessible instance.

1.  **Implement Singleton Pattern:**
    *   Modify the `DBIOService` class to include a `get_instance()` class method.
    *   Make the `__init__` method private or ensure it prevents re-initialization.
    *   Reference existing singletons like `InfoBarService` or `ConfigurationManager` for the established pattern.

2.  **Update Module Constructors:**
    *   Search the codebase for all classes that accept `db_io_service` as a constructor parameter.
    *   Remove the parameter from the `__init__` method signature.
    *   Update the internal logic of these classes to retrieve the service using `DBIOService.get_instance()` where needed, rather than storing `self.db_io_service`.

3.  **Update `main.py`:**
    *   Remove the manual instantiation of `DBIOService`.
    *   The service will now be instantiated on its first use via `get_instance()`.

## Phase 2: Refactor Caching to the Repository Layer

**Goal:** Make caching a transparent feature of the repository, not a separate, visible service. This decouples the cache logic from the business logic in `DBIOService`.

1.  **Create `CachedSQLiteRepository`:**
    *   Create a new class `CachedSQLiteRepository` that implements the `ITransactionRepository` interface.
    *   This class will act as a decorator or proxy for the actual `SQLiteTransactionRepository`.

2.  **Move Cache Logic:**
    *   The `CachedSQLiteRepository` will contain an instance of `SQLiteTransactionRepository` (for database access) and an instance of `InMemoryCache` (or a similar caching mechanism).
    *   Move all logic from the current `DBCachingService` into this new repository class. The cache will be completely internal to the repository.

3.  **Implement Transparent Caching:**
    *   Methods like `get_transactions()` in `CachedSQLiteRepository` will first check the cache.
    *   If the data is in the cache, return it.
    *   If there is a cache miss, the method will query the underlying `SQLiteTransactionRepository`, populate the cache, and then return the data.

4.  **Deprecate `DBCachingService`:**
    *   Once all logic is moved, the `DBCachingService` class can be safely removed.

## Phase 3: Integrate and Finalize

**Goal:** Connect the new components and ensure the system is stable.

1.  **Update `DBIOService` Singleton:**
    *   In the `DBIOService.__init__` method, change the repository instantiation from `self.repo = SQLiteTransactionRepository()` to `self.repo = CachedSQLiteRepository()`.
    *   Since the caching is now transparent, remove all direct cache-related method calls (e.g., `initialize_cache`, `get_cache_info`) from `DBIOService`.

2.  **Address Asynchronous Initialization:**
    *   Implement an `async` initialization method within `CachedSQLiteRepository` (e.g., `_ensure_cache_ready()`).
    *   This method will be responsible for loading the initial data from the database into the cache.
    *   Ensure that any data-access methods `await` this readiness check to prevent timing issues and "cache miss" warnings on startup.

3.  **Testing and Validation:**
    *   Run all existing tests to ensure no regressions were introduced.
    *   Specifically test the application startup sequence to confirm the cache is initialized correctly and that data is available to UI components when they first load.
    *   Verify that performance is as expected and that the "cache miss" warnings are gone.

---

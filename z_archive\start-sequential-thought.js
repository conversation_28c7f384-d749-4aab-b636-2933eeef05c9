#!/usr/bin/env node

/**
 * Sequential Thought MCP Server Launcher
 * 
 * This script launches the Sequential Thought MCP server using child_process.
 * It's designed to be managed by PM2 on Windows systems.
 */

const { spawn } = require('child_process');
const path = require('path');

// Log function with timestamps
function log(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

log('Starting Sequential Thought MCP server...');

// Use shell: true to handle Windows command execution properly
const server = spawn('npx', ['@modelcontextprotocol/server-sequential-thinking'], {
  stdio: 'inherit',
  shell: true,
  windowsHide: false
});

server.on('error', (err) => {
  log(`Failed to start server: ${err.message}`);
  process.exit(1);
});

server.on('exit', (code, signal) => {
  if (code !== 0) {
    log(`Server process exited with code ${code} and signal ${signal}`);
    process.exit(code || 1);
  }
  log('Server process exited normally');
});

process.on('SIGINT', () => {
  log('Received SIGINT. Gracefully shutting down...');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  log('Received SIGTERM. Gracefully shutting down...');
  server.kill('SIGTERM');
});

log('Server process started. Waiting for server initialization...');

# Requirements: Auto-Import Folder (MVP)

## Functional Requirements

### File Handling
- On first run, create a default `flatmate_imports` folder in the user's Downloads directory.
- Monitor the configured import folder for new `.csv` files using a polling mechanism.
- Automatically process new CSVs using the existing `dw_director` pipeline.
- On success, move the processed file to an `archive` subfolder within the import directory.
- On failure, move the file to a `failed_imports` subfolder within the import directory.
>> this will be very rare much more likely to get an unrecognised import, for which we already have handling

### Configuration & UI
- Provide an option in application settings to enable/disable the feature.
- Allow the user to view and change the import folder path from settings.
- Log all import activities (success, failure, file movement) and make logs accessible to the user.
- The `update_data` view should reflect newly imported data.

## Non-Functional Requirements
- The file monitoring process must run in the background without blocking the UI.
- The feature must only read from the user-specified directory.

## Future Enhancements (out of scope for this phase)
** Support for additional file types (OFX, PDF)**
>> begin research - ofx parsing is likely easy enough, its standardised already.
>> pdf parsing will be more difficult and  is a higher priority 
as i see it 
>> along with the related issue of auto importing from email attachments 

** Use of OS-level file system events instead of polling.**

** Advanced UI features in `update_data` (e.g., import statistics, per-account summaries, manual re-import).**
>>functionality already exists

** Granular error recovery options.**
NOT A CONCERN the database can not import duplicate transactions

** Management of multiple import sources.**
NOT A CONCERN the database can not import duplicate transactions
email auto downloads will require specific implementation
part of an import_service in core ?
or in update data... 
---
*Optimised: 2025-07-20*


# Pressing open concerns:
configurability - tidy import folder by default 
option to create user configurable archive folder
check import folder if archived
migrate to user configured folder
I personally keep a statement downloads folder in my ACCOUNTS folder in ~/documents 
keep a record of folder name and location 
in user prefs (config.app_paths.user_prefs) 
this may require an import_director in update_data module 
open question about local configs vs core config local configs have access to core anyway

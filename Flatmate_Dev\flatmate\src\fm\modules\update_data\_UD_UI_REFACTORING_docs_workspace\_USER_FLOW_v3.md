# Update Data GUI - User Journey Flow v2 (PRD Draft)

## Overview
This document describes the user experience for updating financial data through the Update Data GUI, incorporating guide pane feedback and active/inactive state management.

## User Experience Flow
GUIDE PANE: "Select a source folder or files to begin."

### First Run Conditions: 
```py
is_first_run = True    
data_base_populated = False
monitor_folders_enabled = False
monitered_folders = []
```
*>> # out of scope note: consider a means of tracking monitered folders if they are moved ...*

GUIDE PANE: "Select a source folder or files to begin."
## LAYOUT: 
### LEFT PANEL:

**widget: select_source_group**
.label_text: "Source Files..."
.selected_option: source_options.SELECT_FOLDER
.options_list: [
    source_options.SELECT_FOLDER,
    source_options.SELECT_FILES,
    *source_folder_paths if source_folder_paths else []
]

**widget: select_archive_group**
.label_text: "Archive..."
.selected_option: source_options.SELECT_FOLDER
.select_button_state: source_folder_paths and source_folder_paths == selected_source_folder

>> # update data base chb_label.text = [ ] "Update data base."

**widget: action_button**
.action_button_state: inactive
text: "Process files"

### CENTER PANEL:

**widget: guide_pane**
.label_text: "Select a source folder or files to begin."
.state: inactive

**widget: file_display_section**
.placeholder_text: "No files selected."
.state: inactive
.actions_available: false

**widget: file_list_widget**
.visible: false
.items: []
.state: inactive

### 🎯 **Step 1: Source Selection**

#### **Option A: Folder Selection**
**User action:** Click "Select Folder" under Source Files
**System response:**
- **widget: folder_dialog** opens
- **widget: guide_pane** updates: "Found [X] CSV files ready for processing"
- **widget: file_display_section** becomes active
- **widget: file_list_widget** populates with discovered files
- **widget: select_archive_group** becomes active

#### **Option B: File Selection**  
**User action:** Click "Select Files" under Source Files
**System response:**
- **widget: file_dialog** opens for multi-file selection
- **widget: guide_pane** updates: "Selected [X] files for processing"
- **widget: file_display_section** becomes active
- **widget: file_list_widget** populates with selected files
- **widget: select_archive_group** becomes active

### 📁 **Step 2: Archive Location**

#### **Option A: Same as Source**
**User action:** **widget: archive_option** selects "Same as source"
**System response:**
- **widget: guide_pane** updates: "Files will be moved to 'Archive' subfolder in source location"
- **widget: destination_dialog** remains hidden

#### **Option B: Custom Location**
**User action:** **widget: archive_option** selects "Select folder..."
**System response:**
- **widget: destination_dialog** opens for folder selection
- **widget: guide_pane** updates: "Choose where to create 'Archive' folder for processed files"

### ⚡ **Step 3: Ready State**
**When both selections complete:**
- **widget: action_button** transitions from inactive to active state
- **widget: guide_pane** updates: "Ready to process [X] files"
- **widget: monitor_checkbox** appears: "Monitor this folder for new files [ ]"

### 🔄 **Processing State**
**User action:** Click active **widget: action_button**
**System response:**
- **widget: guide_pane** updates: "Processing file 3 of 15..."
- **widget: progress_bar** shows completion percentage
- **widget: cancel_button** remains available
- **widget: action_button** shows processing animation

### ✅ **Success State**
**Completion response:**
- **widget: action_button** changes to **View Results** (active state)
- **widget: guide_pane** updates: "Successfully processed [X] files"
- **widget: results_summary** appears in main display area

## State Management

### Visual States
- **Inactive**: Grayed out, disabled interaction
- **Active**: Standard appearance, enabled interaction  
- **Processing**: Animated state with progress indication

### Guide Pane Behavior
- **Contextual**: Always relevant to current user action
- **Progressive**: Updates as user makes choices
- **Concise**: One clear message at a time

## Error Handling

### Invalid Source
**guide_pane**: "No compatible files found in selected location"
**System response**: Returns to source selection

### Processing Errors  
**guide_pane**: "Error processing [filename]: [specific error]"
**System response**: Continues with remaining files, provides error log

## User Paths Summary

### **Quick Path** (60% of users)
1. Select folder → Same as source → Process → View results

### **Organized Path** (30% of users)  
1. Select folder → Custom destination → Process → View results

### **Selective Path** (10% of users)
1. Select specific files → Custom destination → Process → View results

## Technical Implementation Notes

### State Coordination
- Central `StateCoordinator` class manages UI state transitions
- Simple boolean logic: `source_configured AND destination_configured`
- Guide pane updates via direct method calls

### File Display
- Real-time file discovery and validation
- Visual indicators for file types and counts
- Responsive to source changes

### Monitoring Option
- Checkbox state persists across sessions
- Background file monitoring when enabled
-possible implementation of option for automatic processing of new files (future enhancement)

# Architecture Refactor Plan: Left Panel Organization

## Current State Analysis

### File Structure Issues
```
left_panel_components/
├── widgets/
│   └── widgets.py  # Contains only LeftPanelWidgets - unnecessary nesting
└── [other files]
```

**Problem**: The `widgets/` directory contains only one file (`widgets.py`) with the `LeftPanelWidgets` class, creating unnecessary nesting.

### Widget Organization Analysis

**Current LeftPanelWidgets contains**:
- `source_group`: `SelectOptionGroupVLayout` for source files
- `save_group`: `SelectOptionGroupVLayout` for archive/save options  
- `db_update_checkbox`: `LabeledCheckBox` for database updates
- `create_master_checkbox`: `LabeledCheckBox` (hidden)
- `process_btn`: `ActionButton` for processing
- `cancel_btn`: `ExitButton` for cancellation

**Correct Access Paths**:
- `self.left_panel.buttons_widget.source_group` - for source options
- `self.left_panel.buttons_widget.save_group` - for save/archive options  
- `self.left_panel.buttons_widget.db_update_checkbox` - for database checkbox
- `self.left_panel.buttons_widget.process_btn` - for process button
- `self.left_panel.buttons_widget.cancel_btn` - for cancel button

## Proposed Architecture Refactor

### 1. Flatten Directory Structure
```
left_panel_components/
├── left_panel_widgets.py          # Rename from widgets/widgets.py
├── source_option_group.py         # Extract source group
├── save_option_group.py           # Extract save group  
├── action_controls.py              # Process/cancel buttons
└── option_types.py                # Already exists - keep enums
```

### 2. Refactor LeftPanelWidgets into Specialized Components

**SourceOptionGroup** (`source_option_group.py`):
```python
class SourceOptionGroup(SelectOptionGroupVLayout):
    """Dedicated source file selection component."""
    def __init__(self):
        super().__init__(
            options=[e.value for e in SourceOptions],
            label_text="Source Files...",
            button_text="[SELECT]"
        )
```

**SaveOptionGroup** (`save_option_group.py`):
```python
class SaveOptionGroup(SelectOptionGroupVLayout):
    """Dedicated save/archive location component."""
    def __init__(self):
        super().__init__(
            options=[e.value for e in SaveOptions],
            label_text="Archive",
            button_text="[SELECT]"
        )
```

**ActionControls** (`action_controls.py`):
```python
class ActionControls(QWidget):
    """Process and cancel button controls."""
    def __init__(self):
        super().__init__()
        # Contains process_btn and cancel_btn
```

### 3. Update LeftPanelWidgets Structure

**New LeftPanelWidgets** (`left_panel_widgets.py`):
```python
class LeftPanelWidgets(QWidget):
    """Unified left panel controls container."""
    
    def __init__(self):
        super().__init__()
        self.source_option_group = SourceOptionGroup()
        self.save_option_group = SaveOptionGroup()
        self.db_update_checkbox = LabeledCheckBox("Update Database")
        self.create_master_checkbox = LabeledCheckBox("Create Master")
        self.action_controls = ActionControls()
```

### 4. Update Access Patterns in UpdateDataView

**Current (incorrect)**:
```python
# These don't exist
self.left_panel.buttons_widget.set_source_option(option)
self.left_panel.buttons_widget.get_update_database()
```

**Correct (after refactor)**:
```python
# Proper access to components
self.left_panel.widgets.source_option_group.set_selected_option(option)
self.left_panel.widgets.db_update_checkbox.is_checked()
```

## Migration Strategy

### Phase 1: Directory Restructure
1. Rename `widgets/widgets.py` to `left_panel_widgets.py`
2. Move file up one directory level
3. Update all import statements

### Phase 2: Component Extraction  
1. Create separate files for each component
2. Extract functionality from monolithic LeftPanelWidgets
3. Maintain backward compatibility during transition

### Phase 3: Update Access Patterns
1. Update UpdateDataView to use correct component access
2. Update LeftPanelManager to coordinate new components
3. Remove deprecated access methods

## Benefits

### Immediate
- **Clarity**: Clear separation of concerns
- **Maintainability**: Smaller, focused components
- **Testability**: Individual components can be tested

### Long-term
- **Extensibility**: Easy to add new option types
- **Reusability**: Components can be reused elsewhere
- **Consistency**: Standardized naming across codebase

## Risk Mitigation
- **Backward Compatibility**: Maintain old interface during transition
- **Incremental Changes**: Phase-by-phase implementation
- **Testing**: Comprehensive test coverage for each phase

## Recommendation
Proceed with Phase 1 immediately (directory restructure), then implement Phase 2 and 3 in subsequent sprints. The current architecture is functional but would benefit significantly from this refactor.

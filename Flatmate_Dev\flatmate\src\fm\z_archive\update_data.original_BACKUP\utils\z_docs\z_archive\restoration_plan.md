# Restoration and Refactoring Plan: `dw_director` & `dw_pipeline`

**Date:** 2025-07-10

## 1. Current State Analysis

This document outlines the plan to complete the refactoring of `dw_director.py` and `dw_pipeline.py`. The analysis is based on a review of the current code against the objectives in `report.md` and `refactoring_plan.md`.

### 1.1 `dw_director.py` Assessment

-   **Role**: Correctly serves as the primary orchestrator of the pipeline.
-   **Issue**: Contains file system operations that violate the principle of separation of concerns. Specifically, it handles the deletion of original source files after backup and the moving of unrecognized files. This logic should reside within the pipeline module.

### 1.2 `dw_pipeline.py` Assessment

-   **`merge_dataframes()`**: The current deduplication logic is too basic. It combines all potential key columns into one list. It does not follow the prioritized strategy from `report.md` (i.e., use `Unique Id` if present, otherwise fall back to a specific composite key).
-   **`validate_final_data()`**: This function is performing some data integrity checks that should be handled by a more specialized function.
-   **Missing Logic**: The `validate_data_integrity()` function, designed to catch tricky duplicates (e.g., same date, non-zero amount, duplicate balance/ID), is completely missing.

## 2. Proposed Refactoring Steps

To align the code with the intended architecture, the following changes are required. We will proceed step-by-step and confirm each change with you.

### Step 1: Consolidate File Operations in `dw_pipeline.py`

**Status: `merge_dataframes` logic is confirmed to be correct as-is.**

-   [ ] **Task 1.1: Consolidate File Cleanup Logic**
    -   **Objective**: Move the responsibility of deleting original files from `dw_director.py` into the `back_up_originals` function in `dw_pipeline.py`. The pipeline function should handle both backing up and then cleaning up the source files.
    -   **Reason**: This aligns with the architectural goal of making `dw_director.py` a pure orchestrator and centralizing all file system operations within `dw_pipeline.py`.

### Step 2: Update `dw_director.py`

-   [ ] **Task 2.1: Update Director to Use Consolidated Function**
    -   Modify `dw_director.py` to call the updated `back_up_originals` function.
    -   Remove the now-redundant file deletion logic from the director.

## 3. Next Steps

Once you approve this plan, I will begin with **Task 1.1**. I will present the proposed code changes for your review before applying them.

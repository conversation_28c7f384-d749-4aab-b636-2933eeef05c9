# Declarative Mode-Driven UI Architecture Pattern

**Date**: 2025-01-21
**Status**: ACTIVE_PATTERN
**First Implementation**: Auto Import Morphic UI

## 🎯 **Pattern Definition**

**Name**: Declarative Mode-Driven UI Architecture
**Category**: UI State Management Pattern
**Combines**: State Pattern + Configuration Pattern + Single Source of Truth Pattern

### **Core Principle**
Define what each UI mode should look like (declarative), not how to achieve it (imperative).

### **Problem Solved**
- Scattered UI state logic across multiple files
- Inconsistent state management between UI modes
- Hard-to-debug state transition bugs
- Manual synchronization between UI elements

## 🏗️ **Pattern Structure**

### **Components**

1. **Mode Enumeration**
   ```python
   class UIMode(str, Enum):
       MODE_A = "mode_a"
       MODE_B = "mode_b"
   ```

2. **Element State Model**
   ```python
   class UIElementState(BaseModel):
       visible: bool = True
       enabled: bool = True
       text: Optional[str] = None
       options: Optional[List[str]] = None
   ```

3. **Mode Configuration**
   ```python
   class ModeConfiguration(BaseModel):
       element_1: UIElementState
       element_2: UIElementState
       
       class Config:
           frozen = True  # Immutable
   ```

4. **Configuration Registry**
   ```python
   MODE_CONFIGS = {
       UIMode.MODE_A: ModeConfiguration(...),
       UIMode.MODE_B: ModeConfiguration(...)
   }
   ```

5. **State Applier**
   ```python
   class UIStateApplier:
       @staticmethod
       def apply_configuration(view, config: ModeConfiguration):
           # Apply config to actual UI elements
   ```

6. **Mode Determiner**
   ```python
   def determine_mode(context_vars) -> UIMode:
       # Logic to determine current mode
   ```

## 🎯 **Implementation Guidelines**

### **Step 1: Define Modes**
- Enumerate all possible UI modes
- Use descriptive, business-logic names
- Keep modes mutually exclusive

### **Step 2: Model Element States**
- Use Pydantic for type safety and validation
- Make configurations immutable (frozen=True)
- Include all UI properties that change between modes

### **Step 3: Create Configuration Registry**
- Define complete UI state for each mode
- Use declarative configuration (what, not how)
- Validate configurations at startup

### **Step 4: Implement State Applier**
- Handle different UI element types
- Preserve user state where appropriate
- Apply configurations atomically

### **Step 5: Integrate with Context Manager**
- Use mode determination logic
- Apply configurations through state applier
- Handle mode transitions smoothly

## ✅ **Benefits**

### **Immediate Benefits**
- ✅ **Eliminates State Bugs**: Single source of truth prevents inconsistencies
- ✅ **Type Safety**: Pydantic validation catches configuration errors
- ✅ **Immutable Configs**: Frozen models prevent accidental mutations
- ✅ **Declarative**: Define what modes look like, not how to achieve them

### **Long-term Benefits**
- ✅ **Maintainable**: Adding new modes is straightforward
- ✅ **Testable**: Easy to unit test mode configurations
- ✅ **Debuggable**: All mode logic in one place
- ✅ **Extensible**: Easy to add new UI elements or modes
- ✅ **Consistent**: Guaranteed consistent behavior across modes

## 🧪 **Testing Strategy**

### **Unit Tests**
```python
def test_mode_configuration_valid():
    config = get_mode_config(UIMode.MODE_A)
    assert config.element_1.visible == True
    
def test_mode_determination():
    mode = determine_mode(auto_import=True, database=True)
    assert mode == UIMode.DATABASE_AUTO_IMPORT
    
def test_state_application():
    applier = UIStateApplier()
    applier.apply_configuration(mock_view, test_config)
    assert mock_view.element.text == "Expected Text"
```

### **Integration Tests**
```python
def test_complete_mode_transition():
    # Test full mode transition workflow
    context_manager.configure_view_for_mode(view, UIMode.MODE_A)
    assert view.element_1.isVisible() == True
    
    context_manager.configure_view_for_mode(view, UIMode.MODE_B)
    assert view.element_1.isVisible() == False
```

## 🔄 **Related Patterns**

### **State Pattern**
- **Similarity**: Different behaviors for different states
- **Difference**: This pattern focuses on UI configuration, not behavior

### **Strategy Pattern**
- **Similarity**: Encapsulated algorithms (UI configurations)
- **Difference**: Configurations are data, not algorithms

### **Configuration Pattern**
- **Similarity**: External configuration drives behavior
- **Difference**: Configurations are type-safe and immutable

### **Observer Pattern**
- **Complementary**: Can notify observers of mode changes
- **Usage**: Trigger mode transitions based on external events

## 📋 **Implementation Checklist**

### **Design Phase**
- [ ] Identify all UI modes
- [ ] List all UI elements that change between modes
- [ ] Define mode determination logic
- [ ] Design configuration structure

### **Implementation Phase**
- [ ] Create mode enumeration
- [ ] Define element state models
- [ ] Create mode configurations
- [ ] Implement state applier
- [ ] Integrate with context manager

### **Testing Phase**
- [ ] Unit test mode configurations
- [ ] Unit test state application
- [ ] Integration test mode transitions
- [ ] Test edge cases and error conditions

### **Documentation Phase**
- [ ] Document mode definitions
- [ ] Document configuration structure
- [ ] Document usage examples
- [ ] Document testing strategy

## 🚀 **Usage Examples**

### **Basic Usage**
```python
# Determine current mode
mode = determine_mode(auto_import_enabled=True, database_mode=True)

# Get configuration for mode
config = get_mode_config(mode)

# Apply configuration to UI
UIStateApplier.apply_configuration(view, config)
```

### **Mode Transition**
```python
def handle_database_toggle(self, enabled: bool):
    # Determine new mode
    new_mode = determine_mode(
        auto_import_enabled=self.auto_import_status.enabled,
        database_mode=enabled
    )
    
    # Apply new configuration
    config = get_mode_config(new_mode)
    UIStateApplier.apply_configuration(self.view, config)
```

## 📊 **Success Metrics**

### **Code Quality**
- **Reduced Complexity**: Mode logic centralized in one place
- **Improved Testability**: Configurations can be unit tested
- **Better Maintainability**: Adding modes requires minimal code changes

### **Bug Prevention**
- **State Consistency**: Impossible to have inconsistent UI states
- **Type Safety**: Pydantic prevents invalid configurations
- **Immutability**: Configurations cannot be accidentally modified

### **Developer Experience**
- **Clear Intent**: Mode definitions are self-documenting
- **Easy Debugging**: All mode logic in one place
- **Predictable Behavior**: Declarative configurations are easy to reason about

---

**Pattern Status**: Ready for implementation
**First Use Case**: Auto Import Morphic UI
**Estimated Implementation**: 2 hours
**Risk Level**: LOW - Well-defined pattern with clear benefits

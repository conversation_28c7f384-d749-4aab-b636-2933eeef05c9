# Update Data Event-Driven Architecture Implementation Roadmap

**Date**: 2025-07-26 14:32:15  
**Version**: 1.0  
**Architect**: <PERSON> 🏗️  
**Status**: Planning Phase

---

## Feedback Integration Summary

Based on your excellent architectural observations:

### ✅ **Key Issues Identified**
1. **`option_types.py` in wrong location** - Should be in `view_components` not `utils`
2. **`utils` should be renamed to `pipeline`** - Better reflects its purpose
3. **`view_events.py` location question** - Should be in `_view_components` not `services`
4. **Auto-import speculation** - Don't create events until needed, focus on `NEW_FILES_DETECTED`
5. **Need bite-size implementation chunks** - Logical order, versioned guides

### 📋 **Terminology Clarification**
- **Phase**: Major architectural milestone (weeks)
- **Sprint**: Focused implementation cycle (3-5 days)  
- **Session**: Single work session (2-4 hours)
- **Task**: Individual actionable item (30-60 minutes)

---

## File Structure Rationalization Plan

### Current Structure Issues:
```
update_data/
├── utils/                          # ❌ Wrong name - should be 'pipeline'
│   ├── option_types.py            # ❌ View-specific, should be in view_components
│   ├── dw_director.py             # ✅ Correct - pipeline logic
│   └── file_utils.py              # ✅ Correct - pipeline utilities
├── services/
│   └── events.py                  # ✅ Keep - global events
└── _view_components/
    └── state_coordinator.py       # ✅ Correct location
```

### Proposed Structure:
```
update_data/
├── pipeline/                       # ✅ Renamed from 'utils'
│   ├── dw_director.py             # ✅ Pipeline orchestration
│   ├── file_utils.py              # ✅ File processing utilities
│   └── processing_tracker.py      # ✅ Processing logic
├── services/
│   ├── events.py                  # ✅ Global events (keep existing)
│   └── local_event_bus.py         # ✅ NEW - Local event coordination
├── _view_components/
│   ├── view_events.py             # ✅ NEW - View event definitions
│   ├── option_types.py            # ✅ MOVED - View-specific types
│   └── state_coordinator.py       # ✅ Enhanced with events
└── models/
    └── ui_modes.py                # ✅ Keep existing
```

---

## Implementation Phases

### 🎯 **Phase 1: Foundation Rationalization** (Sprint 1)
**Duration**: 1 session (2-3 hours)  
**Goal**: Clean up file structure and naming

#### Session 1.1: File Structure Cleanup
**Tasks**:
1. **Rename `utils` → `pipeline`** (15 min)
2. **Move `option_types.py` → `_view_components`** (15 min)
3. **Update all imports** (30 min)
4. **Create `local_event_bus.py` in `services`** (45 min)
5. **Create `view_events.py` in `_view_components`** (30 min)
6. **Test imports work** (15 min)

**Deliverable**: Clean file structure with no broken imports

---

### 🎯 **Phase 2: Event Architecture Foundation** (Sprint 2)
**Duration**: 2 sessions (4-6 hours)  
**Goal**: Implement local event system

#### Session 2.1: Local Event Bus Implementation
**Tasks**:
1. **Implement `LocalEventBus` class** (60 min)
2. **Define core `ViewEvents` enum** (30 min)
3. **Create `EventDataFactory`** (45 min)
4. **Add debugging/logging capabilities** (30 min)
5. **Write basic tests** (45 min)

#### Session 2.2: Event Integration Setup
**Tasks**:
1. **Integrate local bus with existing components** (60 min)
2. **Set up event bridging to global bus** (45 min)
3. **Update state coordinator to use events** (60 min)
4. **Test event flow** (30 min)

**Deliverable**: Working local event system with basic integration

---

### 🎯 **Phase 3: Component Decoupling** (Sprint 3)
**Duration**: 3 sessions (6-8 hours)  
**Goal**: Remove direct coupling between components

#### Session 3.1: State Coordinator Refactoring
**Tasks**:
1. **Remove direct view references from StateCoordinator** (45 min)
2. **Implement event-based state updates** (60 min)
3. **Add state validation and error handling** (45 min)
4. **Test state transitions via events** (30 min)

#### Session 3.2: Presenter Decoupling  
**Tasks**:
1. **Remove direct view manipulation from presenter** (90 min)
2. **Implement event-based presenter logic** (60 min)
3. **Add business logic validation** (30 min)
4. **Test presenter event emissions** (30 min)

#### Session 3.3: View Event Integration
**Tasks**:
1. **Refactor view to emit user action events** (60 min)
2. **Implement event-based UI updates** (60 min)
3. **Remove direct presenter calls** (45 min)
4. **Test complete event flow** (45 min)

**Deliverable**: Fully decoupled components communicating via events

---

## Task Prioritization Matrix

### 🔥 **Critical Path Tasks** (Must do first)
1. **File structure rationalization** - Blocks everything else
2. **Local event bus creation** - Foundation for decoupling
3. **State coordinator event integration** - Core state management

### ⚡ **High Priority Tasks** (Do next)
4. **Presenter decoupling** - Major architectural improvement
5. **View event integration** - Complete the event loop

### 📋 **Medium Priority Tasks** (Polish phase)
6. **Error handling enhancement** - Robustness
7. **Event debugging tools** - Developer experience
8. **Performance optimization** - If needed

---

## Session Planning Template

### Session Structure:
```
Session X.Y: [Session Name]
├── Duration: [X hours]
├── Prerequisites: [What must be done first]
├── Tasks: [Specific actionable items with time estimates]
├── Success Criteria: [How to know it's done]
├── Testing: [How to validate it works]
└── Deliverable: [What gets produced]
```

### Next Session Recommendation:
**Session 1.1: File Structure Cleanup**
- **Duration**: 2-3 hours
- **Prerequisites**: None (can start immediately)
- **Focus**: Foundation rationalization
- **Risk**: Low (mostly file moves and renames)

---

## Implementation Notes

### 🚨 **Critical Decisions Made**
1. **`view_events.py` location**: `_view_components` (not `services`) - closer to view logic
2. **No speculative auto-import events** - Only implement `NEW_FILES_DETECTED` when needed
3. **Bite-size tasks** - 15-90 minute chunks for manageable progress
4. **Test-driven approach** - Validate each step before proceeding

### 🎯 **Success Metrics**
- **No circular imports** - Clean dependency structure
- **Event-driven communication** - No direct component coupling  
- **Maintainable code** - Clear separation of concerns
- **Developer friendly** - Easy to understand and extend

### 📝 **Documentation Strategy**
- **Versioned implementation guides** - Each session gets a versioned guide
- **Timestamp everything** - Clear chronological progression
- **Decision rationale** - Why choices were made
- **Rollback plans** - How to undo if needed

---

## Ready to Proceed?

**Recommended Starting Point**: Session 1.1 (File Structure Cleanup)
- Low risk, high impact
- Unblocks all subsequent work
- Can be completed in one focused session
- Immediate visible progress

Would you like me to create the detailed implementation guide for Session 1.1?

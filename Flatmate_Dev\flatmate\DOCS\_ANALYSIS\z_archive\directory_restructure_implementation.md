# Directory Restructure Implementation Plan

## Current Structure Analysis

### Problem: Unnecessary Nesting
```
flatmate/src/fm/modules/update_data/_view_components/left_panel_components/
├── widgets/
│   └── widgets.py  # Contains only LeftPanelWidgets
└── [other files]
```

The `widgets/` directory contains only one file, creating unnecessary nesting and poor naming.

## Proposed Restructure

### Phase 1: Immediate Restructure
```
flatmate/src/fm/modules/update_data/_view_components/
├── left_panel_widgets.py          # Formerly widgets/widgets.py
├── left_panel_components/
│   ├── source_option_group.py     # Extract source group
│   ├── save_option_group.py       # Extract save group
│   ├── action_controls.py         # Extract action buttons
│   └── option_types.py            # Keep enums
└── [other existing files]
```

### Phase 2: Component Refactor

**File: `left_panel_widgets.py`** (replaces widgets/widgets.py)
```python
"""
Left Panel Widgets for Update Data Module.

Provides unified access to left panel UI components.
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout
from fm.gui._shared_components.widgets import HeadingLabel

from .left_panel_components.source_option_group import SourceOptionGroup
from .left_panel_components.save_option_group import SaveOptionGroup  
from .left_panel_components.action_controls import ActionControls


class LeftPanelWidgets(QWidget):
    """Container for left panel UI components."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        self.title = HeadingLabel("Actions")
        layout.addWidget(self.title)
        
        # Source files selection
        self.source_group = SourceOptionGroup()
        layout.addWidget(self.source_group)
        
        # Save/archive location
        self.save_group = SaveOptionGroup()
        layout.addWidget(self.save_group)
        
        # Action buttons
        self.action_controls = ActionControls()
        layout.addWidget(self.action_controls)
        
        layout.addStretch()
```

**File: `left_panel_components/source_option_group.py`**
```python
"""
Source file selection component for left panel.
"""

from PySide6.QtWidgets import QVBoxLayout, QWidget
from fm.gui._shared_components.widgets import SelectOptionGroupVLayout
from ..option_types import SourceOptions


class SourceOptionGroup(SelectOptionGroupVLayout):
    """Source file selection component."""
    
    def __init__(self):
        super().__init__(
            options=[e.value for e in SourceOptions],
            label_text="Source Files...",
            button_text="[SELECT]"
        )
```

**File: `left_panel_components/save_option_group.py`**
```python
"""
Save/archive location component for left panel.
"""

from PySide6.QtWidgets import QVBoxLayout, QWidget
from fm.gui._shared_components.widgets import SelectOptionGroupVLayout
from ..option_types import SaveOptions


class SaveOptionGroup(SelectOptionGroupVLayout):
    """Save/archive location component."""
    
    def __init__(self):
        super().__init__(
            options=[e.value for e in SaveOptions],
            label_text="Archive",
            button_text="[SELECT]"
        )
```

**File: `left_panel_components/action_controls.py`**
```python
"""
Action buttons component for left panel.
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout
from fm.gui._shared_components.widgets import ActionButton, ExitButton


class ActionControls(QWidget):
    """Process and cancel action buttons."""
    
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.process_btn = ActionButton("PROCESS FILES")
        self.process_btn.setEnabled(False)
        layout.addWidget(self.process_btn)
        
        self.cancel_btn = ExitButton("Cancel")
        layout.addWidget(self.cancel_btn)
```

## Migration Steps

### Step 1: Create New Files
1. Create `left_panel_widgets.py` in `_view_components/`
2. Create component files in `left_panel_components/`
3. Copy relevant code from existing `widgets.py`

### Step 2: Update Imports
```python
# Before:
from ._view_components.left_panel_components.widgets.widgets import LeftPanelWidgets

# After:  
from ._view_components.left_panel_widgets import LeftPanelWidgets
```

### Step 3: Update Access Patterns
```python
# Before (incorrect):
self.left_panel.buttons_widget.set_source_option(option)

# After (correct):
self.left_panel.widgets.source_group.set_selected_option(option)
```

## Verification Checklist
- [ ] Application starts without AttributeError
- [ ] All left panel controls function correctly
- [ ] Import statements updated throughout codebase
- [ ] No circular dependencies introduced
- [ ] Backward compatibility maintained during transition

## Benefits
- **Clarity**: Clear component separation
- **Maintainability**: Smaller, focused files
- **Testability**: Individual components can be tested
- **Scalability**: Easy to add new components
- **Naming**: Consistent with project conventions

# Update Data Module Configuration

## Overview
This directory contains the module-specific configuration wrapper for the Update Data module. It provides a clean interface to the core configuration system while allowing module-specific configuration needs.

## Structure
```
config/
├── __init__.py         # Exports ud_config
├── local_config.py     # Module config wrapper
├── defaults.toml       # Module-specific defaults
└── README.md          # This file
```

## How It Works

### Local Config Wrapper
The `local_config.py` file defines `UpdateDataConfig` which wraps the core config system:

```python
from ....core.config import config as core_config

class UpdateDataConfig:
    def __init__(self):
        self._core = core_config
```

This wrapper:
1. Provides module-specific access to core config
2. Can add module-specific config methods
3. Keeps module code isolated from core implementation details

### Usage
In module code:
```python
from .config import ud_config

# Get a config value
master_path = ud_config.get_path(UpdateDataKeys.Paths.MASTER)

# Set a config value
ud_config.set_value(UpdateDataKeys.History.RECENT_MASTERS, masters_list)
```

### Benefits
1. **Encapsulation**: Module code doesn't need to know about core config details
2. **Flexibility**: Can add module-specific config functionality
3. **Maintainability**: Changes to core config only need to be handled in wrapper
4. **Isolation**: Each module's config needs are separated

## Adding New Configuration
1. Add new keys to `UpdateDataKeys`
2. Add default values to `defaults.toml`
3. Add any needed wrapper methods to `UpdateDataConfig`

"""
Widget Configuration Package

Provides configuration system for shared widgets.
"""

from .widget_config import (
    BaseWidgetConfig,
    ButtonConfig,
    CheckBoxConfig,
    LabelConfig,
    OptionMenuConfig,
    SelectorConfig,
    FilterConfig
)
from .factory import ConfigFactory

__all__ = [
    'BaseWidgetConfig',
    'ButtonConfig',
    'CheckBoxConfig',
    'LabelConfig',
    'OptionMenuConfig',
    'SelectorConfig',
    'FilterConfig',
    'ConfigFactory'
]
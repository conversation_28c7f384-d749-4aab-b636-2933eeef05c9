"""
Utility for tracking metadata columns across the data processing pipeline.
"""

from typing import ClassVar, List


class MetadataColumnTracker:
    """
    Singleton class to track metadata columns across the pipeline.
    Ensures columns are stored, unique, and can be reset between jobs.
    """
    _columns: ClassVar[List[str]] = []

    @classmethod
    def add_columns(cls, new_cols: List[str]) -> None:
        """
        Add new columns, ensuring no duplicates.
        
        Args:
            new_cols: List of column names to add
        """
        cls._columns.extend(col for col in new_cols if col not in cls._columns)

    @classmethod
    def get_columns(cls) -> List[str]:
        """
        Retrieve the current list of metadata columns.
        
        Returns:
            List of unique metadata column names
        """
        return cls._columns.copy()

    @classmethod
    def clear(cls) -> None:
        """
        Clear all tracked metadata columns.
        Call this at the start of each job/processing cycle.
        """
        cls._columns.clear()

    @classmethod
    def set_columns(cls, columns: List[str]) -> None:
        """
        Directly set the list of metadata columns, replacing any existing columns.
        
        Args:
            columns: List of column names to set
        """
        cls._columns = list(dict.fromkeys(columns))  # Preserve order, remove duplicates

#!/usr/bin/env python3
"""
Pipeline functions for processing bank CSV files.
"""

# Standard library imports
import os
from typing import Any, Dict, List, Optional, Tuple, Union
import shutil
import datetime

import pandas as pd

# Core imports
from fm.core.data_services import DBIOService
from fm.core.data_services.standards.columns import Columns
from fm.core.database.sql_repository.transaction_repository import ImportResult
from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository
from fm.core.services.logger import log
# from ..config.ud_config import ud_config
# from ..config.ud_keys import UpdateDataKeys


def merge_dataframes(
    formatted_dfs: List[pd.DataFrame],
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Merge formatted DataFrames, ensuring consistent column structure, and handle duplicates.
    This function standardizes all incoming DataFrames to a master set of columns
    derived from the Columns registry, ensuring no data is lost during concatenation.
    """
    if not formatted_dfs or all(df.empty for df in formatted_dfs):
        return pd.DataFrame(), {"duplicates_removed": 0, "total_rows": 0}

    merged_df = pd.concat(formatted_dfs, axis=0, ignore_index=True)

    # Get all columns from the registry to ensure no data is lost
    all_cols = [str(c) for c in Columns.get('statement_handler')] # Use only statement_handler columns for merging
    for col in all_cols:
        if col not in merged_df.columns:
            merged_df[col] = None
    merged_df = merged_df[all_cols]

    merged_df.dropna(how='all', inplace=True)

    before_count = len(merged_df)

    # --- Deduplication ---
    potential_keys = [
        Columns.DATE,
        Columns.DETAILS,
        Columns.AMOUNT,
        Columns.BALANCE,
        Columns.UNIQUE_ID
    ]
    # Use Column objects for checking existence in the DataFrame columns
    dedup_subset = [key for key in potential_keys if key in merged_df.columns]

    if not dedup_subset:
        log.warning("Could not determine a key for deduplication. No duplicates removed.")
        deduplicated_df = merged_df
    else:
        # drop_duplicates requires a list of strings, not Column objects
        str_dedup_subset = [str(c) for c in dedup_subset]
        display_names = [c.display_name for c in dedup_subset]
        log.info(f"Deduplicating based on columns: {display_names}")
        deduplicated_df = merged_df.drop_duplicates(subset=str_dedup_subset, keep='first')

    duplicates_removed = before_count - len(deduplicated_df)
    log.info(f"Removed {duplicates_removed} duplicate transactions.")

    stats = {"duplicates_removed": duplicates_removed, "total_rows": len(deduplicated_df)}

    return deduplicated_df, stats


def _check_duplicate_unique_ids(df: pd.DataFrame) -> None:
    """Check for and log any duplicate unique IDs in the DataFrame.
    
    This is an internal function and should not be called directly.
    """
    if Columns.UNIQUE_ID not in df.columns:
        return
        
    # Find duplicates (excluding NaN values)
    dup_mask = df.duplicated(Columns.UNIQUE_ID, keep=False) & ~df[Columns.UNIQUE_ID].isna()
    duplicate_ids = df[dup_mask]
    
    if not duplicate_ids.empty:
        log.warning(f"Found {len(duplicate_ids)} duplicate unique IDs")
        for _, group in duplicate_ids.groupby(Columns.UNIQUE_ID):
            log.warning(f"Duplicate ID {group[Columns.UNIQUE_ID].iloc[0]} appears {len(group)} times")


def _find_suspicious_transactions(df: pd.DataFrame) -> pd.DataFrame:
    """
    Find transactions with obvious data integrity issues.

    This validation has been simplified to avoid false positives from:
    - Overlapping statement periods
    - Out-of-order transactions
    - Different starting balances between statements

    Currently disabled - returns empty DataFrame.
    Real data integrity should be handled at the import/parsing level.

    Args:
        df: DataFrame containing transaction data

    Returns:
        Empty DataFrame (validation disabled)
    """
    # Validation disabled due to too many false positives
    # Bank statements can have overlapping periods, out-of-order transactions,
    # and different starting balances that make sequential validation unreliable
    return pd.DataFrame()


def validate_final_data(df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    Perform final validation checks on the merged and deduplicated DataFrame.
    """
    is_valid = True
    errors = []

    # 1. Check for required columns
    required_columns = [Columns.DATE, Columns.AMOUNT]
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        error_msg = f"Missing required columns: {', '.join(map(str, missing_columns))}"
        errors.append(error_msg)
        log.error(error_msg)
        return False, errors

    # 2. Check for missing dates or amounts
    missing_dates = df[Columns.DATE].isna().sum()
    missing_amounts = df[Columns.AMOUNT].isna().sum()

    if missing_dates > 0:
        error_msg = f"Found {missing_dates} transactions with missing dates"
        errors.append(error_msg)
        log.error(error_msg)
        is_valid = False

    if missing_amounts > 0:
        error_msg = f"Found {missing_amounts} transactions with missing amounts"
        errors.append(error_msg)
        log.error(error_msg)
        is_valid = False

    # 3. Check for duplicate unique IDs
    _check_duplicate_unique_ids(df)
    
    # 4. Find suspicious transactions (validation currently disabled)
    suspicious_transactions = _find_suspicious_transactions(df)

    # Note: Balance validation is currently disabled due to false positives
    # from overlapping statement periods and out-of-order transactions
    if not suspicious_transactions.empty:
        log.warning(f"Found {len(suspicious_transactions)} transactions with data integrity issues")

        for idx, row in suspicious_transactions.iterrows():
            log.warning(
                f"Data integrity issue (row {idx+1}): Account {row.get(Columns.ACCOUNT, 'N/A')}, "
                f"Date {row.get(Columns.DATE, 'N/A')}, Amount {row.get(Columns.AMOUNT, 'N/A')}, "
                f"Balance {row.get(Columns.BALANCE, 'N/A')}"
            )

    return is_valid, errors


def save_master_file(df: pd.DataFrame, save_folder: str) -> Dict[str, Any]:
    """Save DataFrame to a timestamped CSV file in the specified folder.

    Args:
        df: DataFrame to save
        save_folder: Path to the folder where the file will be saved

    Returns:
        Dictionary with save results, including the final output path.
    """
    result = {"save_success": False, "error": None, "output_path": None}
    try:
        os.makedirs(save_folder, exist_ok=True)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"fmMaster_{timestamp}.csv"
        output_path = os.path.join(save_folder, filename)

        df_to_save = df.copy()

        # Rename columns to their display names for the final CSV output
        rename_map = {str(c): c.display_name for c in Columns.get('statement_handler') if str(c) in df_to_save.columns}
        df_to_save.rename(columns=rename_map, inplace=True)

        # Format date column if it exists (using the display name now)
        date_display_name = Columns.DATE.display_name
        if date_display_name in df_to_save.columns and pd.api.types.is_datetime64_any_dtype(df_to_save[date_display_name]):
            df_to_save[date_display_name] = df_to_save[date_display_name].dt.strftime("%d-%m-%y")

        df_to_save.to_csv(output_path, index=False)
        result["save_success"] = True
        result["output_path"] = output_path
        log.info(f"Successfully saved master file to {output_path}")

    except Exception as e:
        log.error(f"Failed to save master file: {e}", exc_info=True)
        result["error"] = str(e)

    return result


def update_database_from_df(
    df: pd.DataFrame,
    source_file: Optional[str] = None
) -> Optional[ImportResult]:
    """Update the database with transactions from a DataFrame.

    Args:
        df: DataFrame containing transaction data.
        source_file: Optional source file path for reference.

    Returns:
        ImportResult object or None if update fails.
    """
    try:
        if df.empty:
            log.info("Empty DataFrame provided, nothing to update.")
            return ImportResult()

        df_to_update = df.copy()
        repo = SQLiteTransactionRepository()
        db_service = DBIOService(repo=repo)

        import_result = db_service.update_database(df_to_update, source_file=source_file)

        if import_result.error_count > 0:
            log.error(
                f"Database update completed with {import_result.error_count} errors: "
                f"{import_result.errors}"
            )

        log.info(
            f"Database update complete: {import_result.added_count} added, "
            f"{import_result.duplicate_count} duplicates, "
            f"{import_result.error_count} errors."
        )
        return import_result

    except Exception as e:
        log.error(f"Critical error updating database: {e}", exc_info=True)
        return ImportResult(
            added_count=0,
            duplicate_count=0,
            error_count=len(df) if not df.empty else 0,
            errors=[f"Critical error: {e}"]
        )


def back_up_originals(
    filepaths_list: List[str],
    save_dir: Optional[str] = None,
    cleanup_source_files: bool = False,
) -> Tuple[bool, Union[str, Dict[str, Any]]]:
    """Backup original files and optionally clean them up.

    Args:
        filepaths_list: A list of filepaths to be backed up.
        save_dir: The directory to save the backup files. If None, uses config.
        cleanup_source_files: If True, deletes the original files after backup.

    Returns:
        A tuple containing a success boolean and a results dictionary or error string.
    """
    if save_dir is None:
        save_dir = config.get_value(ConfigKeys.Paths.STATEMENTS_ARCHIVE_DIR)

    os.makedirs(save_dir, exist_ok=True)
    results: Dict[str, Any] = {"successes": [], "failures": []}

    for filepath in filepaths_list:
        try:
            # Normalize paths for comparison
            src_path = os.path.abspath(os.path.normpath(filepath))
            dest_path = os.path.abspath(os.path.normpath(save_dir))
            
            if not os.path.exists(src_path):
                raise FileNotFoundError(f"Source file not found: {src_path}")

            # Check if source and destination are the same
            if os.path.dirname(src_path) == dest_path:
                results["successes"].append(filepath)
                continue

            # Copy the file
            shutil.copy2(src_path, dest_path)
            results["successes"].append(filepath)

            if cleanup_source_files:
                os.remove(src_path)

        except (FileNotFoundError, PermissionError, OSError) as e:
            log.error(f"Failed to backup {filepath}: {e}")
            results["failures"].append({filepath: str(e)})
        except Exception as e:
            log.error(f"Unexpected error while processing {filepath}: {e}", exc_info=True)
            results["failures"].append({filepath: f"Unexpected error: {str(e)}"})

    if results["failures"]:
        return False, results

    return True, results


def handle_unrecognized_files(
    unrecognized_files: List[Dict[str, Any]], processed_files: set
) -> int:
    """Moves files that were not recognized by any handler to the 'unrecognised' folder.

    Args:
        unrecognized_files: A list of dictionaries, each with a 'filepath'.
        processed_files: A set of filepaths that were successfully processed.

    Returns:
        The number of files that were successfully moved.
    """
    unrecognized_count = 0
    for item in unrecognized_files:
        filepath = item["filepath"]
        if filepath in processed_files:
            continue

        # unrecognised_files_tracker is handled in the director
        if move_to_unrecognised(filepath):
            unrecognized_count += 1
            log.info(f"Moved unrecognized file: {filepath}")
        else:
            log.error(f"Failed to move unrecognized file: {filepath}")

    if unrecognized_count > 0:
        log.warning(
            f"Moved {unrecognized_count} unrecognized files to unrecognised folder"
        )

    return unrecognized_count


def move_to_unrecognised(filepath: str) -> bool:
    """Move unrecognised file to an 'unrecognised' subfolder in the same directory.

    Args:
        filepath: Path to the file to move

    Returns:
        Boolean indicating success or failure
    """
    try:
        # Create 'unrecognised' subdirectory in the same folder as the file
        file_dir = os.path.dirname(filepath)
        unrecognised_dir = os.path.join(file_dir, 'unrecognised')
        os.makedirs(unrecognised_dir, exist_ok=True)

        filename = os.path.basename(filepath)
        new_path = os.path.join(unrecognised_dir, filename)

        # If file exists, add timestamp
        if os.path.exists(new_path):
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(filename)
            new_path = os.path.join(unrecognised_dir, f"{name}_{timestamp}{ext}")

        shutil.move(filepath, new_path)
        log.info(f"Moved unrecognised file to: {new_path}")
        return True
    except Exception as e:
        log.warning(f"Failed to move file to unrecognised folder: {e}")
        return False